import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:get/get.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

import '../../shared/core/app_config.dart';
import '../../shared/core/app_utils.dart';
import '../../shared/models/job_parameter_model.dart';
import '../../shared/widgets/banner_ad_widget.dart';
import 'job_detail/widget/app_job_card.dart';
import 'jobs_controller.dart';
import 'jobs_filter/jobs_filter_module.dart';

class JobsPage extends StatefulWidget {
  final JobParameterModel? filtros;

  const JobsPage({super.key, required this.filtros});

  @override
  _JobsPageState createState() => _JobsPageState();
}

class _JobsPageState extends State<JobsPage> {
  final controller = Modular.get<JobsController>();

  JobParameterModel? filtros;

  @override
  void initState() {
    super.initState();
    filtros = widget.filtros;
    controller.load(filtros: filtros);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Visibility(
              visible:
                  widget.filtros!.q != null && widget.filtros!.q!.isNotEmpty,
              child: Text(
                widget.filtros!.q ?? '',
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                ),
              ),
            ),
            Visibility(
              visible: widget.filtros!.q == null || widget.filtros!.q!.isEmpty,
              child: const Text(
                'Buscar Vagas',
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.white,
                ),
              ),
            ),
            Visibility(
              visible: widget.filtros!.cidade != null &&
                  widget.filtros!.cidade!.isNotEmpty,
              child: Text(
                widget.filtros!.cidade![0],
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
        actions: <Widget>[
          IconButton(
            icon: const Icon(Icons.filter_list, color: AppConfig.colorPrimary),
            onPressed: () async {
              var params = await Modular.to.pushNamed(
                JobsFilterModule.route,
                arguments: controller.facets,
              );

              if (params != null) {
                filtros = params as JobParameterModel?;
                controller.clearJobs();
                controller.load(filtros: filtros, reload: true);
              }
            },
          )
        ],
        iconTheme: const IconThemeData(color: AppConfig.colorPrimary),
        actionsIconTheme: const IconThemeData(color: AppConfig.colorPrimary),
      ),
      body: GetBuilder<JobsController>(
        init: controller,
        builder: (_) => Visibility(
          visible: controller.loading && controller.jobs.isEmpty,
          replacement: Column(
            children: <Widget>[
              Align(
                alignment: Alignment.centerLeft,
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 25, vertical: 30),
                  child: Text(
                    '${controller.totalVagas} vagas encontradas',
                    style: const TextStyle(
                      color: Colors.grey,
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
              Expanded(
                child: ListView.separated(
                  itemBuilder: (_, i) {
                    if (i < controller.jobs.length) {
                      if (i == 2 || i % 10 == 2) {
                        return Column(
                          children: <Widget>[
                            BannerAdWidget(
                              adUnitId:
                                  AppConfig.getAdmobBannerListagemVagasId(),
                              size: AdSize.fullBanner,
                            ),
                            AppJobCard(
                              job: controller.jobs[i],
                            ),
                          ],
                        );
                      }

                      return AppJobCard(
                        job: controller.jobs[i],
                      );
                    } else if (i > 1 &&
                        widget.filtros!.pag < controller.totalPaginas!) {
                      if (!controller.loading) {
                        widget.filtros!.nextPage();
                        WidgetsBinding.instance.addPostFrameCallback(
                            (_) => controller.load(filtros: filtros));
                      }
                      return const ShimmerJobPageItem();
                    }
                    return Container();
                  },
                  separatorBuilder: (_, i) {
                    return const Divider();
                  },
                  itemCount: controller.jobs.length + 1,
                ),
              )
            ],
          ),
          child: const ShimmerJobPage(),
        ),
      ),
    );
  }
}
