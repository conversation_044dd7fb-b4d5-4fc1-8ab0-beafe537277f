import 'package:flutter/material.dart';

import '../../../../../shared/widgets/app_image_network_widget.dart';

class BenefitsMoreCard extends StatelessWidget {
  const BenefitsMoreCard({
    super.key,
    required this.isNew,
    this.discountText,
    this.titlePartner,
    this.imagePath,
    this.onTap,
  });

  final bool isNew;
  final String? discountText;
  final String? titlePartner;
  final String newText = 'novo';
  final String? imagePath;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: MediaQuery.of(context).size.height * 0.3,
        width: MediaQuery.of(context).size.width * 0.4,
        margin: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6),
          color: const Color(0xFFF5F5F5),
        ),
        child: Column(
          children: [
            Expanded(
              child: Center(
                child: AppImageNetworkWidget(
                  imagePath ?? '',
                  scale: 1.5,
                ),
              ),
            ),
            Row(
              children: [
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(
                      right: 10.0,
                      left: 10.0,
                      top: 10.0,
                      bottom: 10,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          discountText ?? '',
                          style: const TextStyle(
                            color: Colors.black,
                          ),
                          textAlign: TextAlign.left,
                        ),
                        const SizedBox(
                          height: 5.0,
                        ),
                        Text(
                          titlePartner ?? '',
                          style: const TextStyle(
                            color: Color(0xFF666666),
                          ),
                          textAlign: TextAlign.left,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
