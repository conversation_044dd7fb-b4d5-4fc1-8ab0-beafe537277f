// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../shared/core/app_config.dart';
import '../../../shared/core/app_utils.dart';
import '../../../shared/models/responses/inscricoes_response_model.dart';
import '../../../shared/utils/card_default_alert.dart';
import '../../../shared/widgets/app_image_network_widget.dart';
import '../../../shared/widgets/app_usuario_card/app_usuario_card_controller.dart';
import '../../jobs/job_detail/job_detail_controller.dart';
import '../../subscription/subscription_controller.dart';
import '../home_controller.dart';
import 'tab_subscriptions_controller.dart';
import 'utils/app_job_card_subscription.dart';
import 'utils/header_aplications.dart';

class TabSubscriptionsPage extends StatefulWidget {
  const TabSubscriptionsPage({super.key});

  @override
  _TabSubscriptionsPageState createState() => _TabSubscriptionsPageState();
}

class _TabSubscriptionsPageState extends State<TabSubscriptionsPage> {
  final controller = Modular.get<TabSubscriptionsController>();
  final subscriptionController = Modular.get<SubscriptionController>();
  final appUsuarioController = Modular.get<AppUsuarioCardController>();
  final controllerJob = Modular.get<JobDetailController>();
  final controllerHome = Modular.get<HomeController>();

  List<JobModel> inscricoes = [];
  Map<int, String?> situacoes = {};
  bool loading = true;
  String? error;

  final _refreshController = RefreshController(
    initialRefresh: false,
  );

  @override
  void initState() {
    super.initState();
    _loadInscricoes();
  }

  Future<void> _loadInscricoes() async {
    try {
      if (!mounted) return;

      setState(() {
        loading = true;
      });

      await controller.load();
      List<JobModel> loadedInscricoes = controller.inscricoes!;

      if (mounted) {
        setState(() {
          inscricoes = loadedInscricoes;
          loading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          error = e.toString();
          loading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Padding(
          padding: const EdgeInsets.only(left: 16.0),
          child: Image.asset(
            'lib/assets/images/logo-empregare-splash.png',
            scale: 1.8,
          ),
        ),
        elevation: 0.5,
        backgroundColor: AppConfig.white,
        actions: <Widget>[
          Observer(
            builder: (_) => Padding(
              padding: const EdgeInsets.only(
                bottom: 6.0,
                top: 6.0,
                right: 26,
              ),
              child: ClipOval(
                child: AppImageNetworkWidget(
                  height: 45,
                  width: 45,
                  fit: BoxFit.cover,
                  appUsuarioController.foto ?? '',
                  errorImage: 'lib/assets/images/person-filled.png',
                  scale: 2,
                ),
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          const HeaderApplications(showMinhasVagas: true),
          Expanded(
            child: loading
                ? const ShimmerJobPage()
                : inscricoes.isEmpty
                    ? const Center(
                        child: CardAlertDefault(
                          title: 'Nenhuma inscrição encontrada!',
                        ),
                      )
                    : SmartRefresher(
                        header: CustomHeader(
                          builder: (_, mode) {
                            if (mode == RefreshStatus.refreshing) {
                              return const SizedBox(
                                width: 50,
                                child: SpinKitCircle(
                                    color: AppConfig.colorPrimary),
                              );
                            }

                            if (mode == RefreshStatus.canRefresh) {
                              return const Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.arrow_downward),
                                  Text('Puxe para atualizar')
                                ],
                              );
                            }

                            return const SizedBox.shrink();
                          },
                        ),
                        controller: _refreshController,
                        onRefresh: () async {
                          setState(() {
                            loading = true;
                          });
                          _refreshController.refreshCompleted();
                          Future.delayed(const Duration(milliseconds: 500), () {
                            setState(() {
                              loading = false;
                            });
                          });

                          if (controllerHome.closeScreen == true) {
                            setState(() {
                              loading = true;
                            });
                            _refreshController.refreshCompleted();
                            Future.delayed(const Duration(milliseconds: 500),
                                () {
                              setState(() {
                                loading = false;
                              });
                            });
                          }
                        },
                        child: ListView(
                          shrinkWrap: false,
                          physics: const BouncingScrollPhysics(),
                          children: inscricoes
                              .map((e) => AppJobCardSubscription(
                                    key: Key(e.candidaturaID.toString()),
                                    job: e,
                                    isSubscription: true,
                                  ))
                              .toList(),
                        ),
                      ),
          ),
        ],
      ),
    );
  }
}
