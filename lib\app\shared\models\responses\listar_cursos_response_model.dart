class CursoResponseModel {
  final bool sucesso;
  final String mensagem;
  final List<CursoModel>? dados;

  CursoResponseModel({this.sucesso = false, this.mensagem = "", this.dados});

  factory CursoResponseModel.fromJson(Map<String, dynamic> json) =>
      CursoResponseModel(
        sucesso: json['sucesso'] ?? false,
        mensagem: json['mensagem'] ?? "",
        dados: (json['dados'] as List<dynamic>?)
            ?.map((e) => CursoModel.fromJson(e as Map<String, dynamic>))
            .toList(),
      );
}

class CursoModel {
  final int? id;
  String? instituicao;
  String? nome;
  String? descricao;
  String? cargaHoraria;
  String? mesInicio;
  String? anoInicio;
  int? mesI;
  int? anoI;
  final String? periodoPorExtenso;

  CursoModel({
    this.id,
    this.instituicao,
    this.nome,
    this.descricao,
    this.cargaHoraria,
    this.mesInicio,
    this.anoInicio,
    this.mesI,
    this.anoI,
    this.periodoPorExtenso,
  });

  factory CursoModel.fromJson(Map<String, dynamic> json) => CursoModel(
        id: json['id'] as int?,
        instituicao: json['instituicao'] as String?,
        nome: json['nome'] as String?,
        descricao: json['descricao'] as String?,
        cargaHoraria: json['cargaHoraria'] as String?,
        mesInicio: json['mesInicio'] as String?,
        anoInicio: json['anoInicio'] as String?,
        mesI: json['mesI'] as int?,
        anoI: json['anoI'] as int?,
        periodoPorExtenso: json['periodoPorExtenso'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'instituicao': instituicao,
        'nome': nome,
        'descricao': descricao,
        'cargaHoraria': cargaHoraria,
        'mesInicio': mesInicio,
        'anoInicio': anoInicio,
        'mesI': mesI,
        'anoI': anoI,
        'periodoPorExtenso': periodoPorExtenso,
      };

  CursoModel copy() {
    return CursoModel(
      id: id,
      instituicao: instituicao,
      nome: nome,
      descricao: descricao,
      cargaHoraria: cargaHoraria,
      mesInicio: mesInicio,
      anoInicio: anoInicio,
      mesI: mesI,
      anoI: anoI,
      periodoPorExtenso: periodoPorExtenso,
    );
  }
}
