import 'package:intl/intl.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';

class PessoaResponseModel {
  final int? pessoaID;
  final String? id;
  String? nome;
  final bool? colaborador;
  int? dia;
  int? mes;
  int? ano;
  final String? idade;
  String? sexo;
  String? email;
  final int? status;
  final String? foto;
  final bool? possuiFoto;
  int? estadoCivilID;
  String? estadoCivil;
  String? telefone;
  String? telefonePaisCode;
  String? celular;
  String? celularPaisCode;
  String? cidade;
  int? cidadeID;
  String? paisID;
  int? estadoID;
  String? estado;
  String? cep;
  String? logradouro;
  String? bairro;
  final AprendizResponseModel? aprendiz;
  final String? miniCurriculo;
  final TipoResponseModel? tipo;
  final PrivacidadeResponseModel? privacidade;
  final bool? semExperiencia;
  final bool? semInformatica;
  final bool? semDeficiencia;
  final String? deficienciaTexto;
  final int? pretensaoSalarialID;
  final String? pretensaoSalarial;
  final int? filhosID;
  final String? filhos;

  final String? moedaSigla;
  final int? salarioConvertidoEnum;
  final int? pretensaoSalarialMin;
  final int? pretensaoSalarialMax;
  final String? separadorSalario;

  String get pretensaoSalarialFormatado {
    if (pretensaoSalarialMin == null || pretensaoSalarialMax == null) return "Não definido!";

    var data = "";

    var format = NumberFormat(
      '#,###',
      moedaSigla == 'R\$' ? 'pt_BR' : 'en_US',
    ).format;
    data += "De: $moedaSigla ${format(pretensaoSalarialMin)}${separadorSalario}00";
    data += "   ";
    data += "Até: $moedaSigla ${format(pretensaoSalarialMax)}${separadorSalario}00";

    return data;
  }

  String get nascimento => "$dia/$mes/$ano";
  bool get isBr => paisID == "BR";

  String get cepFormmater =>
      isBr ? MaskTextInputFormatter(mask: "#####-###").maskText(cep ?? '') : cep ?? '';

  PessoaResponseModel({
    this.pessoaID,
    this.id,
    this.nome,
    this.colaborador,
    this.dia,
    this.mes,
    this.ano,
    this.idade,
    this.sexo,
    this.email,
    this.status,
    this.foto,
    this.possuiFoto,
    this.estadoCivilID,
    this.estadoCivil,
    this.telefone,
    this.telefonePaisCode,
    this.celular,
    this.celularPaisCode,
    this.cidade,
    this.cidadeID,
    this.paisID,
    this.estadoID,
    this.estado,
    this.cep,
    this.logradouro,
    this.bairro,
    this.aprendiz,
    this.miniCurriculo,
    this.tipo,
    this.privacidade,
    this.semExperiencia,
    this.semInformatica,
    this.semDeficiencia,
    this.deficienciaTexto,
    this.pretensaoSalarialID,
    this.pretensaoSalarial,
    this.filhosID,
    this.filhos,
    this.moedaSigla,
    this.salarioConvertidoEnum,
    this.pretensaoSalarialMin,
    this.pretensaoSalarialMax,
    this.separadorSalario,
  });

  factory PessoaResponseModel.fromJson(Map<String, dynamic> json) {
    return PessoaResponseModel(
      pessoaID: json['pessoaID'] as int?,
      id: json['id'] as String?,
      nome: json['nome'] as String?,
      colaborador: json['colaborador'] as bool?,
      dia: json['dia'] as int?,
      mes: json['mes'] as int?,
      ano: json['ano'] as int?,
      idade: json['idade'] as String?,
      sexo: json['sexo'] as String?,
      email: json['email'] as String?,
      status: json['status'] as int?,
      foto: json['foto'] as String?,
      possuiFoto: json['possuiFoto'] as bool?,
      estadoCivilID: json['estadoCivilID'] as int?,
      estadoCivil: json['estadoCivil'] as String?,
      telefone: json['telefone'] as String?,
      telefonePaisCode: json['telefonePaisCode'] as String?,
      celular: json['celular'] as String?,
      celularPaisCode: json['celularPaisCode'] as String?,
      cidade: json['cidade'] as String?,
      cidadeID: json['cidadeID'] as int?,
      paisID: json['paisID'] as String?,
      estadoID: json['estadoID'] as int?,
      estado: json['estado'] as String?,
      cep: json['cep'] as String?,
      logradouro: json['logradouro'] as String?,
      bairro: json['bairro'] as String?,
      aprendiz: json['aprendiz'] == null
          ? null
          : AprendizResponseModel.fromJson(json['aprendiz'] as Map<String, dynamic>),
      miniCurriculo: json['miniCurriculo'] as String?,
      tipo: json['tipo'] == null
          ? null
          : TipoResponseModel.fromJson(json['tipo'] as Map<String, dynamic>),
      privacidade: json['privacidade'] == null
          ? null
          : PrivacidadeResponseModel.fromJson(json['privacidade'] as Map<String, dynamic>),
      semExperiencia: json['semExperiencia'] as bool?,
      semInformatica: json['semInformatica'] as bool?,
      semDeficiencia: json['semDeficiencia'] as bool?,
      deficienciaTexto: json['deficienciaTexto'] as String?,
      pretensaoSalarialID: json['pretensaoSalarialID'] as int?,
      pretensaoSalarial: json['pretensaoSalarial'] as String?,
      filhosID: json['filhosID'] as int?,
      filhos: json['filhos'] as String?,
      moedaSigla: json['moedaSigla'],
      salarioConvertidoEnum: json['salarioConvertidoEnum'],
      pretensaoSalarialMin: json['pretensaoSalarialMin'],
      pretensaoSalarialMax: json['pretensaoSalarialMax'],
      separadorSalario: json['separadorSalario'],
    );
  }

  PessoaResponseModel copy() {
    return PessoaResponseModel(
      pessoaID: pessoaID,
      id: id,
      nome: nome,
      colaborador: colaborador,
      dia: dia,
      mes: mes,
      ano: ano,
      idade: idade,
      sexo: sexo,
      email: email,
      status: status,
      foto: foto,
      possuiFoto: possuiFoto,
      estadoCivilID: estadoCivilID,
      estadoCivil: estadoCivil,
      telefone: telefone,
      telefonePaisCode: telefonePaisCode,
      celular: celular,
      celularPaisCode: celularPaisCode,
      cidade: cidade,
      cidadeID: cidadeID,
      paisID: paisID,
      estadoID: estadoID,
      estado: estado,
      cep: cep,
      logradouro: logradouro,
      bairro: bairro,
      aprendiz: aprendiz,
      miniCurriculo: miniCurriculo,
      tipo: tipo,
      privacidade: privacidade,
      semExperiencia: semExperiencia,
      semInformatica: semInformatica,
      semDeficiencia: semDeficiencia,
      deficienciaTexto: deficienciaTexto,
      pretensaoSalarialID: pretensaoSalarialID,
      pretensaoSalarial: pretensaoSalarial,
      filhosID: filhosID,
      filhos: filhos,
    );
  }
}

class AprendizResponseModel {
  final String? mae;
  final bool? maeDesconhecida;
  final String? pai;
  final bool? paiDesconhecido;

  AprendizResponseModel({
    this.mae,
    this.maeDesconhecida,
    this.pai,
    this.paiDesconhecido,
  });

  factory AprendizResponseModel.fromJson(Map<String, dynamic> json) {
    return AprendizResponseModel(
      mae: json['mae'] as String?,
      maeDesconhecida: json['maeDesconhecida'] as bool?,
      pai: json['pai'] as String?,
      paiDesconhecido: json['paiDesconhecido'] as bool?,
    );
  }
}

class TipoResponseModel {
  final String? nome;
  final String? descricaoCandidato;
  final String? descricaoEmpresa;
  final String? cor;
  final String? cssClass;

  TipoResponseModel({
    this.nome,
    this.descricaoCandidato,
    this.descricaoEmpresa,
    this.cor,
    this.cssClass,
  });

  factory TipoResponseModel.fromJson(Map<String, dynamic> json) {
    return TipoResponseModel(
      nome: json['nome'] as String?,
      descricaoCandidato: json['descricaoCandidato'] as String?,
      descricaoEmpresa: json['descricaoEmpresa'] as String?,
      cor: json['cor'] as String?,
      cssClass: json['cssClass'] as String?,
    );
  }
}

class PrivacidadeResponseModel {
  final int? tipo;
  final String? nome;
  final String? descricao;
  final String? icone;

  PrivacidadeResponseModel({this.tipo, this.nome, this.descricao, this.icone});

  factory PrivacidadeResponseModel.fromJson(Map<String, dynamic> json) {
    return PrivacidadeResponseModel(
      tipo: json['tipo'] as int?,
      nome: json['nome'] as String?,
      descricao: json['descricao'] as String?,
      icone: json['icone'] as String?,
    );
  }
}
