# GitHub Copilot Instructions for Empregare App

## Visão Geral do Projeto

- Aplicativo Flutter com arquitetura **Modular** (`flutter_modular`) para injeção de dependências e rotas.
- Estado gerenciado em partes do app por **MobX** (observables/ actions) e em outras camadas por **GetX** (Controllers com `LoaderMixin`).
- Controle de permissões, carregamento e navegação centralizados em `lib/app/modules/`:
  - Cada módulo tem `module.dart`, `page.dart`, `controller.dart`.
  - Ex.: `curriculum_editing` define `CurriculumEditingModule`, `CurriculumEditingPage`, `CurriculumEditingController`.

## Fluxo de Dados e Serviços

- **CurriculumController** (`lib/app/modules/curriculum/curriculum_controller.dart`) usa `LoaderMixin` para marcar carregamento e `update()` para notificar Views.
- Operações de rede e repositórios em `shared/repositories/`, retornando modelos de resposta.
- Upload de arquivos (`uploadLaudo`) via `FilePickerLib`, com limite de 5MB gerenciado em Controller.

## Padrões de Código e Convenções

- **Mixins** são usados para lógica transversa, p. ex. `LoaderMixin` define `loading` e `changeLoading()`.
- A UI pode usar `GetBuilder<Controller>(builder: ...)` para reagir a `update()` ou `Obx` para reativos GetX.
- Funções de validação e exibição de Snackbar centralizadas em Controller:
  - `showSnackbarErro(context, mensagem)`
  - `showSnackbarSucesso(context, mensagem)`

## Build e Execução

- **Instalação**: `flutter pub get`
- **Compilar APK** (Android): `sh build_apk.sh`
- **iOS**: abrir `ios/Runner.xcworkspace` no Xcode após `pod install`.
- **Web**: `flutter run -d chrome`

## Testes e Qualidade

- Testes unitários em `test/primeiro_test.dart`.
- Rodar toda suíte: `flutter test`

## Pontos de Atenção para AI Agents

1. **Modular e Injeção**: nunca alterar `Modular.get()` sem entender o Module correspondente.
2. **State Management**:
   - Migração MobX → GetX: remover `@observable/@action`, nao alterar nome de variaveis e `update()`;
   - Usar `LoaderMixin` para estado de carregamento (`changeLoading(true/false)`);
   - Em `Page`, substituir `Observer` (MobX) por `GetBuilder<Controller>`.
3. **Naming Conventions**: arquivos e classes seguem `<module>_<feature>_controller.dart` e `CamelCase`.
4. **Arquivos Gerados**: código em `build/` pode ser ignorado.

---
_Em caso de dúvidas sobre padrões específicos, consulte os arquivos em `lib/app/shared/` e os módulos existentes para referência._
