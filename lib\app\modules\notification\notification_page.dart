import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

import '../../shared/core/app_config.dart';
import '../../shared/core/app_translation.i18n.dart';
import '../../shared/core/app_utils.dart';
import '../../shared/models/responses/dash_notificacoes_response_model.dart';
import '../../shared/widgets/app_bar_default.dart';
import '../questionnaire/questionnaire_module.dart';
import '../schedule/schedule_module.dart';
import 'notification_controller.dart';

class NotificationPage extends StatefulWidget {
  const NotificationPage({super.key});

  @override
  _NotificationPageState createState() => _NotificationPageState();
}

class _NotificationPageState extends State<NotificationPage> {
  final controller = Modular.get<NotificationController>();
  final _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      appBar: AppBarDefault(titleText: 'Notificações'.i18n),
      body: Observer(
        builder: (BuildContext context) {
          return Padding(
            padding: const EdgeInsets.all(10),
            child: Visibility(
              visible: controller.loading,
              replacement: ListView.separated(
                itemBuilder: (_, index) {
                  int qLength = controller.questionarios.length;
                  if (index < qLength) {
                    QuestionarioModel questionario =
                        controller.questionarios[index];
                    return ListTile(
                      onTap: () {
                        Modular.to.pushNamed(
                          QuestionnaireModule.route,
                          arguments: questionario.id,
                        );
                      },
                      title: Text(
                        questionario.titulo!,
                        style: const TextStyle(fontSize: 16),
                      ),
                      subtitle: Text(
                        questionario.vaga!,
                        maxLines: 3,
                        style: const TextStyle(
                          fontSize: 14,
                        ),
                      ),
                      trailing: Text(
                        'RESPONDER'.i18n,
                        style: const TextStyle(
                          color: AppConfig.colorPrimary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    );
                  }

                  AgendaModel agenda = controller.agendas[index - qLength];
                  return ListTile(
                    onTap: () {
                      Modular.to.pushNamed(ScheduleModule.route,
                          arguments: agenda.token);
                    },
                    title: Text(
                      'Compromisso'.i18n,
                      style: const TextStyle(
                        fontSize: 16,
                      ),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Text(
                          removeAllHtmlTags(agenda.data),
                          style: const TextStyle(
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 5),
                        Text(
                          'Situação: '.i18n +
                              removeAllHtmlTags(
                                agenda.situacao,
                              ),
                          style: const TextStyle(
                            fontSize: 11,
                          ),
                        ),
                      ],
                    ),
                    trailing: Text(
                      'VISUALIZAR'.i18n,
                      style: const TextStyle(
                        color: AppConfig.colorPrimary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  );
                },
                separatorBuilder: (_, index) {
                  return Container(height: 1, color: AppConfig.grey);
                },
                itemCount:
                    controller.questionarios.length + controller.agendas.length,
              ),
              child: const Center(
                child: SpinKitCircle(
                  color: AppConfig.colorPrimary,
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
