// ignore_for_file: hash_and_equals

import 'dart:convert';

class ArticleResponseModel {
  final String date;
  final String dataFormatada;
  final String link;
  final Title title;
  final Excerpt excerpt;
  final Embedded embedded;
  final bool destaque;

  String get image {
    String source = "";

    for (var item in embedded.wpFeaturedmedia) {
      source = item.mediaDetails.sizes.full.sourceUrl;
      if (source.isNotEmpty) {
        break;
      }
    }

    return source;
  }

  ArticleResponseModel({
    required this.date,
    required this.dataFormatada,
    required this.link,
    required this.title,
    required this.excerpt,
    required this.embedded,
    required this.destaque,
  });

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    result.addAll({'date': date});
    result.addAll({'dataFormatada': dataFormatada});
    result.addAll({'link': link});
    result.addAll({'title': title.toMap()});
    result.addAll({'excerpt': excerpt.toMap()});
    result.addAll({'_embedded': embedded.toMap()});
    result.addAll({'destaque': destaque});

    return result;
  }

  factory ArticleResponseModel.fromMap(Map<String, dynamic> map) {
    return ArticleResponseModel(
      date: map['date'] ?? '',
      dataFormatada: map['dataFormatada'] ?? '',
      link: map['link'] ?? '',
      title: Title.fromMap(map['title']),
      excerpt: Excerpt.fromMap(map['excerpt']),
      embedded: Embedded.fromMap(map['_embedded']),
      destaque: map['destaque'] ?? false,
    );
  }

  String toJson() => json.encode(toMap());

  factory ArticleResponseModel.fromJson(String source) =>
      ArticleResponseModel.fromMap(json.decode(source));
}

class Title {
  final String rendered;
  Title({
    required this.rendered,
  });

  Title copyWith({
    String? rendered,
  }) {
    return Title(
      rendered: rendered ?? this.rendered,
    );
  }

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    result.addAll({'rendered': rendered});

    return result;
  }

  factory Title.fromMap(Map<String, dynamic> map) {
    return Title(
      rendered: map['rendered'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory Title.fromJson(String source) => Title.fromMap(json.decode(source));

  @override
  String toString() => 'Title(rendered: $rendered)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Title && other.rendered == rendered;
  }

  @override
  int get hashCode => rendered.hashCode;
}

class Excerpt {
  final String rendered;
  final bool protected;
  Excerpt({
    required this.rendered,
    required this.protected,
  });

  Excerpt copyWith({
    String? rendered,
    bool? protected,
  }) {
    return Excerpt(
      rendered: rendered ?? this.rendered,
      protected: protected ?? this.protected,
    );
  }

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    result.addAll({'rendered': rendered});
    result.addAll({'protected': protected});

    return result;
  }

  factory Excerpt.fromMap(Map<String, dynamic> map) {
    return Excerpt(
      rendered: map['rendered'] ?? '',
      protected: map['protected'] ?? false,
    );
  }

  String toJson() => json.encode(toMap());

  factory Excerpt.fromJson(String source) =>
      Excerpt.fromMap(json.decode(source));

  @override
  String toString() => 'Excerpt(rendered: $rendered, protected: $protected)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Excerpt &&
        other.rendered == rendered &&
        other.protected == protected;
  }

  @override
  int get hashCode => rendered.hashCode ^ protected.hashCode;
}

class Embedded {
  final List<WpFeaturedmedia> wpFeaturedmedia;
  Embedded({
    required this.wpFeaturedmedia,
  });

  Embedded copyWith({
    List<WpFeaturedmedia>? wpFeaturedmedia,
  }) {
    return Embedded(
      wpFeaturedmedia: wpFeaturedmedia ?? this.wpFeaturedmedia,
    );
  }

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    result.addAll(
        {'wp:featuredmedia': wpFeaturedmedia.map((x) => x.toMap()).toList()});

    return result;
  }

  factory Embedded.fromMap(Map<String, dynamic> map) {
    return Embedded(
      wpFeaturedmedia: List<WpFeaturedmedia>.from(
          map['wp:featuredmedia']?.map((x) => WpFeaturedmedia.fromMap(x))),
    );
  }

  String toJson() => json.encode(toMap());

  factory Embedded.fromJson(String source) =>
      Embedded.fromMap(json.decode(source));

  @override
  String toString() => '_embedded(wpFeaturedmedia: $wpFeaturedmedia)';

  @override
  int get hashCode => wpFeaturedmedia.hashCode;
}

class WpFeaturedmedia {
  final MediaDetails mediaDetails;
  WpFeaturedmedia({
    required this.mediaDetails,
  });

  WpFeaturedmedia copyWith({
    MediaDetails? mediaDetails,
  }) {
    return WpFeaturedmedia(
      mediaDetails: mediaDetails ?? this.mediaDetails,
    );
  }

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    result.addAll({'media_details': mediaDetails.toMap()});

    return result;
  }

  factory WpFeaturedmedia.fromMap(Map<String, dynamic> map) {
    return WpFeaturedmedia(
      mediaDetails: MediaDetails.fromMap(map['media_details']),
    );
  }

  String toJson() => json.encode(toMap());

  factory WpFeaturedmedia.fromJson(String source) =>
      WpFeaturedmedia.fromMap(json.decode(source));

  @override
  String toString() => 'Wp:featuredmedia(media_details: $mediaDetails)';

  @override
  int get hashCode => mediaDetails.hashCode;
}

class MediaDetails {
  final Sizes sizes;
  MediaDetails({
    required this.sizes,
  });

  MediaDetails copyWith({
    Sizes? sizes,
  }) {
    return MediaDetails(
      sizes: sizes ?? this.sizes,
    );
  }

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    result.addAll({'sizes': sizes.toMap()});

    return result;
  }

  factory MediaDetails.fromMap(Map<String, dynamic> map) {
    return MediaDetails(
      sizes: Sizes.fromMap(map['sizes']),
    );
  }

  String toJson() => json.encode(toMap());

  factory MediaDetails.fromJson(String source) =>
      MediaDetails.fromMap(json.decode(source));

  @override
  String toString() => 'Media_details(sizes: $sizes)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is MediaDetails && other.sizes == sizes;
  }

  @override
  int get hashCode => sizes.hashCode;
}

class Sizes {
  final Medium medium;
  final Large large;
  final Thumbnail thumbnail;
  final Medium_large mediumLarge;
  final MdBoneXxxl mdBoneXxxl;
  final MdBoneXxl mdBoneXxl;
  final MdBoneXl mdBoneXl;
  final MdBoneLg mdBoneLg;
  final MdBoneMd mdBoneMd;
  final MdBoneSm mdBoneSm;
  final MdBoneXs mdBoneXs;
  final Full full;
  Sizes({
    required this.medium,
    required this.large,
    required this.thumbnail,
    required this.mediumLarge,
    required this.mdBoneXxxl,
    required this.mdBoneXxl,
    required this.mdBoneXl,
    required this.mdBoneLg,
    required this.mdBoneMd,
    required this.mdBoneSm,
    required this.mdBoneXs,
    required this.full,
  });

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    result.addAll({'medium': medium.toMap()});
    result.addAll({'large': large.toMap()});
    result.addAll({'thumbnail': thumbnail.toMap()});
    result.addAll({'medium_large': mediumLarge.toMap()});
    result.addAll({'md_bone_xxxl': mdBoneXxxl.toMap()});
    result.addAll({'md_bone_xxl': mdBoneXxl.toMap()});
    result.addAll({'md_bone_xl': mdBoneXl.toMap()});
    result.addAll({'md_bone_lg': mdBoneLg.toMap()});
    result.addAll({'md_bone_md': mdBoneMd.toMap()});
    result.addAll({'md_bone_sm': mdBoneSm.toMap()});
    result.addAll({'md_bone_xs': mdBoneXs.toMap()});
    result.addAll({'full': full.toMap()});

    return result;
  }

  factory Sizes.fromMap(Map<String, dynamic> map) {
    return Sizes(
      medium: Medium.fromMap(map['medium'] ?? {}),
      large: Large.fromMap(map['large'] ?? {}),
      thumbnail: Thumbnail.fromMap(map['thumbnail'] ?? {}),
      mediumLarge: Medium_large.fromMap(map['medium_large'] ?? {}),
      mdBoneXxxl: MdBoneXxxl.fromMap(map['md_bone_xxxl'] ?? {}),
      mdBoneXxl: MdBoneXxl.fromMap(map['md_bone_xxl'] ?? {}),
      mdBoneXl: MdBoneXl.fromMap(map['md_bone_xl'] ?? {}),
      mdBoneLg: MdBoneLg.fromMap(map['md_bone_lg'] ?? {}),
      mdBoneMd: MdBoneMd.fromMap(map['md_bone_md'] ?? {}),
      mdBoneSm: MdBoneSm.fromMap(map['md_bone_sm'] ?? {}),
      mdBoneXs: MdBoneXs.fromMap(map['md_bone_xs'] ?? {}),
      full: Full.fromMap(map['full'] ?? {}),
    );
  }

  String toJson() => json.encode(toMap());

  factory Sizes.fromJson(String source) => Sizes.fromMap(json.decode(source));
}

class Medium {
  final String sourceUrl;
  Medium({
    required this.sourceUrl,
  });

  Medium copyWith({
    String? sourceUrl,
  }) {
    return Medium(
      sourceUrl: sourceUrl ?? this.sourceUrl,
    );
  }

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    result.addAll({'source_url': sourceUrl});

    return result;
  }

  factory Medium.fromMap(Map<String, dynamic> map) {
    return Medium(
      sourceUrl: map['source_url'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory Medium.fromJson(String source) => Medium.fromMap(json.decode(source));

  @override
  String toString() => 'Medium(source_url: $sourceUrl)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Medium && other.sourceUrl == sourceUrl;
  }

  @override
  int get hashCode => sourceUrl.hashCode;
}

class Large {
  final String sourceUrl;
  Large({
    required this.sourceUrl,
  });

  Large copyWith({
    String? sourceUrl,
  }) {
    return Large(
      sourceUrl: sourceUrl ?? this.sourceUrl,
    );
  }

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    result.addAll({'source_url': sourceUrl});

    return result;
  }

  factory Large.fromMap(Map<String, dynamic> map) {
    return Large(
      sourceUrl: map['source_url'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory Large.fromJson(String source) => Large.fromMap(json.decode(source));

  @override
  String toString() => 'Large(source_url: $sourceUrl)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Large && other.sourceUrl == sourceUrl;
  }

  @override
  int get hashCode => sourceUrl.hashCode;
}

class Thumbnail {
  final String sourceUrl;
  Thumbnail({
    required this.sourceUrl,
  });

  Thumbnail copyWith({
    String? sourceUrl,
  }) {
    return Thumbnail(
      sourceUrl: sourceUrl ?? this.sourceUrl,
    );
  }

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    result.addAll({'source_url': sourceUrl});

    return result;
  }

  factory Thumbnail.fromMap(Map<String, dynamic> map) {
    return Thumbnail(
      sourceUrl: map['source_url'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory Thumbnail.fromJson(String source) =>
      Thumbnail.fromMap(json.decode(source));

  @override
  String toString() => 'Thumbnail(source_url: $sourceUrl)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Thumbnail && other.sourceUrl == sourceUrl;
  }

  @override
  int get hashCode => sourceUrl.hashCode;
}

// ignore: camel_case_types
class Medium_large {
  final String sourceUrl;
  Medium_large({
    required this.sourceUrl,
  });

  Medium_large copyWith({
    String? sourceUrl,
  }) {
    return Medium_large(
      sourceUrl: sourceUrl ?? this.sourceUrl,
    );
  }

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    result.addAll({'source_url': sourceUrl});

    return result;
  }

  factory Medium_large.fromMap(Map<String, dynamic> map) {
    return Medium_large(
      sourceUrl: map['source_url'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory Medium_large.fromJson(String source) =>
      Medium_large.fromMap(json.decode(source));

  @override
  String toString() => 'Medium_large(source_url: $sourceUrl)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Medium_large && other.sourceUrl == sourceUrl;
  }

  @override
  int get hashCode => sourceUrl.hashCode;
}

class MdBoneXxxl {
  final String sourceUrl;
  MdBoneXxxl({
    required this.sourceUrl,
  });

  MdBoneXxxl copyWith({
    String? sourceUrl,
  }) {
    return MdBoneXxxl(
      sourceUrl: sourceUrl ?? this.sourceUrl,
    );
  }

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    result.addAll({'source_url': sourceUrl});

    return result;
  }

  factory MdBoneXxxl.fromMap(Map<String, dynamic> map) {
    return MdBoneXxxl(
      sourceUrl: map['source_url'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory MdBoneXxxl.fromJson(String source) =>
      MdBoneXxxl.fromMap(json.decode(source));

  @override
  String toString() => 'Md_bone_xxxl(source_url: $sourceUrl)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is MdBoneXxxl && other.sourceUrl == sourceUrl;
  }

  @override
  int get hashCode => sourceUrl.hashCode;
}

class MdBoneXxl {
  final String sourceUrl;
  MdBoneXxl({
    required this.sourceUrl,
  });

  MdBoneXxl copyWith({
    String? sourceUrl,
  }) {
    return MdBoneXxl(
      sourceUrl: sourceUrl ?? this.sourceUrl,
    );
  }

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    result.addAll({'source_url': sourceUrl});

    return result;
  }

  factory MdBoneXxl.fromMap(Map<String, dynamic> map) {
    return MdBoneXxl(
      sourceUrl: map['source_url'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory MdBoneXxl.fromJson(String source) =>
      MdBoneXxl.fromMap(json.decode(source));

  @override
  String toString() => 'Md_bone_xxl(source_url: $sourceUrl)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is MdBoneXxl && other.sourceUrl == sourceUrl;
  }

  @override
  int get hashCode => sourceUrl.hashCode;
}

class MdBoneXl {
  final String sourceUrl;
  MdBoneXl({
    required this.sourceUrl,
  });

  MdBoneXl copyWith({
    String? sourceUrl,
  }) {
    return MdBoneXl(
      sourceUrl: sourceUrl ?? this.sourceUrl,
    );
  }

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    result.addAll({'source_url': sourceUrl});

    return result;
  }

  factory MdBoneXl.fromMap(Map<String, dynamic> map) {
    return MdBoneXl(
      sourceUrl: map['source_url'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory MdBoneXl.fromJson(String source) =>
      MdBoneXl.fromMap(json.decode(source));

  @override
  String toString() => 'Md_bone_xl(source_url: $sourceUrl)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is MdBoneXl && other.sourceUrl == sourceUrl;
  }

  @override
  int get hashCode => sourceUrl.hashCode;
}

class MdBoneLg {
  final String sourceUrl;
  MdBoneLg({
    required this.sourceUrl,
  });

  MdBoneLg copyWith({
    String? sourceUrl,
  }) {
    return MdBoneLg(
      sourceUrl: sourceUrl ?? this.sourceUrl,
    );
  }

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    result.addAll({'source_url': sourceUrl});

    return result;
  }

  factory MdBoneLg.fromMap(Map<String, dynamic> map) {
    return MdBoneLg(
      sourceUrl: map['source_url'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory MdBoneLg.fromJson(String source) =>
      MdBoneLg.fromMap(json.decode(source));

  @override
  String toString() => 'Md_bone_lg(source_url: $sourceUrl)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is MdBoneLg && other.sourceUrl == sourceUrl;
  }

  @override
  int get hashCode => sourceUrl.hashCode;
}

class MdBoneMd {
  final String sourceUrl;
  MdBoneMd({
    required this.sourceUrl,
  });

  MdBoneMd copyWith({
    String? sourceUrl,
  }) {
    return MdBoneMd(
      sourceUrl: sourceUrl ?? this.sourceUrl,
    );
  }

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    result.addAll({'source_url': sourceUrl});

    return result;
  }

  factory MdBoneMd.fromMap(Map<String, dynamic> map) {
    return MdBoneMd(
      sourceUrl: map['source_url'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory MdBoneMd.fromJson(String source) =>
      MdBoneMd.fromMap(json.decode(source));

  @override
  String toString() => 'Md_bone_md(source_url: $sourceUrl)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is MdBoneMd && other.sourceUrl == sourceUrl;
  }

  @override
  int get hashCode => sourceUrl.hashCode;
}

class MdBoneSm {
  final String sourceUrl;
  MdBoneSm({
    required this.sourceUrl,
  });

  MdBoneSm copyWith({
    String? sourceUrl,
  }) {
    return MdBoneSm(
      sourceUrl: sourceUrl ?? this.sourceUrl,
    );
  }

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    result.addAll({'source_url': sourceUrl});

    return result;
  }

  factory MdBoneSm.fromMap(Map<String, dynamic> map) {
    return MdBoneSm(
      sourceUrl: map['source_url'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory MdBoneSm.fromJson(String source) =>
      MdBoneSm.fromMap(json.decode(source));

  @override
  String toString() => 'Md_bone_sm(source_url: $sourceUrl)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is MdBoneSm && other.sourceUrl == sourceUrl;
  }

  @override
  int get hashCode => sourceUrl.hashCode;
}

class MdBoneXs {
  final String sourceUrl;
  MdBoneXs({
    required this.sourceUrl,
  });

  MdBoneXs copyWith({
    String? sourceUrl,
  }) {
    return MdBoneXs(
      sourceUrl: sourceUrl ?? this.sourceUrl,
    );
  }

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    result.addAll({'source_url': sourceUrl});

    return result;
  }

  factory MdBoneXs.fromMap(Map<String, dynamic> map) {
    return MdBoneXs(
      sourceUrl: map['source_url'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory MdBoneXs.fromJson(String source) =>
      MdBoneXs.fromMap(json.decode(source));

  @override
  String toString() => 'Md_bone_xs(source_url: $sourceUrl)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is MdBoneXs && other.sourceUrl == sourceUrl;
  }

  @override
  int get hashCode => sourceUrl.hashCode;
}

class Full {
  final String sourceUrl;
  Full({
    required this.sourceUrl,
  });

  Full copyWith({
    String? sourceUrl,
  }) {
    return Full(
      sourceUrl: sourceUrl ?? this.sourceUrl,
    );
  }

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    result.addAll({'source_url': sourceUrl});

    return result;
  }

  factory Full.fromMap(Map<String, dynamic> map) {
    return Full(
      sourceUrl: map['source_url'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory Full.fromJson(String source) => Full.fromMap(json.decode(source));

  @override
  String toString() => 'Full(source_url: $sourceUrl)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Full && other.sourceUrl == sourceUrl;
  }

  @override
  int get hashCode => sourceUrl.hashCode;
}
