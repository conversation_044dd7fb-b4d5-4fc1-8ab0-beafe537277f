class LoginModel {
  final String? usuario;
  final String? senha;
  final String? returnUrl;
  final String? urlBase;
  final String? pessoaID;
  final String? empresaID;
  final String? vinculo;
  final String? ip;
  final String? navegador;

  LoginModel({
    this.usuario,
    this.senha,
    this.returnUrl,
    this.urlBase,
    this.pessoaID,
    this.empresaID,
    this.vinculo,
    this.ip,
    this.navegador,
  });

  Map<String, dynamic> to<PERSON>son() {
    return {
      'usuario': usuario,
      'senha': senha,
      'returnUrl': returnUrl,
      'urlBase': urlBase,
      'pessoaID': pessoaID,
      'empresaID': empresaID,
      'vinculo': vinculo,
      'ip': ip,
      'navegador': navegador,
    };
  }
}
