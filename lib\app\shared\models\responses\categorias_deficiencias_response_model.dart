class CategoriaDeficienciaResponseModel {
  final bool sucesso;
  final String mensagem;
  final List<CategoriaDeficienciaModel>? dados;

  CategoriaDeficienciaResponseModel({
    this.sucesso = false,
    this.mensagem = "",
    this.dados,
  });

  factory CategoriaDeficienciaResponseModel.fromJson(
      Map<String, dynamic> json) {
    return CategoriaDeficienciaResponseModel(
      sucesso: json['sucesso'] ?? false,
      mensagem: json['mensagem'] ?? "",
      dados: (json['dados'] as List<dynamic>?)
          ?.map((e) =>
              CategoriaDeficienciaModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class CategoriaDeficienciaModel {
  final int? id;
  final int? tipo;
  final String? nome;

  CategoriaDeficienciaModel({this.id, this.tipo, this.nome});

  factory CategoriaDeficienciaModel.fromJson(Map<String, dynamic> json) {
    return CategoriaDeficienciaModel(
      id: json['id'] as int?,
      tipo: json['tipo'] as int?,
      nome: json['nome'] as String?,
    );
  }

  @override
  String toString() => nome!;

  @override
  operator ==(other) => other is CategoriaDeficienciaModel && other.id == id;

  @override
  int get hashCode => id.hashCode ^ nome.hashCode;
}
