import 'package:flutter_modular/flutter_modular.dart';

import 'recommendations_controller.dart';
import 'recommendations_page.dart';

class RecommendationsModule extends Module {
  static const route = '/recommendations';

  @override
  void binds(i) {
    i.addLazySingleton(RecommendationsController.new);
  }

  @override
  void routes(r) {
    r.child(
      Modular.initialRoute,
      child: (context) => const RecommendationsPage(),
    );
  }
}
