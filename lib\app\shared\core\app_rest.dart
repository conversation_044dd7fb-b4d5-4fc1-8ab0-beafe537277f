import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../models/session_model.dart';
import '../services/session_service.dart';
import 'app_config.dart';

class AppRest extends DioForNative {
  AppRest() : super(BaseOptions()) {
    interceptors.add(CustomInterceptors());
    options.baseUrl = AppConfig.apiUrl;
    options.connectTimeout = const Duration(
      milliseconds: AppConfig.connectionTimeout,
    );
    options.receiveTimeout = const Duration();
    options.sendTimeout = const Duration(
      milliseconds: 30000,
    );

    options.contentType ??= Headers.jsonContentType;
  }
}

class CustomInterceptors extends InterceptorsWrapper {
  final SessionService _sessionService = Modular.get();

  @override
  Future onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    SessionModel? session = await _sessionService.get();

    if ((await _sessionService.isLoggedIn()) &&
        !options.extra.containsKey("no-auth")) {
      options.headers["Authorization"] = "Bearer ${session?.token}";
    }

    return super.onRequest(options, handler);
  }
}
