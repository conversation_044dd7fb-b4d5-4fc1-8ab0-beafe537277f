import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../shared/core/app_config.dart';
import '../../../shared/core/app_translation.i18n.dart';
import '../../../shared/models/responses/job_sugestoes_response_model.dart';
import '../../../shared/widgets/app_dropdown_search.dart';
import '../../../shared/widgets/app_text_form_field.dart';
import '../../home/<USER>/tab_jobs_controller.dart';
import 'jobs_filter_controller.dart';
import 'widgets/custom_switch.dart';
import 'widgets/facet_builder.dart';

class JobsFilterPage extends StatefulWidget {
  const JobsFilterPage({super.key});

  @override
  _JobsFilterPageState createState() => _JobsFilterPageState();
}

class _JobsFilterPageState extends State<JobsFilterPage> {
  final controller = Modular.get<JobsFilterController>();
  final tabJobsController = Modular.get<TabJobsController>();
  final facets = Modular.get<TabJobsController>().facets;

  @override
  void initState() {
    super.initState();

    controller.setPcD(
      facets.pcd.firstWhereOrNull((el) => el.id == 'true')?.isSelected ??
          tabJobsController.isPcd,
    );
    controller.setAsCegas(
      facets.selecaoCega
              .firstWhereOrNull((el) => el.id == 'true')
              ?.isSelected ??
          tabJobsController.isAsCegas,
    );

    for (var uf in tabJobsController.filtros.uf ?? []) {
      facets.uf.firstWhereOrNull((e) => e.nome == uf)?.setSelected(true);
    }

    for (var pais in tabJobsController.filtros.pais ?? []) {
      facets.pais.firstWhereOrNull((e) => e.nome == pais)?.setSelected(true);
    }

    for (var city in tabJobsController.filtros.cidade ?? []) {
      facets.cidade.firstWhereOrNull((e) => e.nome == city)?.setSelected(true);
    }

    for (var level in tabJobsController.filtros.nivel ?? []) {
      facets.nivel.firstWhereOrNull((e) => e.nome == level)?.setSelected(true);
    }

    for (var regime in tabJobsController.filtros.regime ?? []) {
      facets.regime
          .firstWhereOrNull((e) => e.nome == regime)
          ?.setSelected(true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          title: Text(
            'Refinar busca por vaga'.i18n,
            style: const TextStyle(color: Colors.black),
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.close),
              onPressed: () => Modular.to.pop(),
            ),
          ],
        ),
        body: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              FacetBuilder(
                titulo: 'Termo de busca'.i18n,
                facet: const [],
                child: Column(
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        const Text(
                          'Cargo',
                          style: TextStyle(
                            fontSize: 14,
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w400,
                            color: Color(0xff161519),
                          ),
                        ),
                        const SizedBox(height: 10),
                        AppTextFormField(
                          initialText:
                              Modular.get<TabJobsController>().filtros.q ??
                                  controller.parameters.query,
                          onChanged: (value) {
                            controller.parameters.q = value;
                            controller.parameters.query = value;
                          },
                          hintText: 'Digite o cargo ou código da vaga',
                          radius: 6,
                          textInputAction: TextInputAction.done,
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        const Text(
                          'Localização',
                          style: TextStyle(
                            fontSize: 14,
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w400,
                            color: Color(0xff161519),
                          ),
                        ),
                        const SizedBox(height: 5),
                        DropdownSearch(
                          context,
                          hintText: 'Escolha a localização',
                          title: 'Escolha a localização'.i18n,
                          items: facets.cidade
                              .map((e) => DropdownSearchItem(
                                    value: e.id,
                                    searchKey: e.nome,
                                    text: e.nome,
                                    child: Text(e.nome),
                                  ))
                              .toList(),
                          onSelected: (dynamic item) {
                            if (item != null) {
                              facets.cidade
                                  .firstWhereOrNull((e) => e.nome == item)
                                  ?.setSelected(true);
                            }
                          },
                          value: facets.cidade
                              .firstWhereOrNull((e) => e.isSelected ?? false)
                              ?.nome,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12, child: Divider()),
              FacetBuilder(
                titulo: 'País'.i18n,
                facet: facets.pais,
              ),
              const SizedBox(height: 12, child: Divider()),
              FacetBuilder(
                titulo: 'Localidade'.i18n,
                itemsPerList: 2,
                facet: (facets.cidade),
              ),
              const SizedBox(height: 12, child: Divider()),
              FacetBuilder(
                titulo: 'Estados'.i18n,
                itemsPerList: 4,
                facet: facets.uf,
              ),
              const SizedBox(height: 12, child: Divider()),
              FacetBuilder(
                titulo: 'Nível'.i18n,
                itemsPerList: 2,
                facet: facets.nivel,
              ),
              const SizedBox(height: 12, child: Divider()),
              FacetBuilder(
                titulo: 'Regime de Contratação'.i18n,
                itemsPerList: 2,
                facet: facets.regime,
              ),
              const SizedBox(height: 12, child: Divider()),
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 30,
                  vertical: 10,
                ),
                child: InkWell(
                  child: Column(
                    children: <Widget>[
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: <Widget>[
                          Text(
                            'Seleção às Cegas'.i18n,
                            style: const TextStyle(
                              fontWeight: FontWeight.w500,
                              fontSize: 18,
                            ),
                          ),
                          Observer(
                            builder: (context) {
                              return CustomSwitch(
                                value: controller.isAsCegas ?? false,
                                onChanged: (value) {
                                  for (var el in facets.selecaoCega) {
                                    el.setSelected(el.id == value.toString());
                                  }

                                  controller.setAsCegas(value);
                                },
                              );
                            },
                          ),
                        ],
                      ),
                      const SizedBox(height: 25, child: Divider()),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: <Widget>[
                          Text(
                            'Somente PcD'.i18n,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Observer(
                            builder: (context) {
                              return CustomSwitch(
                                value: controller.isPcD ?? false,
                                onChanged: (value) {
                                  for (var el in facets.pcd) {
                                    el.setSelected(el.id == value.toString());
                                  }
                                  controller.setPcD(value);
                                },
                              );
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
        bottomNavigationBar: Container(
          padding: const EdgeInsets.symmetric(vertical: 7, horizontal: 15),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                offset: const Offset(0.0, -7.0),
                blurRadius: 10.0,
                spreadRadius: 0.0,
              )
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                child: const Text(
                  "Redefinir",
                  style: TextStyle(fontFamily: 'Inter'),
                ),
                onPressed: () {
                  controller.setParameters(FacetsModel.empty());
                  Modular.to.pop(controller.parameters);
                },
              ),
              SizedBox(
                height: 45,
                width: 170,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppConfig.colorPrimary,
                  ),
                  onPressed: () {
                    controller.setParameters(facets);
                    Modular.to.pop(controller.parameters);
                  },
                  child: Text(
                    'Exibir resultados'.i18n,
                    style: const TextStyle(
                      fontFamily: 'Inter',
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
