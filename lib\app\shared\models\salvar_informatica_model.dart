class SalvarInformaticaModel {
  final List<String?>? avancado;
  final List<String?>? intermediario;
  final List<String?>? basico;

  SalvarInformaticaModel({this.avancado, this.intermediario, this.basico});

  Map<String, dynamic> toJson() {
    final result = <String, dynamic>{};

    result.addAll({'avancado': avancado});
    result.addAll({'intermediario': intermediario});
    result.addAll({'basico': basico});

    return result;
  }
}
