import 'package:flutter/material.dart';

class Secao extends StatelessWidget {
  const Secao({super.key, this.fields});

  final Widget? fields;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      color: Colors.white,
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(
              left: 15,
              right: 15,
              top: 8,
              bottom: 78,
            ),
            child: fields,
          ),
        ],
      ),
    );
  }
}
