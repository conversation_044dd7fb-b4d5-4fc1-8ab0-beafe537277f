class CadastrarModel {
  final String? paisID;
  final String? nome;
  final String? cpf;
  final int? dia;
  final int? mes;
  final int? ano;
  final String? sexo;
  final int? cidadeID;
  final String? email;
  final String? telefone;
  final String? telefonePaisCode;
  final String? celular;
  final String? celularPaisCode;
  final String? senha;
  final String? dispositivo;

  CadastrarModel({
    this.paisID,
    this.nome,
    this.cpf,
    this.dia,
    this.mes,
    this.ano,
    this.sexo,
    this.cidadeID,
    this.email,
    this.telefone,
    this.telefonePaisCode,
    this.celular,
    this.celularPaisCode,
    this.senha,
    this.dispositivo,
  });

  Map<String, dynamic> toJson() {
    return {
      'paisID': paisID,
      'nome': nome,
      'cpf': cpf,
      'dia': dia,
      'mes': mes,
      'ano': ano,
      'sexo': sexo,
      'cidadeID': cidadeID,
      'email': email,
      'telefone': telefone,
      'telefonePaisCode': telefonePaisCode,
      'celular': celular,
      'celularPaisCode': celularPaisCode,
      'senha': senha,
      'dispositivo': dispositivo,
    };
  }
}
