import 'package:flutter/material.dart';
import 'package:flutter_holo_date_picker/flutter_holo_date_picker.dart';
import 'package:intl/intl.dart';

import '../../../../../../shared/widgets/app_text_form_field.dart';

class ProfileDateSelect extends StatefulWidget {
  const ProfileDateSelect({
    super.key,
    required this.dateNotifier,
    required this.focusNode,
    this.hintTextDay = 'Dia',
    this.hintTextMonth = 'Mês',
    this.hintTextYear = 'Ano',
    this.validatorDay,
    this.validatorMonth,
    this.validatorYear,
  });

  final ValueNotifier<DateTime?> dateNotifier;
  final FocusNode focusNode;
  final String hintTextDay;
  final String hintTextMonth;
  final String hintTextYear;
  final String? Function(String?)? validatorDay;
  final String? Function(String?)? validatorMonth;
  final String? Function(String?)? validatorYear;

  @override
  _DateSelectState createState() => _DateSelectState();
}

class _DateSelectState extends State<ProfileDateSelect> {
  late TextEditingController dayController;
  late TextEditingController monthController;
  late TextEditingController yearController;
  final DateFormat monthFormat = DateFormat.MMMM('pt_BR');

  @override
  void initState() {
    super.initState();
    dayController = TextEditingController();
    monthController = TextEditingController();
    yearController = TextEditingController();

    widget.focusNode.addListener(_handleFocusChange);
    widget.dateNotifier.addListener(_updateControllers);
    _initializeControllers();
  }

  void _initializeControllers() {
    final date = widget.dateNotifier.value;
    if (date != null) {
      dayController.text = date.day.toString();
      monthController.text = monthFormat.format(date);
      yearController.text = date.year.toString();
    }
  }

  void _updateControllers() {
    final date = widget.dateNotifier.value;
    if (date != null) {
      setState(() {
        dayController.text = date.day.toString();
        monthController.text = monthFormat.format(date);
        yearController.text = date.year.toString();
      });
    }
  }

  @override
  void dispose() {
    widget.focusNode.removeListener(_handleFocusChange);
    widget.dateNotifier.removeListener(_updateControllers);
    dayController.dispose();
    monthController.dispose();
    yearController.dispose();
    super.dispose();
  }

  void _handleFocusChange() {
    if (widget.focusNode.hasFocus) {
      _selectDate();
    }
  }

  Future<DateTime?> selectDate(
    BuildContext context,
    String title,
    DateTime? currentDate, {
    String format = 'dd-MMMM-yyyy',
  }) async {
    final DateTime now = DateTime.now();
    final DateTime hundredYears = now.subtract(const Duration(days: 36500));

    var datePicked = await DatePicker.showSimpleDatePicker(
      context,
      titleText: title,
      confirmText: 'Confirmar',
      cancelText: 'Cancelar',
      initialDate: currentDate ?? now,
      firstDate: hundredYears,
      lastDate: now.add(const Duration(days: (368 * 8))),
      dateFormat: format,
      locale: DateTimePickerLocale.pt_br,
    );
    return datePicked;
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await selectDate(
      context,
      'Selecione a data',
      widget.dateNotifier.value,
    );

    if (picked != null) {
      widget.dateNotifier.value = picked;
    }

    widget.focusNode.unfocus();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          flex: 1,
          child: AppTextFormField(
            hintText: widget.hintTextDay,
            suffixIcon: Padding(
              padding: const EdgeInsets.only(right: 2.0),
              child: InkWell(
                onTap: _selectDate,
                child: const Icon(
                  Icons.keyboard_arrow_down_outlined,
                  color: Colors.black,
                ),
              ),
            ),
            readOnly: true,
            controller: dayController,
            textInputAction: TextInputAction.next,
            keyboardType: TextInputType.datetime,
            enabled: true,
            isPassword: false,
            radius: 6,
            validator: widget.validatorDay ??
                (value) {
                  if (value!.isEmpty) return 'Campo obrigatório';
                  return null;
                },
            focusNode: widget.focusNode,
          ),
        ),
        const SizedBox(width: 7),
        Expanded(
          flex: 2,
          child: AppTextFormField(
            hintText: widget.hintTextMonth,
            suffixIcon: Padding(
              padding: const EdgeInsets.only(right: 16.0),
              child: InkWell(
                onTap: _selectDate,
                child: const Icon(
                  Icons.keyboard_arrow_down_outlined,
                  color: Colors.black,
                ),
              ),
            ),
            readOnly: true,
            controller: monthController,
            textInputAction: TextInputAction.next,
            keyboardType: TextInputType.text,
            enabled: true,
            isPassword: false,
            radius: 6,
            validator: widget.validatorMonth ??
                (value) {
                  if (value!.isEmpty) return 'Campo obrigatório';
                  return null;
                },
            focusNode: widget.focusNode,
          ),
        ),
        const SizedBox(width: 7),
        Expanded(
          flex: 2,
          child: AppTextFormField(
            hintText: widget.hintTextYear,
            suffixIcon: Padding(
              padding: const EdgeInsets.only(right: 16.0),
              child: InkWell(
                onTap: _selectDate,
                child: const Icon(
                  Icons.keyboard_arrow_down_outlined,
                  color: Colors.black,
                ),
              ),
            ),
            readOnly: true,
            controller: yearController,
            textInputAction: TextInputAction.next,
            keyboardType: TextInputType.datetime,
            enabled: true,
            isPassword: false,
            radius: 6,
            validator: widget.validatorYear ??
                (value) {
                  if (value!.isEmpty) return 'Campo obrigatório';
                  return null;
                },
            focusNode: widget.focusNode,
          ),
        ),
      ],
    );
  }
}
