import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../shared/core/app_config.dart';
import '../../shared/services/session_service.dart';
import 'chat_controller.dart';

class ChatPage extends StatefulWidget {
  final String? title;
  final String? subTitle;
  final String? chatID;

  const ChatPage({
    super.key,
    this.title,
    this.subTitle,
    this.chatID,
  });

  @override
  _ChatPageState createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> {
  final SessionService _sessionService = Modular.get();
  final ChatController controller = Modular.get();

  @override
  void initState() {
    super.initState();

    controller.setLoading(true);
  }

  WebViewController initWebViewController(String? token) {
    final webviewController = WebViewController();

    webviewController.setJavaScriptMode(JavaScriptMode.unrestricted);
    webviewController.setNavigationDelegate(
      NavigationDelegate(
        onPageFinished: (v) {
          webviewController.currentUrl().then((value) {
            controller.setIsNotFound(value?.contains("Erros/Http404") ?? false);
          });

          controller.setLoading(false);
        },
      ),
    );
    webviewController.loadRequest(
      Uri.https(
        "www.empregare.com",
        "/pt-br/CandidateMessages/RedirectAuthChatApp",
        {
          'chatID': widget.chatID,
          'token': token,
        },
      ),
    );

    return webviewController;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(),
            FittedBox(
              child: Text(
                widget.title ?? '',
                style: const TextStyle(
                  color: Colors.black,
                ),
              ),
            ),
            Text(
              widget.subTitle ?? '',
              style: const TextStyle(
                color: Colors.black,
                fontSize: 10,
              ),
            ),
          ],
        ),
        leading: IconButton(
          splashRadius: 25,
          icon: const Icon(Icons.close, color: Colors.black),
          onPressed: Modular.to.pop,
        ),
      ),
      body: Observer(
        builder: (_) {
          if (controller.isNotFound) {
            return const Center(
              child: Text(
                "Não foi possível encontrar!",
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
            );
          }

          return Stack(
            children: [
              FutureBuilder(
                future: _sessionService.get(),
                builder: (_, snps) {
                  if (snps.hasData) {
                    return WebViewWidget(
                      controller: initWebViewController(snps.data!.token),
                    );
                  }

                  return const ColoredBox(
                    color: Colors.white,
                    child: Center(
                      child: SpinKitCircle(
                        color: AppConfig.colorPrimary,
                      ),
                    ),
                  );
                },
              ),
              Visibility(
                visible: controller.loading,
                child: const ColoredBox(
                  color: Colors.white,
                  child: Center(
                    child: SpinKitCircle(
                      color: AppConfig.colorPrimary,
                    ),
                  ),
                ),
              )
            ],
          );
        },
      ),
    );
  }
}
