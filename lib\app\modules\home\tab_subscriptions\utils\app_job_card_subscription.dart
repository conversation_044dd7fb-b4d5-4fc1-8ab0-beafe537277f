// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:bottom_sheet/bottom_sheet.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:empregare_app/app/modules/jobs/job_detail/job_detail_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

import '../../../../shared/models/responses/inscricoes_response_model.dart';
import '../../../subscription/subscription_controller.dart';
import '../../home_controller.dart';
import '../datails/body_details.dart';
import '../datails/header_details.dart';
import '../tab_subscriptions_controller.dart';

class AppJobCardSubscription extends StatefulWidget {
  final JobModel job;
  final bool isSubscription;

  const AppJobCardSubscription({
    super.key,
    required this.job,
    this.isSubscription = false,
  });

  @override
  State<AppJobCardSubscription> createState() => _AppJobCardSubscriptionState();
}

class _AppJobCardSubscriptionState extends State<AppJobCardSubscription>
    with AutomaticKeepAliveClientMixin {
  final controllerTab = Modular.get<TabSubscriptionsController>();
  final controller = Modular.get<HomeController>();

  Color getBackgroundColor(String text) {
    switch (text) {
      case 'Processo seletivo em andamento':
        return const Color(0xFF9E9EEF);
      case 'Processo seletivo cancelado':
        return const Color(0xFF9E9FA2);
      case 'Aprovada - Aguardando publicação da vaga':
        return const Color(0xFF5CB85C);
      case 'Processo seletivo pausado':
        return const Color(0xFFD9534F);
      case 'Processo seletivo encerrado':
        return const Color(0xFF656668);
      default:
        return const Color(0xFF656668);
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return InkWell(
      onTap: () async {
        controllerTab.load();
        controller.setCloseScreen(false);

        var subscription = Modular.get<SubscriptionController>();

        subscription.load(widget.job.candidaturaID).whenComplete(() {
          controllerTab.changeSubscription(subscription.inscricao);
        });

        controllerTab.changeJob(widget.job);

        await showStickyFlexibleBottomSheet(
          minHeight: .82,
          initHeight: .82,
          maxHeight: .92,
          maxHeaderHeight: 100,
          minHeaderHeight: 70,
          isSafeArea: true,
          useRootScaffold: true,
          bottomSheetBorderRadius: const BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
          anchors: [0.8],
          decoration: const BoxDecoration(
            color: Colors.white,
          ),
          context: context,
          isDismissible: true,
          duration: const Duration(milliseconds: 700),
          headerBuilder: (BuildContext context, double offset) {
            return Observer(
              builder: (_) => HeaderDeatails(
                job: widget.job,
                closeScreen: controller.closeScreen,
              ),
            );
          },
          bodyBuilder: (context, bottomSheetOffset) {
            return SliverChildListDelegate([
              BodyDetails(job: widget.job),
            ]);
          },
        );

        controllerTab.changeJob(null);
        controllerTab.changeSubscription(null);
        Modular.get<JobDetailController>().removeJob(
          null,
        );
      },
      child: DecoratedBox(
        decoration: const BoxDecoration(
          border: Border.symmetric(
            horizontal: BorderSide(
              color: Color(0xFFEEEFF3),
              width: 1,
            ),
          ),
        ),
        child: SizedBox(
          height: 128,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Padding(
                    padding: const EdgeInsets.only(
                      left: 24,
                      right: 12,
                      top: 16,
                      bottom: 6,
                    ),
                    child: SizedBox(
                      width: 60,
                      height: 50,
                      child: Visibility(
                        visible: widget.job.empresa != 'EMPRESA CONFIDENCIAL',
                        replacement: Container(
                          decoration: const BoxDecoration(
                            borderRadius: BorderRadius.all(Radius.circular(6)),
                            color: Color(0xFFEFF3FC),
                          ),
                          child: Image.asset(
                            scale: 1.5,
                            'lib/assets/icons/lock_v2.png',
                          ),
                        ),
                        child: CachedNetworkImage(
                          imageUrl: widget.job.logo?.thumb ?? '',
                          imageBuilder: (context, imageProvider) => Container(
                            decoration: BoxDecoration(
                              image: DecorationImage(
                                image: imageProvider,
                                fit: BoxFit.fitWidth,
                                alignment: Alignment.center,
                              ),
                            ),
                          ),
                          placeholder: (context, url) =>
                              SpinKitCircle(color: Colors.blue[300]),
                          errorWidget: (context, url, error) =>
                              const Icon(Icons.broken_image, size: 60),
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(
                        right: 24,
                        top: 16,
                        bottom: 6,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.job.empresa ?? "",
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: const TextStyle(
                              fontSize: 14,
                              fontFamily: 'Inter',
                              fontWeight: FontWeight.w400,
                              color: Colors.black,
                            ),
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Text(
                            widget.job.titulo ?? "",
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: const TextStyle(
                              fontWeight: FontWeight.w400,
                              fontFamily: 'Inter',
                              fontSize: 16,
                              color: Colors.black,
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                ],
              ),
              FutureBuilder<String?>(
                future:
                    Modular.get<SubscriptionController>().getInscricaoSituacao(
                  widget.job.candidaturaID!,
                ),
                builder: (context, snap) {
                  if (snap.data?.isEmpty ?? true) {
                    return const SizedBox();
                  }

                  return Padding(
                    padding: const EdgeInsets.only(left: 24.0, bottom: 15),
                    child: Card(
                      color: getBackgroundColor(snap.data ?? ''),
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          snap.data ?? '',
                          style: const TextStyle(
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
