import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../shared/app_container.dart';
import '../../../shared/core/app_config.dart';
import '../../../shared/widgets/app_html.dart';
import '../questionnaire_controller.dart';

class WidgetPerguntaCaixaDeSelecaoUnica extends StatelessWidget {
  const WidgetPerguntaCaixaDeSelecaoUnica({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Modular.get<QuestionnaireController>();

    var alternativas = controller.pergunta!.alternativas;

    if (controller.pergunta?.alternativas == null) {
      return const SizedBox.shrink();
    }
    return Observer(
      builder: (_) => AppContainer(
        child: Column(
          children: List.generate(
            controller.pergunta!.alternativas!.length,
            (i) {
              var alternativa = controller.pergunta!.alternativas![i];

              return InkWell(
                onTap: () {
                  controller.setAlternativasMultiplas(alternativa.id!);
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Checkbox(
                      activeColor: AppConfig.colorPrimary,
                      value: controller.alternativasSelecionadas
                          .contains(alternativa.id),
                      onChanged: (bool? value) {
                        if (alternativas![i].id != null) {
                          controller.setAlternativasMultiplas(alternativa.id!);
                        }
                      },
                      key: Key(alternativa.id.toString()),
                    ),
                    AppHtml(
                      data: controller.pergunta?.descricao!,
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
