import 'package:dio/dio.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../core/app_rest.dart';
import '../models/responses/cidade_response_model.dart';
import '../models/responses/estado_response_model.dart';
import '../models/responses/pais_response_model.dart';

class CitiesRepository {
  final AppRest _rest = Modular.get();

  Future<PaisResponseModel> getPaises() async {
    try {
      final response = await _rest.get('/variaveis/paises');
      return PaisResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return PaisResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return PaisResponseModel.fromJson(err.response!.data);
    }
  }

  Future<EstadoResponseModel> getEstados({required String? paisID}) async {
    try {
      final response = await _rest.get('/variaveis/estados/$paisID');

      return EstadoResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return EstadoResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return EstadoResponseModel.fromJson(err.response!.data);
    }
  }

  Future<CidadeResponseModel> getCidades({required int? estadoID}) async {
    try {
      final response = await _rest.get('/variaveis/cidades/$estadoID');
      return CidadeResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return CidadeResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return CidadeResponseModel.fromJson(err.response!.data);
    }
  }

  Future<CidadeResponseModel> getCidadeSugestions({
    required String query,
  }) async {
    try {
      final response = await _rest.get(
        '/variaveis/cidades/buscarV2?query=$query',
      );

      return CidadeResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return CidadeResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return CidadeResponseModel.fromJson(err.response!.data);
    }
  }
}
