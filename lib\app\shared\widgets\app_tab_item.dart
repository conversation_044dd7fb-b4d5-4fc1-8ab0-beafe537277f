import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../core/app_config.dart';
import 'app_image_network_widget.dart';
import 'app_usuario_card/app_usuario_card_controller.dart';

class TabItem extends StatelessWidget {
  const TabItem({
    super.key,
    this.imageNoActive,
    this.imageActive,
    required this.title,
    this.index,
    this.routeName,
    this.paddingHeight,
    required this.isProfile,
    required this.isSelected,
    required this.onPressed,
    this.appUsuarioController,
  });

  final String? imageNoActive;
  final String? imageActive;
  final String title;
  final int? index;
  final String? routeName;
  final double? paddingHeight;
  final bool isProfile;
  final bool isSelected;
  final Function(String? routeName, int? index) onPressed;
  final AppUsuarioCardController? appUsuarioController;

  @override
  Widget build(BuildContext context) {
    return MaterialButton(
      onPressed: () => onPressed(routeName, index),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: <Widget>[
          const SizedBox(
            height: 1,
          ),
          isSelected
              ? Container(
                  color: AppConfig.colorPrimary,
                  height: 3.0,
                  width: 60,
                )
              : const SizedBox(
                  height: 3,
                ),
          const SizedBox(
            height: 5,
          ),
          Observer(
            builder: (_) => Visibility(
              visible: isProfile,
              replacement: imageActive != null && imageNoActive != null
                  ? ImageIcon(
                      AssetImage(isSelected ? imageActive! : imageNoActive!),
                      size: 50,
                      color: isSelected ? AppConfig.colorPrimary : Colors.grey,
                    )
                  : const Icon(
                      Icons.error,
                      size: 50,
                      color: Colors.grey,
                    ),
              child: ClipOval(
                child: AppImageNetworkWidget(
                  appUsuarioController?.foto ?? '',
                  height: 50,
                  width: 50,
                  fit: BoxFit.cover,
                  errorImage: 'lib/assets/images/person-filled.png',
                  scale: 4,
                ),
              ),
            ),
          ),
          SizedBox(
            height: isProfile ? paddingHeight : 1,
          ),
          Text(
            title.i18n,
            style: TextStyle(
              fontSize: 18,
              color: isSelected ? AppConfig.colorPrimary : Colors.grey,
              fontFamily: 'Inter',
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }
}
