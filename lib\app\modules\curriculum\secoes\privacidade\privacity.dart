import 'package:bottom_sheet/bottom_sheet.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../../shared/core/app_config.dart';
import '../../../home/<USER>/tab_profile_controller.dart';
import '../../curriculum_controller.dart';
import 'card_privacity.dart';
import 'save_privacy_button.dart';

class Privacity extends StatelessWidget {
  const Privacity({
    super.key,
    required this.controllerTabProfile,
  });

  final TabProfileController controllerTabProfile;

  @override
  Widget build(BuildContext context) {
    final controller = Modular.get<CurriculumController>();

    return Observer(
      builder: (_) => Container(
        decoration: BoxDecoration(
          color: const Color(0xFFD8D8E6).withValues(alpha: 0.8),
        ),
        height: 48,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 20.0),
              child: Container(
                decoration: const BoxDecoration(
                  color: AppConfig.colorPrimary,
                  borderRadius: BorderRadius.all(Radius.circular(6)),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(
                      height: 20,
                      child: Image.asset(
                        controllerTabProfile.privacidade?.tipo == 1
                            ? 'lib/assets/icons/earth.png'
                            : 'lib/assets/icons/cadeado.png',
                        color: controllerTabProfile.privacidade?.tipo == 1
                            ? null
                            : Colors.white,
                        fit: BoxFit.contain,
                      ),
                    ),
                    const SizedBox(width: 5),
                    Visibility(
                      visible: !controller.salvandoPrivacidade,
                      replacement: const SizedBox(
                        height: 18,
                        width: 18,
                        child: CircularProgressIndicator(
                          color: AppConfig.colorPrimary,
                          backgroundColor: AppConfig.grey,
                        ),
                      ),
                      child: Text(
                        controllerTabProfile.privacidade?.nome ?? 'Indefinido',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            InkWell(
              onTap: () async {
                await controller.initializeSelectedCard(
                  controllerTabProfile.privacidade?.tipo,
                );

                double initHeight =
                    (450 / MediaQuery.of(context).size.height).clamp(0.1, 1.0);
                double anchor = (initHeight / 2).clamp(0.05, initHeight);

                await showStickyFlexibleBottomSheet(
                  initHeight: initHeight + 0.1,
                  maxHeight: 1,
                  minHeight: 0.20,
                  maxHeaderHeight: 118,
                  minHeaderHeight: 100,
                  isSafeArea: true,
                  bottomSheetBorderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(25),
                    topRight: Radius.circular(25),
                  ),
                  anchors: [anchor, initHeight],
                  decoration: const BoxDecoration(
                    color: Colors.white,
                  ),
                  context: context,
                  isDismissible: true,
                  duration: const Duration(milliseconds: 500),
                  headerBuilder: (BuildContext context, double offset) {
                    return ColoredBox(
                      color: Colors.white,
                      child: Column(
                        children: [
                          const SizedBox(height: 4),
                          Container(
                            width: 84,
                            height: 3,
                            decoration: BoxDecoration(
                              color: const Color(0xFFE3E3E3),
                              borderRadius: BorderRadius.circular(200),
                            ),
                          ),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Padding(
                                padding: EdgeInsets.only(
                                  top: 50,
                                  left: 24,
                                  right: 24,
                                ),
                                child: Text(
                                  'Privacidade do currículo',
                                  style: TextStyle(
                                    color: Color(0xFF292929),
                                    fontSize: 24,
                                    fontFamily: 'Inter',
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                              const Spacer(),
                              Padding(
                                padding: const EdgeInsets.only(
                                  top: 10,
                                  right: 15,
                                ),
                                child: InkWell(
                                  onTap: () {
                                    Navigator.of(context).pop();
                                  },
                                  child: Image.asset(
                                    'lib/assets/images/close.png',
                                    scale: 2,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    );
                  },
                  bodyBuilder: (context, bottomSheetOffset) {
                    return SliverChildListDelegate(
                      [
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 24),
                          child: Observer(
                            builder: (_) => Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                CardPrivacity(
                                  description:
                                      'Currículo completo visível para todas as empresas anunciantes do site.',
                                  title: 'Público',
                                  image: 'lib/assets/icons/privacity/union.png',
                                  isSelected: controller.selectedCardIndex == 1,
                                  onTap: () {
                                    controller.selectCard(1);
                                    controller.setPrivacy(1);
                                  },
                                ),
                                const SizedBox(height: 16),
                                CardPrivacity(
                                  description:
                                      'Seus dados de contato disponíveis somente nas inscrições das vagas ou solicitação prévia.',
                                  title: 'Privado',
                                  image: 'lib/assets/icons/privacity/v2.png',
                                  isSelected: controller.selectedCardIndex == 2,
                                  onTap: () {
                                    controller.selectCard(2);
                                    controller.setPrivacy(2);
                                  },
                                ),
                                const SizedBox(height: 16),
                                CardPrivacity(
                                  description:
                                      'Seus dados de contato disponíveis somente nas inscrições das vagas ou solicitação prévia.',
                                  title: 'Desativado',
                                  image:
                                      'lib/assets/icons/privacity/visibilidade.png',
                                  isSelected: controller.selectedCardIndex == 3,
                                  onTap: () {
                                    controller.selectCard(3);
                                    controller.setPrivacy(3);
                                  },
                                ),
                                SavePrivacyButton(
                                  controller: controller,
                                  controllerTabProfile: controllerTabProfile,
                                ),
                                const SizedBox(height: 100),
                              ],
                            ),
                          ),
                        )
                      ],
                    );
                  },
                );
              },
              child: Row(
                children: [
                  Icon(
                    controllerTabProfile.privacidade?.tipo != 1
                        ? Icons.lock_outline_rounded
                        : Icons.lock_open_rounded,
                    color: AppConfig.colorPrimary,
                  ),
                  const SizedBox(width: 5),
                  const Padding(
                    padding: EdgeInsets.only(right: 20.0),
                    child: Text(
                      'Privacidade',
                      style: TextStyle(
                        color: Color(0xFF292929),
                        fontSize: 12,
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
