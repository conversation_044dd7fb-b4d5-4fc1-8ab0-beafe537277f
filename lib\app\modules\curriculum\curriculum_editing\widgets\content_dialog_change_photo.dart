import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../../../shared/core/app_config.dart';
import '../../../curriculum/curriculum_controller.dart';
import 'row_default.dart';

class ContentDialogChangePhoto extends StatefulWidget {
  const ContentDialogChangePhoto({super.key});

  @override
  State<ContentDialogChangePhoto> createState() =>
      _ContentDialogChangePhotoState();
}

class _ContentDialogChangePhotoState extends State<ContentDialogChangePhoto> {
  final CurriculumController _curriculumController = Modular.get();

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        SimpleDialog(
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 15,
            vertical: 10,
          ),
          titlePadding: EdgeInsets.zero,
          children: [
            InkWell(
              child: RowDefault(
                icon: const Icon(Icons.camera_alt, color: Colors.grey),
                title: Text('Tirar Foto'.i18n),
              ),
              onTap: () {},
            ),
            const SizedBox(height: 20),
            InkWell(
              child: RowDefault(
                icon: const Icon(Icons.image, color: Colors.grey),
                title: Text('Buscar Foto'.i18n),
              ),
              onTap: () {},
            ),
            const SizedBox(height: 20),
            InkWell(
              child: RowDefault(
                icon: const Icon(Icons.close, color: Colors.red),
                title: Text('Cancelar'.i18n),
              ),
              onTap: () => Modular.to.pop(),
            ),
          ],
        ),
        Observer(
          builder: (BuildContext context) {
            return _curriculumController.loading
                ? Column(
                    children: [
                      Expanded(
                        child: Container(
                          color: Colors.white70,
                          child: const SpinKitCircle(
                            color: AppConfig.colorPrimary,
                          ),
                        ),
                      ),
                    ],
                  )
                : Container();
          },
        ),
      ],
    );
  }
}
