import 'package:flutter_modular/flutter_modular.dart';

import '../../shared/widgets/app_usuario_card/app_usuario_card_controller.dart';
import '../home/<USER>/tab_profile_controller.dart';
import 'controllers/complementary_controller.dart';
import 'controllers/computing_controller.dart';
import 'controllers/deficiencies_controller.dart';
import 'controllers/diversities_controller.dart';
import 'controllers/experience_controller.dart';
import 'controllers/languages_controller.dart';
import 'controllers/objective_controller.dart';
import 'controllers/social_media_controller.dart';
import 'curriculum_controller.dart';
import 'curriculum_editing/curriculum_editing_controller.dart';
import 'curriculum_page.dart';

class CurriculumModule extends Module {
  static const route = '/curriculum';

  @override
  void binds(i) {
    i.addLazySingleton(TabProfileController.new);

    i.addLazySingleton(DeficienciesController.new);
    i.addLazySingleton(DiversitiesController.new);
    i.addLazySingleton(LanguagesController.new);
    i.addLazySingleton(ObjectiveController.new);
    i.addLazySingleton(SocialMediaController.new);
    i.addLazySingleton(ComputingController.new);
    i.addLazySingleton(ComplementaryController.new);
    i.addLazySingleton(ExperienceController.new);
    i.addLazySingleton(CurriculumController.new);
    i.addLazySingleton(AppUsuarioCardController.new);
    i.addLazySingleton(CurriculumEditingController.new);
  }

  @override
  void routes(r) {
    r.child(
      '/',
      child: (context) => CurriculumPage(isHelper: r.args.data ?? false),
    );
  }
}
