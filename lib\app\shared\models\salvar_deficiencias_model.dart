class SalvarDeficienciasModel {
  final List<SalvarDeficienciaModel>? deficiencias;
  final String? descricao;
  final int? remover;

  SalvarDeficienciasModel({
    this.deficiencias,
    this.descricao,
    this.remover = 0,
  });

  Map<String, dynamic> toJson() {
    final result = <String, dynamic>{};

    result.addAll({
      'deficiencias': deficiencias?.map((x) => x.toJson()).toList(),
    });

    result.addAll({'descricao': descricao});
    result.addAll({'remover': remover});

    return result;
  }
}

class SalvarDeficienciaModel {
  final int? id;
  final int? pessoaID;
  final int? tipo;
  final int? deficienciaID;
  final String? nome;
  final String? categoriaNome;

  SalvarDeficienciaModel({
    this.id,
    this.pessoaID,
    this.tipo,
    this.deficienciaID,
    this.nome,
    this.categoriaNome,
  });

  Map<String, dynamic> toJson() {
    final result = <String, dynamic>{};

    result.addAll({'id': id});
    result.addAll({'pessoaID': pessoaID});
    result.addAll({'tipo': tipo});
    result.addAll({'deficienciaID': deficienciaID});
    result.addAll({'nome': nome});
    result.addAll({'categoriaNome': categoriaNome});

    return result;
  }
}
