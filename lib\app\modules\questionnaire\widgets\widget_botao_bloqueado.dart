import 'package:flutter/material.dart';

import '../../../shared/app_container.dart';

class WidgetBloqueado extends StatelessWidget {
  const WidgetBloqueado({
    super.key,
    required this.context,
  });

  final BuildContext context;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(15),
      height: MediaQuery.of(context).size.height * 0.8,
      child: const Center(
        child: AppContainer(
          color: Color(0xFFFFF5E8),
          height: 50,
          width: double.maxFinite,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Text(
                'Você excedeu o limite de tentativas!',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Color(0xFFFF762E),
                  fontWeight: FontWeight.w500,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
