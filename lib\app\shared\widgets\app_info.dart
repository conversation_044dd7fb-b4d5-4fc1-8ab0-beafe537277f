import 'package:flutter/material.dart';

class AppInfo extends StatelessWidget {
  final Icon icon;
  final Text title;
  final Text? subtitle;
  final Widget? child;
  final EdgeInsetsGeometry? padding;

  const AppInfo({
    super.key,
    required this.icon,
    required this.title,
    this.subtitle,
    this.padding,
    this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: <Widget>[
          icon,
          const SizedBox(width: 7),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                FittedBox(
                  child: Text(
                    title.data!,
                    style: title.style?.copyWith(
                      fontWeight: FontWeight.w300,
                      color: const Color(0xFF888888),
                      fontSize: 11,
                    ),
                  ),
                ),
                child != null
                    ? child!
                    : FittedBox(
                        child: Text(
                          subtitle?.data ?? "",
                          style: subtitle?.style?.copyWith(
                            fontWeight: FontWeight.w500,
                            fontSize: 12,
                          ),
                        ),
                      )
              ],
            ),
          )
        ],
      ),
    );
  }
}
