class SalvarDiversidadeModel {
  bool? compartilharDadosDiversidade;
  bool? naoInformarDadosDiversidade;
  int? pronome;
  int? identidadeGenero;
  int? orientacaoSexual;
  int? racaCor;

  SalvarDiversidadeModel({
    this.compartilharDadosDiversidade,
    this.naoInformarDadosDiversidade,
    this.pronome,
    this.identidadeGenero,
    this.orientacaoSexual,
    this.racaCor,
  });

  Map<String, dynamic> toJson() {
    return {
      "compartilharDadosDiversidade": compartilharDadosDiversidade ?? false,
      "naoInformarDadosDiversidade": naoInformarDadosDiversidade ?? false,
      "pronome": pronome,
      "identidadeGenero": identidadeGenero,
      "orientacaoSexual": orientacaoSexual,
      "racaCor": racaCor,
    };
  }

  SalvarDiversidadeModel copyWith({
    compartilharDadosDiversidade,
    pronome,
    identidadeGenero,
    orientacaoSexual,
    racaCor,
    naoInformarDadosDiversidade,
  }) {
    return SalvarDiversidadeModel(
      compartilharDadosDiversidade:
          compartilharDadosDiversidade ?? this.compartilharDadosDiversidade,
      naoInformarDadosDiversidade:
          naoInformarDadosDiversidade ?? this.naoInformarDadosDiversidade,
      pronome: pronome ?? this.pronome,
      identidadeGenero: identidadeGenero ?? this.identidadeGenero,
      orientacaoSexual: orientacaoSexual ?? this.orientacaoSexual,
      racaCor: racaCor ?? this.racaCor,
    );
  }
}
