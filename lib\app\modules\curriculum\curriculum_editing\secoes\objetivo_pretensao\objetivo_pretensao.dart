import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../../../../shared/widgets/app_dropdown_search.dart';
import '../../../../../shared/widgets/app_text_form_field.dart';
import '../../../../curriculum/curriculum_controller.dart';
import '../../curriculum_editing_controller.dart';
import '../../widgets/secao.dart';
import 'widgets/badges_areas.dart';

class ObjetivoPretensao extends StatefulWidget {
  const ObjetivoPretensao({super.key});

  @override
  State<ObjetivoPretensao> createState() => _ObjetivoPretensaoState();
}

class _ObjetivoPretensaoState extends State<ObjetivoPretensao> {
  final CurriculumController _curriculumController = Modular.get();
  final controller = Modular.get<CurriculumEditingController>();

  @override
  Widget build(BuildContext context) {
    return Secao(
      fields: Observer(builder: (_) {
        var areasInteresseToSaveLength =
            _curriculumController.objective.areasInteresseToSave.length;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Padding(
            //   padding: EdgeInsets.only(
            //     top: _curriculumController.objective.pretensaoSalarial != null
            //         ? 8
            //         : 0,
            //   ),
            //   child: Column(
            //     crossAxisAlignment: CrossAxisAlignment.start,
            //     children: [
            //       Text(
            //         'Pretensão salarial'.i18n,
            //         style: const TextStyle(
            //           fontSize: 14,
            //         ),
            //       ),
            //       const SizedBox(height: 5),
            //       DropdownSearch(
            //         context,
            //         hintText: 'Pretensão salarial',
            //         title: 'Pretensão salarial'.i18n,
            //         items: _curriculumController.objective.pretensoes
            //             .map((e) => DropdownSearchItem(
            //                   value: e,
            //                   searchKey: e.titulo!,
            //                   text: e.titulo,
            //                   child: Text(e.titulo!),
            //                 ))
            //             .toList(),
            //         onSelected: (dynamic item) {
            //           if (item != null) {
            //             _curriculumController.objective.setPretensao(item);
            //           }
            //         },
            //         value: _curriculumController.objective.pretensaoSalarial,
            //       ),
            //     ],
            //   ),
            // ),
            // const SizedBox(height: 10),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'De:'.i18n,
                  style: const TextStyle(
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 5),
                PretensaoSalarialInput(
                  initialValue: _curriculumController
                      .objective.pretensaoSalarialMin
                      ?.toString(),
                  currencyValue: _curriculumController.objective.moeda,
                  onCurrency: _curriculumController.objective.setMoeda,
                  onChanged: (value) {
                    _curriculumController.objective.pretensaoSalarialMin =
                        value;
                  },
                ),
              ],
            ),
            const SizedBox(height: 10),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Até:'.i18n,
                  style: const TextStyle(
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 5),
                PretensaoSalarialInput(
                  initialValue: _curriculumController
                      .objective.pretensaoSalarialMax
                      ?.toString(),
                  currencyValue: _curriculumController.objective.moeda,
                  onCurrency: _curriculumController.objective.setMoeda,
                  onChanged: (value) {
                    _curriculumController.objective.pretensaoSalarialMax =
                        value;
                  },
                ),
              ],
            ),
            const SizedBox(height: 10),
            Padding(
              padding: EdgeInsets.only(
                top: areasInteresseToSaveLength > 0 ? 8 : 0,
              ),
              child: Column(
                children: [
                  BadgesAreas(
                    areasInteresseToSaveLength: areasInteresseToSaveLength,
                  ),
                  const SizedBox(
                    height: 5,
                  ),
                  Visibility(
                    visible: areasInteresseToSaveLength < 3 ||
                        areasInteresseToSaveLength == 0,
                    child: DropdownSearch(
                      context,
                      hintText: 'Adicionar áreas de interesse'.i18n,
                      title: 'Adicionar áreas de interesse'.i18n,
                      items: _curriculumController.objective.areasSeguro
                          .where((a) {
                        return !_curriculumController
                            .objective.areasInteresseToSave
                            .any((item) {
                          return item.nome == a.nome;
                        });
                      }).map(
                        (e) {
                          return DropdownSearchItem(
                            value: e,
                            searchKey: e.nomeSeguro,
                            text: '',
                            child: Text(e.nomeSeguro),
                          );
                        },
                      ).toList(),
                      onSelected: (dynamic item) {
                        if (item != null) {
                          _curriculumController.objective
                              .addAreasInteresseToSave(item);
                        }
                      },
                      value: null,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 5),
            Text(
              'Escolha até 3 áreas de seu interesse'.i18n,
              style: const TextStyle(
                fontSize: 10,
                color: Colors.grey,
              ),
            ),
          ],
        );
      }),
    );
  }
}

class PretensaoSalarialInput extends StatefulWidget {
  final String? currencyValue;
  final String? initialValue;
  final void Function(String? value) onCurrency;
  final void Function(int? valueInCents) onChanged;

  const PretensaoSalarialInput({
    super.key,
    this.currencyValue,
    this.initialValue,
    required this.onCurrency,
    required this.onChanged,
  });

  @override
  State<PretensaoSalarialInput> createState() => _PretensaoSalarialInputState();
}

class _PretensaoSalarialInputState extends State<PretensaoSalarialInput> {
  @override
  Widget build(BuildContext context) {
    return AppTextFormField(
      keyboardType: const TextInputType.numberWithOptions(),
      initialText: widget.initialValue,
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
        LengthLimitingTextInputFormatter(6)
      ],
      onChanged: (value) {
        if (value.isEmpty) {
          widget.onChanged(null);
        } else {
          String cleanValue = value.replaceAll(RegExp(r'[^\d]'), '');
          try {
            final parsedValue = int.tryParse(cleanValue);
            widget.onChanged(parsedValue);
          } catch (e) {
            widget.onChanged(null);
          }
        }
      },
      hintText: 'Valor',
      suffixText: '.00',
      radius: 6,
      prefixIcon: Padding(
        padding: const EdgeInsets.only(right: 8),
        child: DropdownMenu(
          width: 90,
          onSelected: widget.onCurrency,
          initialSelection: widget.currencyValue,
          dropdownMenuEntries: ["R\$", "\$", "€"].map(
            (item) {
              return DropdownMenuEntry(
                value: item,
                label: item,
              );
            },
          ).toList(),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Campo obrigatório';
        }

        String cleanValue = value.replaceAll(RegExp(r'[^\d]'), '');
        try {
          final intValue = int.tryParse(cleanValue);
          if (intValue == null || intValue < 1 || intValue > 999999) {
            return 'Insira um valor entre 1 e 999999';
          }
        } catch (e) {
          return 'Valor inválido';
        }

        return null;
      },
    );
  }
}
