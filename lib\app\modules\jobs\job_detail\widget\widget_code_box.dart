import 'package:flutter/material.dart';

class WidgetCodeBox extends StatelessWidget {
  final int? id;
  final String? dataCadastro;
  final String? dataAtualizacao;

  const WidgetCodeBox({
    super.key,
    this.id,
    this.dataCadastro,
    this.dataAtualizacao,
  });

  Widget _buildRow(String label, String value) {
    return Row(
      children: [
        Text(label),
        Text(
          value,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        )
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildRow('Código: ', '$id'),
        const SizedBox(
          height: 15,
        ),
        _buildRow('Data de Cadastro: ', '$dataCadastro'),
        const SizedBox(
          height: 15,
        ),
        _buildRow('Data de Atualização: ', '$dataAtualizacao'),
      ],
    );
  }
}
