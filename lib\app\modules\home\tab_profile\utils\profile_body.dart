// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:bottom_sheet/bottom_sheet.dart';
import 'package:empregare_app/app/modules/home/<USER>/utils/behavioral_profile.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:i18n_extension/default.i18n.dart';
import 'package:launch_app_store/launch_app_store.dart';

import '../../../../shared/core/app_config.dart';
import '../../../curriculum/curriculum_controller.dart';
import '../../../curriculum/curriculum_module.dart';
import '../../../curriculum/secoes/privacidade/card_privacity.dart';
import '../../../curriculum/secoes/privacidade/save_privacy_button.dart';
import '../../../settings/settings_controller.dart';
import '../../../settings/settings_module.dart';
import '../../../weblink/weblink_module.dart';
import '../tab_profile_controller.dart';

class ProfileBody extends StatefulWidget {
  const ProfileBody({
    super.key,
  });

  @override
  State<ProfileBody> createState() => _ProfileBodyState();
}

class _ProfileBodyState extends State<ProfileBody> {
  final controllerConfig = Modular.get<SettingsController>();

  final controllerTabProfile = Modular.get<TabProfileController>();

  final controllerCurriculum = Modular.get<CurriculumController>();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        Expanded(
          child: ListView(
            children: <Widget>[
              const Padding(
                padding: EdgeInsets.only(
                  left: 24,
                  bottom: 16,
                  top: 16,
                ),
                child: Text(
                  'Perfil',
                  style: TextStyle(
                    fontSize: 20,
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              ProfileDefaultContainer(
                title: 'Meu currículo',
                path: 'lib/assets/icons/profile/my_resume.png',
                onTap: () {
                  Modular.to.pushNamed(CurriculumModule.route);
                },
              ),
              ProfileDefaultContainer(
                title: 'Privacidade do Currículo',
                path: 'lib/assets/icons/profile/resume_privacy.png',
                onTap: () async {
                  await controllerCurriculum.initializeSelectedCard(
                    controllerTabProfile.privacidade?.tipo,
                  );

                  double initHeight = (450 / MediaQuery.of(context).size.height)
                      .clamp(0.1, 1.0);

                  double anchor = (initHeight / 2).clamp(0.05, initHeight);

                  await showStickyFlexibleBottomSheet(
                    initHeight: initHeight + 0.2,
                    maxHeight: 1,
                    minHeight: 0.20,
                    maxHeaderHeight: 118,
                    minHeaderHeight: 100,
                    isSafeArea: true,
                    bottomSheetBorderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(25),
                      topRight: Radius.circular(25),
                    ),
                    anchors: [anchor, initHeight],
                    decoration: const BoxDecoration(
                      color: Colors.white,
                    ),
                    context: context,
                    isDismissible: true,
                    duration: const Duration(milliseconds: 500),
                    headerBuilder: (BuildContext context, double offset) {
                      return ColoredBox(
                        color: Colors.white,
                        child: Column(
                          children: [
                            const SizedBox(height: 4),
                            Container(
                              width: 84,
                              height: 3,
                              decoration: BoxDecoration(
                                color: const Color(0xFFE3E3E3),
                                borderRadius: BorderRadius.circular(200),
                              ),
                            ),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Padding(
                                  padding: EdgeInsets.only(
                                    top: 50,
                                    left: 24,
                                    right: 24,
                                  ),
                                  child: Text(
                                    'Privacidade do currículo',
                                    style: TextStyle(
                                      color: Color(0xFF292929),
                                      fontSize: 24,
                                      fontFamily: 'Inter',
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                                const Spacer(),
                                Padding(
                                  padding: const EdgeInsets.only(
                                    top: 10,
                                    right: 15,
                                  ),
                                  child: InkWell(
                                    onTap: () => Navigator.of(context).pop(),
                                    child: Image.asset(
                                      'lib/assets/images/close.png',
                                      scale: 2,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      );
                    },
                    bodyBuilder: (context, bottomSheetOffset) {
                      return SliverChildListDelegate(
                        [
                          Padding(
                            padding: const EdgeInsets.only(left: 24, right: 24),
                            child: GetBuilder<CurriculumController>(
                              init: controllerCurriculum,
                              builder: (_) => Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  CardPrivacity(
                                    description:
                                        'Currículo completo visível para todas as empresas anunciantes do site.',
                                    title: 'Público',
                                    image:
                                        'lib/assets/icons/privacity/union.png',
                                    isSelected: controllerCurriculum
                                            .selectedCardIndex ==
                                        1,
                                    onTap: () {
                                      controllerCurriculum.selectCard(1);
                                      controllerCurriculum.setPrivacy(1);
                                    },
                                  ),
                                  const SizedBox(height: 16),
                                  CardPrivacity(
                                    description:
                                        'Seus dados de contato disponíveis somente nas inscrições das vagas ou solicitação prévia.',
                                    title: 'Privado',
                                    image: 'lib/assets/icons/privacity/v2.png',
                                    isSelected: controllerCurriculum
                                            .selectedCardIndex ==
                                        2,
                                    onTap: () {
                                      controllerCurriculum.selectCard(2);
                                      controllerCurriculum.setPrivacy(2);
                                    },
                                  ),
                                  const SizedBox(height: 16),
                                  CardPrivacity(
                                    description:
                                        'Seus dados de contato disponíveis somente nas inscrições das vagas ou solicitação prévia.',
                                    title: 'Desativado',
                                    image:
                                        'lib/assets/icons/privacity/visibilidade.png',
                                    isSelected: controllerCurriculum
                                            .selectedCardIndex ==
                                        3,
                                    onTap: () {
                                      controllerCurriculum.selectCard(3);
                                      controllerCurriculum.setPrivacy(3);
                                    },
                                  ),
                                  SavePrivacyButton(
                                    controller: controllerCurriculum,
                                    controllerTabProfile: controllerTabProfile,
                                  )
                                ],
                              ),
                            ),
                          )
                        ],
                      );
                    },
                  );
                },
              ),
              ProfileDefaultContainer(
                title: 'Perfil comportamental',
                path: 'lib/assets/icons/profile/behavioral_profile.png',
                onTap: () async {
                  Modular.to.pushNamed(BehavioralProfile.route);
                },
              ),
              const Padding(
                padding: EdgeInsets.only(
                  left: 24,
                  bottom: 16,
                  top: 16,
                ),
                child: Text(
                  'Suporte ao candidato',
                  style: TextStyle(
                    fontSize: 20,
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              ProfileDefaultContainer(
                title: 'Avalie nosso aplicativo',
                path: 'lib/assets/icons/profile/rate_our_app.png',
                onTap: () {
                  LaunchReview.launch(
                    writeReview: false,
                    iOSAppId: AppConfig.kAppIos,
                  );
                },
              ),
              ProfileDefaultContainer(
                title: 'Termos de uso',
                path: 'lib/assets/icons/profile/terms_of_use.png',
                onTap: () {
                  Modular.to.pushNamed(
                    WeblinkModule.route,
                    arguments: {
                      'link': AppConfig.termsOfUseUrl,
                      'title': 'Termos de Uso'
                    },
                  );
                },
              ),
              ProfileDefaultContainer(
                title: 'Política de privacidade',
                path: 'lib/assets/icons/profile/privacy_policy.png',
                onTap: () {
                  Modular.to.pushNamed(
                    WeblinkModule.route,
                    arguments: {
                      'link': AppConfig.privacyPolicyUrl,
                      'title': 'Política de Privacidade e Uso de Dados'
                    },
                  );
                },
              ),
              const Padding(
                padding: EdgeInsets.only(
                  left: 24,
                  bottom: 16,
                  top: 16,
                ),
                child: Text(
                  'Conta',
                  style: TextStyle(
                    fontSize: 20,
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              ProfileDefaultContainer(
                title: 'Configurações',
                path: 'lib/assets/icons/profile/settings.png',
                onTap: () {
                  Modular.to.pushNamed(SettingsModule.route);
                },
              ),
              ProfileDefaultContainer(
                title: 'Ajuda',
                path: 'lib/assets/icons/profile/help.png',
                onTap: () {
                  Modular.to.pushNamed(
                    WeblinkModule.route,
                    arguments: {
                      'link': AppConfig.helpUrl,
                      'title': 'Ajuda para candidatos'
                    },
                  );
                },
              ),
              ProfileDefaultContainer(
                title: 'Sair do aplicativo',
                path: 'lib/assets/icons/profile/exit_the_application.png',
                onTap: controllerConfig.sair,
              ),
            ],
          ),
        )
      ],
    );
  }
}

class ProfileDefaultContainer extends StatelessWidget {
  const ProfileDefaultContainer({
    super.key,
    this.path,
    this.title,
    required this.onTap,
  });

  final String? path;
  final String? title;
  final Function() onTap;

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey,
            offset: Offset(0.0, 0.0),
            blurRadius: 0.3,
            spreadRadius: 0.0,
          )
        ],
      ),
      child: Material(
        animationDuration: const Duration(seconds: 1),
        child: SizedBox(
          height: 80,
          child: Center(
            child: ListTile(
              splashColor: const Color(0xFFEBEBFB),
              dense: true,
              leading: SizedBox(
                height: 30,
                width: 30,
                child: Image.asset(
                  path ?? '',
                  scale: 1.35,
                ),
              ),
              title: Text(
                '$title'.i18n,
                style: const TextStyle(
                  fontSize: 16,
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w400,
                ),
              ),
              onTap: onTap,
            ),
          ),
        ),
      ),
    );
  }
}
