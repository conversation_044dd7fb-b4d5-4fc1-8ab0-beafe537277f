class ClubeBeneficiosCategoriasModel {
  final bool sucesso;
  final List<Categoria> dados;

  ClubeBeneficiosCategoriasModel({
    required this.sucesso,
    required this.dados,
  });

  factory ClubeBeneficiosCategoriasModel.fromJson(Map<String, dynamic> json) {
    return ClubeBeneficiosCategoriasModel(
      sucesso: json['sucesso'],
      dados: List<Categoria>.from(
          json['dados'].map((categoria) => Categoria.fromJson(categoria))),
    );
  }
}

class Categoria {
  final int id;
  final String nome;
  final bool selected;

  Categoria({
    required this.id,
    required this.nome,
    required this.selected,
  });

  factory Categoria.fromJson(Map<String, dynamic> json) {
    return Categoria(
      id: json['id'],
      nome: json['nome'],
      selected: json['selected'],
    );
  }
}
