import 'package:flutter_modular/flutter_modular.dart';
import 'package:mobx/mobx.dart';

import '../../../shared/models/responses/inscricao_response_model.dart';
import '../../../shared/models/responses/inscricoes_response_model.dart';
import '../../../shared/repositories/inscription_repository.dart';
import '../../jobs/job_detail/job_detail_controller.dart';

part 'tab_subscriptions_controller.g.dart';

class TabSubscriptionsController = _TabSubscriptionsControllerBase
    with _$TabSubscriptionsController;

abstract class _TabSubscriptionsControllerBase with Store {
  final InscriptionRepository _repository = Modular.get();

  @observable
  InscricoesResponseModel? data;

  List<JobModel>? get inscricoes => data != null ? data!.dados : [];

  @observable
  bool? loading;

  @action
  Future<void> load() async {
    try {
      loading = true;
      data = await _repository.getInscricoes(pagina: 1);
    } finally {
      loading = false;
    }
  }

  @observable
  JobModel? job;

  @action
  void changeJob(JobModel? value) => job = value;

  @observable
  InscricaoResponseModel? subscription;
  @action
  void changeSubscription(InscricaoResponseModel? value) {
    final controllerJob = Modular.get<JobDetailController>();
    controllerJob.getJobDetails(value?.vaga?.id);

    subscription = value;
  }

  int getVagaId() {
    return subscription?.vaga?.id ?? 0;
  }

  bool hasVagaId() {
    return getVagaId() != 0;
  }
}
