import 'package:flutter_modular/flutter_modular.dart';
import 'package:get/get.dart';

import '../../../shared/mixins/loader_mixin.dart';
import '../../../shared/models/responses/inscricao_response_model.dart';
import '../../../shared/models/responses/inscricoes_response_model.dart';
import '../../../shared/repositories/inscription_repository.dart';
import '../../jobs/job_detail/job_detail_controller.dart';

class TabSubscriptionsController extends GetxController with LoaderMixin {
  final InscriptionRepository _repository = Modular.get();

  InscricoesResponseModel? data;

  List<JobModel>? get inscricoes => data != null ? data!.dados : [];

  Future<void> load() async {
    try {
      changeLoading(true);
      data = await _repository.getInscricoes(pagina: 1);
      update();
    } finally {
      changeLoading(false);
    }
  }

  JobModel? job;

  void changeJob(JobModel? value) {
    job = value;
    update();
  }

  InscricaoResponseModel? subscription;

  void changeSubscription(InscricaoResponseModel? value) {
    final controllerJob = Modular.get<JobDetailController>();
    controllerJob.getJobDetails(value?.vaga?.id);

    subscription = value;
    update();
  }

  int getVagaId() {
    return subscription?.vaga?.id ?? 0;
  }

  bool hasVagaId() {
    return getVagaId() != 0;
  }
}
