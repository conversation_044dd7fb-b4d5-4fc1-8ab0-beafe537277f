import 'dart:core';

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../shared/core/app_config.dart';

class WeblinkPage extends StatefulWidget {
  final String? link;
  final String? title;

  const WeblinkPage({super.key, this.link, this.title});

  @override
  State<WeblinkPage> createState() => _WeblinkPageState();
}

class _WeblinkPageState extends State<WeblinkPage> {
  bool _isDisposed = false;

  @override
  void initState() {
    super.initState();
    initWebViewController();
  }

  @override
  void dispose() {
    _isDisposed = true;
    super.dispose();
  }

  bool isNotFound = false;
  void setIsNotFound(bool value) {
    if (_isDisposed) return;
    isNotFound = value;
    setState(() {});
  }

  bool loading = true;
  void setLoading(bool value) {
    if (_isDisposed) return;
    loading = value;
    setState(() {});
  }

  WebViewController? controller;
  void initWebViewController() {
    final webviewController = WebViewController();

    webviewController.setJavaScriptMode(JavaScriptMode.unrestricted);
    webviewController.setNavigationDelegate(
      NavigationDelegate(
        onPageFinished: (v) {
          webviewController.currentUrl().then((value) {
            if (_isDisposed) return;
            setIsNotFound(value?.contains("Erros/Http404") ?? false);
          }).catchError((error) {
            if (_isDisposed) return;
            setIsNotFound(true);
          });

          if (_isDisposed) return;
          setLoading(false);
        },
        onWebResourceError: (WebResourceError error) {
          if (_isDisposed) return;
          if (error.isForMainFrame ?? false) {
            setIsNotFound(true);
          }
          setLoading(false);
        },
      ),
    );

    webviewController.loadRequest(Uri.parse(widget.link ?? 'Empregare'));

    controller = webviewController;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.title ?? '',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: Modular.to.pop,
        ),
      ),
      body: Visibility(
        visible: isNotFound,
        replacement: Stack(
          children: [
            if (controller != null) WebViewWidget(controller: controller!),
            Visibility(
              visible: loading,
              child: Container(
                color: Colors.white,
                child: const Center(
                  child: SpinKitCircle(
                    color: AppConfig.colorPrimary,
                  ),
                ),
              ),
            )
          ],
        ),
        child: const Center(
          child: Text(
            "Não foi possível encontrar!",
            style: TextStyle(fontWeight: FontWeight.w500),
          ),
        ),
      ),
    );
  }
}
