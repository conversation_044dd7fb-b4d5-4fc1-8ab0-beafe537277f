import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:intl/intl.dart';

import '../../../../shared/models/responses/inscricoes_response_model.dart';
import '../../../jobs/job_detail/job_detail_controller.dart';
import '../../../jobs/job_detail/widget/shimmer_details_job.dart';
import '../../../questionnaire/questionnaire_controller.dart';
import '../../../subscription/subscription_controller.dart';

class BodyDetails extends StatefulWidget {
  const BodyDetails({super.key, required this.job});

  final JobModel job;

  @override
  State<BodyDetails> createState() => _BodyDetailsState();
}

class _BodyDetailsState extends State<BodyDetails> {
  Future<dynamic>? future;

  final controllerJob = Modular.get<JobDetailController>();
  final controller = Modular.get<SubscriptionController>();
  final controllerQuest = Modular.get<QuestionnaireController>();

  @override
  void initState() {
    future = controller.load(widget.job.candidaturaID ?? 0);

    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: future,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting &&
            !snapshot.hasData) {
          return const Padding(
            padding: EdgeInsets.symmetric(horizontal: 24.0),
            child: ShimmerDetailsJob(),
          );
        }

        return Column(
          children: [
            DecoratedBox(
              decoration: const BoxDecoration(
                border: Border.symmetric(
                  horizontal: BorderSide(color: Color(0xFFEEEFF3), width: 2),
                ),
              ),
              child: SizedBox(
                height: 130,
                width: MediaQuery.of(context).size.width,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(left: 24.0, top: 16),
                      child: Text(
                        'Data da sua inscrição ${dataConverter()}',
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          fontFamily: "Inter",
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 20.0, top: 10),
                      child: Card(
                        color: getBackgroundColor(
                          controller.inscricao?.candidatura?.situacaoText ?? '',
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Text(
                            controller.inscricao?.candidatura?.situacaoText ??
                                '',
                            style: const TextStyle(
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ),
                    const Padding(
                      padding: EdgeInsets.only(
                        left: 24.0,
                        top: 10,
                      ),
                      child: Text(
                        'Testes e questionários',
                        style: TextStyle(
                          fontFamily: "Inter",
                          fontSize: 24,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ),
            DecoratedBox(
              decoration: const BoxDecoration(
                border: Border.symmetric(
                  horizontal: BorderSide(color: Color(0xFFEEEFF3), width: 2),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(
                      left: 20.0,
                      top: 10,
                      right: 20,
                    ),
                    child: Column(
                      children: [
                        if (controller.inscricao?.questionarios?.isEmpty ??
                            true)
                          Container(
                            margin: const EdgeInsets.only(top: 20, bottom: 20),
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                color: const Color(0xFFCE8112),
                              ),
                              color: const Color(0xFFFDF6DE),
                            ),
                            child: const Row(
                              children: [
                                Icon(Icons.info, color: Colors.red),
                                SizedBox(width: 10),
                                Expanded(
                                  child: Text(
                                    "Não há nenhum questionário disponível para esta vaga",
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ...(controller.inscricao?.questionarios ?? []).map(
                          (q) {
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const SizedBox(height: 10),
                                Text(
                                  q.questionarioTitulo ?? "",
                                  style: const TextStyle(
                                    fontSize: 19,
                                    fontWeight: FontWeight.w400,
                                    fontFamily: "Inter",
                                  ),
                                ),
                                const SizedBox(height: 10),
                                Row(
                                  children: [
                                    const Text(
                                      'Situação',
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w400,
                                        fontFamily: "Inter",
                                      ),
                                    ),
                                    const SizedBox(width: 10),
                                    DecoratedBox(
                                      decoration: BoxDecoration(
                                        color: getBackColorQuest(
                                          q.situacao ?? '',
                                        ),
                                        borderRadius: const BorderRadius.all(
                                          Radius.circular(
                                            500,
                                          ),
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(8.0),
                                        child: Text(
                                          '${q.situacao}',
                                          style: const TextStyle(
                                            color: Color(0xFF252525),
                                            fontSize: 12,
                                            fontFamily: "Inter",
                                            fontWeight: FontWeight.w400,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 15),
                                DecoratedBox(
                                  decoration: const BoxDecoration(
                                    color: Color(0xFF1B1B63),
                                    borderRadius: BorderRadius.all(
                                      Radius.circular(
                                        500,
                                      ),
                                    ),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(5.0),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Container(
                                          height: 15,
                                          width: 15,
                                          padding: const EdgeInsets.all(2),
                                          margin: const EdgeInsets.all(5),
                                          decoration: BoxDecoration(
                                            color: const Color(0xfff05656),
                                            borderRadius: BorderRadius.circular(
                                              50,
                                            ),
                                          ),
                                          child: const Center(
                                            child: Text(
                                              "??",
                                              style: TextStyle(
                                                color: Colors.white,
                                                fontSize: 8,
                                              ),
                                            ),
                                          ),
                                        ),
                                        Text(
                                          q.situacao ?? '',
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontSize: 10,
                                            fontFamily: "Inter",
                                            fontWeight: FontWeight.w400,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 15),
                                Row(
                                  children: [
                                    Text.rich(
                                      TextSpan(
                                        text: 'Solicitado: ',
                                        style: const TextStyle(
                                          fontWeight: FontWeight.w500,
                                          fontSize: 12,
                                        ),
                                        children: [
                                          TextSpan(
                                            text: q.solicitadoView,
                                            style: const TextStyle(
                                              fontWeight: FontWeight.normal,
                                              fontSize: 12,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const SizedBox(width: 15),
                                    Text.rich(
                                      TextSpan(
                                        text: 'Respondido: ',
                                        style: const TextStyle(
                                          fontWeight: FontWeight.w500,
                                          fontSize: 12,
                                        ),
                                        children: [
                                          TextSpan(
                                            text: q.respondidoView,
                                            style: const TextStyle(
                                              fontWeight: FontWeight.normal,
                                              fontSize: 12,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 15),
                              ],
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  String? dataConverter() {
    final dataString = controller.inscricao?.candidatura?.data;

    if (dataString == null || dataString.isEmpty) {
      return 'Data não disponível';
    }

    try {
      final formatoOriginal = DateFormat('yyyy-MM-dd HH:mm');
      final data = formatoOriginal.parse(dataString);
      final formatoDesejado = DateFormat('dd/MM/yyyy');
      return formatoDesejado.format(data);
    } catch (e) {
      try {
        final formatoAlternativo = DateFormat('yyyy-MM-dd');
        final data = formatoAlternativo.parse(dataString);
        final formatoDesejado = DateFormat('dd/MM/yyyy');
        return formatoDesejado.format(data);
      } catch (e2) {
        return 'Data inválida';
      }
    }
  }

  Color getBackgroundColor(String text) {
    switch (text) {
      case 'Processo seletivo em andamento':
        return const Color(0xFF9E9EEF);
      case 'Processo seletivo cancelado':
        return const Color(0xFF9E9FA2);
      case 'Aprovada - Aguardando publicação da vaga':
        return const Color(0xFF5CB85C);
      case 'Processo seletivo pausado':
        return const Color(0xFFD9534F);
      case 'Processo seletivo encerrado':
        return const Color(0xFF656668);
      default:
        return const Color(0xFF656668);
    }
  }

  Color getBackColorQuest(String text) {
    switch (text) {
      case 'Respondido':
        return const Color(0xFFD2EFD4);
      case 'Processo seletivo em andamento':
        return const Color(0xFF9E9EEF);
      case 'Processo seletivo cancelado':
        return const Color(0xFF9E9FA2);
      case 'Processo seletivo pausado':
        return const Color(0xFFD9534F);
      case 'Processo seletivo encerrado':
        return const Color(0xFF656668);
      default:
        return const Color.fromARGB(255, 224, 224, 224);
    }
  }
}
