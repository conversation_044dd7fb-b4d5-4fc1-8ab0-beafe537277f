class DiversidadeResponseModel {
  final bool sucesso;
  final String mensagem;
  final DiversidadeModel? diversidade;

  DiversidadeResponseModel({
    this.sucesso = false,
    this.mensagem = "",
    this.diversidade,
  });
  factory DiversidadeResponseModel.fromJson(Map<String, dynamic> json) =>
      DiversidadeResponseModel(
        sucesso: json['sucesso'] ?? false,
        mensagem: json['mensagem'] ?? "",
        diversidade: json["diversidade"] == null
            ? null
            : DiversidadeModel.fromJson(json["diversidade"]),
      );
}

class DiversidadeModel {
  bool? dadosNulos;
  int? pronome;
  int? identidadeGenero;
  int? orientacaoSexual;
  int? racaCor;
  bool? marcarCheckbox;
  bool? naoInformarDadosDiversidade;
  String? pronomeTexto;
  String? identidadeGeneroTexto;
  String? orientacaoSexualTexto;
  String? racaCorTexto;
  String? cisGeneroTooltip;
  String? transGeneroTooltip;
  String? heterossexualTooltip;
  String? homossexualTooltip;
  String? bissexualTooltip;
  String? pansexualTooltip;
  String? assexualTooltip;
  String? alertTexto;
  ModalAjuda? modalAjuda;

  DiversidadeModel({
    this.dadosNulos = false,
    this.pronome,
    this.marcarCheckbox = false,
    this.identidadeGenero,
    this.orientacaoSexual,
    this.racaCor,
    this.naoInformarDadosDiversidade = false,
    this.pronomeTexto,
    this.identidadeGeneroTexto,
    this.orientacaoSexualTexto,
    this.racaCorTexto,
    this.cisGeneroTooltip,
    this.transGeneroTooltip,
    this.heterossexualTooltip,
    this.homossexualTooltip,
    this.bissexualTooltip,
    this.pansexualTooltip,
    this.assexualTooltip,
    this.alertTexto,
    this.modalAjuda,
  });

  factory DiversidadeModel.fromJson(Map<String, dynamic> json) {
    return DiversidadeModel(
      dadosNulos: json['dadosNulos'] as bool? ?? false,
      pronome: json['pronome'] as int?,
      marcarCheckbox: json['marcarCheckbox'] as bool? ?? false,
      identidadeGenero: json['identidadeGenero'] as int?,
      orientacaoSexual: json['orientacaoSexual'] as int?,
      racaCor: json['racaCor'] as int?,
      naoInformarDadosDiversidade:
          json['naoInformarDadosDiversidade'] as bool? ?? false,
      pronomeTexto: json['pronomeTexto'] as String?,
      identidadeGeneroTexto: json['identidadeGeneroTexto'] as String?,
      orientacaoSexualTexto: json['orientacaoSexualTexto'] as String?,
      racaCorTexto: json['racaCorTexto'] as String?,
      cisGeneroTooltip: json['cisGeneroTooltip'] as String?,
      transGeneroTooltip: json['transGeneroTooltip'] as String?,
      heterossexualTooltip: json['heterossexualTooltip'] as String?,
      homossexualTooltip: json['homossexualTooltip'] as String?,
      bissexualTooltip: json['bissexualTooltip'] as String?,
      pansexualTooltip: json['pansexualTooltip'] as String?,
      assexualTooltip: json['assexualTooltip'] as String?,
      alertTexto: json['alertTexto'] as String?,
      modalAjuda: json["modalAjuda"] == null
          ? null
          : ModalAjuda.fromJson(json["modalAjuda"]),
    );
  }

  Map<String, dynamic> toJson() => {
        "dadosNulos": dadosNulos,
        "pronome": pronome,
        "identidadeGenero": identidadeGenero,
        "orientacaoSexual": orientacaoSexual,
        "racaCor": racaCor,
        "marcarCheckbox": marcarCheckbox,
        "naoInformarDadosDiversidade": naoInformarDadosDiversidade,
        "pronomeTexto": pronomeTexto,
        "identidadeGeneroTexto": identidadeGeneroTexto,
        "orientacaoSexualTexto": orientacaoSexualTexto,
        "racaCorTexto": racaCorTexto,
        "cisGeneroTooltip": cisGeneroTooltip,
        "transGeneroTooltip": transGeneroTooltip,
        "heterossexualTooltip": heterossexualTooltip,
        "homossexualTooltip": homossexualTooltip,
        "bissexualTooltip": bissexualTooltip,
        "pansexualTooltip": pansexualTooltip,
        "assexualTooltip": assexualTooltip,
        "alertTexto": alertTexto,
        "modalAjuda": modalAjuda,
      };

  DiversidadeModel copy() {
    return DiversidadeModel(
      dadosNulos: dadosNulos = false,
      pronome: pronome,
      identidadeGenero: identidadeGenero,
      orientacaoSexual: orientacaoSexual,
      racaCor: racaCor,
      marcarCheckbox: marcarCheckbox = false,
      naoInformarDadosDiversidade: naoInformarDadosDiversidade = false,
      pronomeTexto: pronomeTexto,
      identidadeGeneroTexto: identidadeGeneroTexto,
      orientacaoSexualTexto: orientacaoSexualTexto,
      racaCorTexto: racaCorTexto,
      cisGeneroTooltip: cisGeneroTooltip,
      transGeneroTooltip: transGeneroTooltip,
      heterossexualTooltip: heterossexualTooltip,
      homossexualTooltip: homossexualTooltip,
      bissexualTooltip: bissexualTooltip,
      pansexualTooltip: pansexualTooltip,
      assexualTooltip: assexualTooltip,
      alertTexto: alertTexto,
      modalAjuda: modalAjuda,
    );
  }
}

class ModalAjuda {
  String? modalAjudaTitulo;
  String? modalAjudaSubtituloIdentidadeGenero;
  String? modalAjudaTextoIdentidadeGenero;
  String? modalAjudaSubtituloOrientacaoSexual;
  String? modalAjudaTextoOrientacaoSexual;
  String? modalAjudaSubtituloRacaCor;
  String? modalAjudaTextoRacaCor;
  String? modalAjudaSubtituloObjetivoDeUso;
  String? modalAjudaTextoObjetivoDeUso;

  ModalAjuda({
    this.modalAjudaTitulo,
    this.modalAjudaSubtituloIdentidadeGenero,
    this.modalAjudaTextoIdentidadeGenero,
    this.modalAjudaSubtituloOrientacaoSexual,
    this.modalAjudaTextoOrientacaoSexual,
    this.modalAjudaSubtituloRacaCor,
    this.modalAjudaTextoRacaCor,
    this.modalAjudaSubtituloObjetivoDeUso,
    this.modalAjudaTextoObjetivoDeUso,
  });

  Map<String, dynamic> toJson() => {
        'modalAjudaTitulo': modalAjudaTitulo,
        'modalAjudaSubtituloIdentidadeGenero':
            modalAjudaSubtituloIdentidadeGenero,
        'modalAjudaTextoIdentidadeGenero': modalAjudaTextoIdentidadeGenero,
        'modalAjudaSubtituloOrientacaoSexual':
            modalAjudaSubtituloOrientacaoSexual,
        'modalAjudaTextoOrientacaoSexual': modalAjudaTextoOrientacaoSexual,
        'modalAjudaSubtituloRacaCor': modalAjudaSubtituloRacaCor,
        'modalAjudaTextoRacaCor': modalAjudaTextoRacaCor,
        'modalAjudaSubtituloObjetivoDeUso': modalAjudaSubtituloObjetivoDeUso,
        'modalAjudaTextoObjetivoDeUso': modalAjudaTextoObjetivoDeUso,
      };

  factory ModalAjuda.fromJson(Map<String, dynamic> json) => ModalAjuda(
        modalAjudaTitulo: json['modalAjudaTitulo'] as String?,
        modalAjudaSubtituloIdentidadeGenero:
            json['modalAjudaSubtituloIdentidadeGenero'] as String?,
        modalAjudaTextoIdentidadeGenero:
            json['modalAjudaTextoIdentidadeGenero'] as String?,
        modalAjudaSubtituloOrientacaoSexual:
            json['modalAjudaSubtituloOrientacaoSexual'] as String?,
        modalAjudaTextoOrientacaoSexual:
            json['modalAjudaTextoOrientacaoSexual'] as String?,
        modalAjudaSubtituloRacaCor:
            json['modalAjudaSubtituloRacaCor'] as String?,
        modalAjudaTextoRacaCor: json['modalAjudaTextoRacaCor'] as String?,
        modalAjudaSubtituloObjetivoDeUso:
            json['modalAjudaSubtituloObjetivoDeUso'] as String?,
        modalAjudaTextoObjetivoDeUso:
            json['modalAjudaTextoObjetivoDeUso'] as String?,
      );
}
