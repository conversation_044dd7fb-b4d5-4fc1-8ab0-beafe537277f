import 'package:flutter/services.dart';

class CpfOrEmail<PERSON><PERSON>atter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    final newText = newValue.text;

    if (newText.isEmpty) {
      return newValue;
    }

    bool isCpf = true;
    isCpf = double.tryParse(newText) != null;
    List<String> splitted = newText.split('.');
    if (splitted.isNotEmpty) {
      isCpf = double.tryParse(splitted.first) != null;
    }
    if (!isCpf) {
      return TextEditingValue(
        text: newText,
        selection: TextSelection.collapsed(offset: newText.length),
      );
    } else {
      if (newText.length <= 14) {
        final formattedCpf = _formatCpf(newText);
        return TextEditingValue(
          text: formattedCpf,
          selection: TextSelection.collapsed(offset: formattedCpf.length),
        );
      } else {
        return TextEditingValue(
          text: oldValue.text,
          selection: TextSelection.collapsed(offset: oldValue.text.length),
        );
      }
    }
  }

  String _formatCpf(String cpf) {
    final numericCpf = cpf.replaceAll(RegExp(r'[^0-9]'), '');

    if (numericCpf.length <= 3) {
      return numericCpf;
    } else if (numericCpf.length <= 6) {
      return '${numericCpf.substring(0, 3)}.${numericCpf.substring(3)}';
    } else if (numericCpf.length <= 9) {
      return '${numericCpf.substring(0, 3)}.${numericCpf.substring(3, 6)}.${numericCpf.substring(6)}';
    } else {
      return '${numericCpf.substring(0, 3)}.${numericCpf.substring(3, 6)}.${numericCpf.substring(6, 9)}-${numericCpf.substring(9)}';
    }
  }
}
