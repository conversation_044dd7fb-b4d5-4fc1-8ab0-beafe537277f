import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

import '../../../shared/core/app_config.dart';
import '../../../shared/models/responses/questionario_response_model.dart';
import '../questionnaire_controller.dart';
import 'widget_botao_bloqueado.dart';
import 'widget_builder_pergunta.dart';
import 'widget_encerramento.dart';
import 'widget_orientacoes.dart';

class WidgetBuilderBody extends StatelessWidget {
  const WidgetBuilderBody({
    super.key,
    required this.controller,
    required this.scaffoldKey,
    required this.questionario,
    required this.context,
  });

  final QuestionnaireController controller;
  final GlobalKey<ScaffoldState> scaffoldKey;
  final QuestionarioModel? questionario;
  final BuildContext context;

  @override
  Widget build(BuildContext context) {
    return Observer(
      builder: (_) {
        if (controller.state == QuestionnaireState.responding) {
          return const WidgetPergunta();
        } else if (controller.state == QuestionnaireState.guideLines) {
          return WidgetOrientacoes(questionario: questionario);
        } else if (controller.state == QuestionnaireState.blocked) {
          return WidgetBloqueado(context: context);
        } else if (controller.state == QuestionnaireState.finishe) {
          return WidgetEncerramento(controller: controller);
        } else if (controller.state == QuestionnaireState.progress) {
          return Container(
            padding: const EdgeInsets.all(15),
            height: MediaQuery.of(context).size.height * 0.8,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(controller.progressMessage ?? ''),
                  Visibility(
                    visible: !controller.comprimindo,
                    replacement: const SpinKitCircle(
                      color: AppConfig.colorPrimary,
                    ),
                    child: Column(
                      children: [
                        SizedBox(
                          height: 30,
                          child: LinearProgressIndicator(
                            value: controller.progress,
                          ),
                        ),
                        Text(controller.progressLegend),
                      ],
                    ),
                  )
                ],
              ),
            ),
          );
        } else
          // ignore: curly_braces_in_flow_control_structures
          return SizedBox(
            height: MediaQuery.of(context).size.height * 0.8,
            child: const Center(
              child: SpinKitCircle(color: AppConfig.colorPrimary),
            ),
          );
      },
    );
  }
}
