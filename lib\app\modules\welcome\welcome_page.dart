import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../shared/core/app_config.dart';
import '../../shared/core/app_translation.i18n.dart';
import '../auth/login/login_module.dart';
import '../auth/signup/signup_module.dart';
import 'utils/build_indicator_widget.dart';

class WelcomePage extends StatefulWidget {
  static const route = '/welcome';

  const WelcomePage({super.key});

  @override
  State<WelcomePage> createState() => _WelcomePageState();
}

class _WelcomePageState extends State<WelcomePage> {
  final PageController pageController = PageController();

  int currentPage = 0;

  @override
  void initState() {
    super.initState();
    pageController.addListener(() {
      setState(() {
        currentPage = pageController.page!.round();
      });
    });
  }

  @override
  void dispose() {
    pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final List<String> images = [
      'lib/assets/images/splash_01.png',
      'lib/assets/images/splash_02.png',
      'lib/assets/images/splash_03.png',
    ];

    final List<String> texts = [
      'Cadastre seu currículo e encontre vagas selecionadas',
      'Personalize seu currículo para se destacar na multidão de candidatos',
      'Encontre novas oportunidades gratuitamente',
    ];

    return Scaffold(
      backgroundColor: AppConfig.white,
      body: SingleChildScrollView(
        child: Column(
          children: <Widget>[
            Padding(
              padding: const EdgeInsets.only(
                top: 48,
                left: 24,
                right: 191,
                bottom: 15,
              ),
              child: Image.asset(
                'lib/assets/images/logo-empregare-splash.png',
                fit: BoxFit.contain,
              ),
            ),
            SizedBox(
              height: MediaQuery.of(context).size.height * 0.61,
              child: PageView.builder(
                controller: pageController,
                itemCount: images.length,
                onPageChanged: (index) {
                  setState(() {
                    currentPage = index;
                  });
                },
                itemBuilder: (context, index) {
                  return Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Center(
                        child: Image.asset(
                          images[index],
                          fit: BoxFit.contain,
                          height: 250,
                        ),
                      ),
                      Flexible(
                        flex: 2,
                        child: Padding(
                          padding: const EdgeInsets.only(
                            top: 40,
                            left: 23.0,
                            right: 26,
                          ),
                          child: Text(
                            texts[index],
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                              fontSize: 18,
                              fontFamily: 'Inter',
                              fontWeight: FontWeight.w400,
                              color: Color(0xFF161519),
                            ),
                          ),
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 25),
              child: BuildIndicator(
                count: images.length,
                currentIndex: currentPage,
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(
                bottom: 12.0,
                left: 24.0,
                right: 24.0,
              ),
              child: SizedBox(
                height: 45.0,
                width: double.infinity,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    side:
                        const BorderSide(color: Color(0xff1b1b63), width: 1.6),
                    backgroundColor: AppConfig.colorPrimary,
                    shadowColor: Colors.transparent,
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6.0),
                    ),
                  ),
                  child: Text(
                    'Cadastre-se'.i18n,
                    style: const TextStyle(
                      color: AppConfig.white,
                      fontSize: 16,
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  onPressed: () async {
                    await Modular.to.pushNamed(
                      SignupModule.route,
                    );
                  },
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(
                bottom: 10.0,
                left: 24.0,
                right: 24.0,
              ),
              child: SizedBox(
                height: 45.0,
                width: double.infinity,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    side: const BorderSide(
                      color: Color(0xff1b1b63),
                      width: 1.0,
                    ),
                    backgroundColor: const Color(0xFFFFFFFF),
                    shadowColor: Colors.transparent,
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6.0),
                    ),
                  ),
                  child: Text(
                    'Entrar'.i18n,
                    style: const TextStyle(
                      color: AppConfig.colorPrimary,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  onPressed: () async {
                    await Modular.to.pushNamed(LoginModule.routeName);
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
