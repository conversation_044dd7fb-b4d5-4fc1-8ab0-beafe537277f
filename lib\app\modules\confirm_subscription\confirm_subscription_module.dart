import 'package:flutter_modular/flutter_modular.dart';

import 'confirm_subscription_controller.dart';
import 'confirm_subscription_page.dart';

class ConfirmSubscriptionModule extends Module {
  static const route = '/confirm_subscription';

  @override
  void binds(i) {
    i.addLazySingleton(ConfirmSubscriptionController.new);
  }

  @override
  void routes(r) {
    r.child(
      Modular.initialRoute,
      child: (context) => ConfirmSubscriptionPage(jobDetails: r.args.data),
    );
  }
}
