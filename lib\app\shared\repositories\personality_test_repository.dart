import 'package:dio/dio.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../core/app_rest.dart';

class PersonalityTestRepository {
  final AppRest _rest = Modular.get();

  Future<String?> getTalentoday() async {
    try {
      final response = await _rest.get('/candidato/talentoday');
      return response.data['suceso'] as bool ? response.data['url'] : null;
    } on DioException catch (err) {
      throw err.message ?? '';
    }
  }
}
