import 'package:empregare_app/app/shared/helpers/snack_bar_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';

import '../../../shared/core/app_config.dart';
import '../../../shared/core/app_translation.i18n.dart';
import '../../../shared/firebase/firebase_messaging.dart';
import '../../../shared/widgets/app_text_form_field.dart';
import '../../forgot_password/utils/cpf_or_email_formatter.dart';
import '../../home/<USER>';
import 'login_controller.dart';
import 'widgets/forgot_password_widget.dart';
import 'widgets/show_people_dialog.dart';
import 'widgets/sign_up_widget.dart';

class LoginPage extends StatelessWidget {
  const LoginPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConfig.white,
      body: SingleChildScrollView(
        child: Form(
          key: Get.find<LoginController>().formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Padding(
                padding: const EdgeInsets.only(top: 50, left: 20, right: 20),
                child: Image.asset(
                  'lib/assets/images/logo-vertical.png',
                  width: 163,
                  height: 163,
                  fit: BoxFit.contain,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                color: AppConfig.white,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: <Widget>[
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        const Text(
                          'E-mail ou CPF',
                          style: TextStyle(
                            fontSize: 14,
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w400,
                            color: Color(0xff161519),
                          ),
                        ),
                        const SizedBox(height: 10),
                        GetBuilder<LoginController>(
                          builder: (controller) {
                            return AppTextFormField(
                              hintText: 'Informe seu e-mail ou CPF',
                              enabled: !controller.loading,
                              onChanged: controller.user,
                              inputFormatters: [CpfOrEmailFormatter()],
                              radius: 6,
                            );
                          },
                        ),
                      ],
                    ),
                    const SizedBox(height: 15),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        const Text(
                          'Senha',
                          style: TextStyle(
                            fontSize: 14,
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w400,
                            color: Color(0xff161519),
                          ),
                        ),
                        const SizedBox(height: 10),
                        GetBuilder<LoginController>(
                          builder: (controller) {
                            return AppTextFormField(
                              hintText: 'Sua senha',
                              enabled: !controller.loading,
                              onChanged: controller.password,
                              isPassword: true,
                              radius: 6,
                            );
                          },
                        ),
                      ],
                    ),
                    const SizedBox(height: 25),
                    SizedBox(
                      height: 45.0,
                      width: double.infinity,
                      child: GetBuilder<LoginController>(
                        builder: (controller) {
                          return ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(6.0),
                              ),
                              shadowColor: Colors.transparent,
                              elevation: 0,
                              backgroundColor: AppConfig.colorPrimary,
                            ),
                            child: Visibility(
                              visible: controller.loading,
                              replacement: Text(
                                'Acessar'.i18n,
                                style: const TextStyle(color: AppConfig.white),
                              ),
                              child: const SpinKitCircle(color: AppConfig.white),
                            ),
                            onPressed: () {
                              if (controller.loading) return;
                              if (controller.formKey.currentState?.validate() != true) return;

                              doLogin(context);
                            },
                          );
                        },
                      ),
                    ),
                    const SizedBox(height: 25),
                    const ForgotPasswordWidget(),
                    const SizedBox(height: 15),
                    const Divider(color: Color(0xffd5d5d5), height: 1),
                    const SizedBox(height: 10),
                    const SignUpWidget(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> doLogin(BuildContext context, {String? personId}) async {
    final controller = Get.find<LoginController>();

    if (await controller.login(personId)) {
      await FirebaseMessagingUtils.instance.updateToken();
      Modular.to.pushReplacementNamed(HomeModule.route);
      return;
    }

    if (controller.people.isNotEmpty) {
      await showDialog(
        context: context,
        builder: (_) {
          return ShowPeopleDialog(
            people: controller.people,
            onLogin: (value) => doLogin(context, personId: value),
          );
        },
      );
      return;
    }

    SnackbarHelper.error(context, controller.message ?? 'Ocorreu um erro ao fazer login');
  }
}
