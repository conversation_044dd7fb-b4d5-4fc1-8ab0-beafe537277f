import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../../../../shared/widgets/app_text_form_field.dart';
import '../../../curriculum_controller.dart';
import '../../widgets/secao.dart';

class CartaApresentacao extends StatefulWidget {
  const CartaApresentacao({super.key});

  @override
  State<CartaApresentacao> createState() => _CartaApresentacaoState();
}

class _CartaApresentacaoState extends State<CartaApresentacao> {
  final CurriculumController _curriculumController = Modular.get();
  final FocusNode _textFieldFocusNode = FocusNode();
  late TextEditingController _textEditingController;

  @override
  void initState() {
    super.initState();

    _textEditingController = TextEditingController(
      text: _curriculumController.sintese,
    );

    _textEditingController.selection = TextSelection.fromPosition(
      TextPosition(offset: _textEditingController.text.length),
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      FocusScope.of(context).requestFocus(_textFieldFocusNode);
    });
  }

  @override
  void dispose() {
    _textFieldFocusNode.dispose();
    _textEditingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Secao(
      fields: Observer(
        builder: (_) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                'Escreva sobre você'.i18n,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 10),
              Text(
                'Escreva o que você sabe fazer de melhor, quais '
                        'são suas áreas de interesse e porque o estágio ou '
                        'trainee é importante para você.'
                    .i18n,
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(
                height: 5,
              ),
              AppTextFormField(
                hintText: '',
                controller: _textEditingController,
                focusNode: _textFieldFocusNode,
                minLines: 10,
                maxLines: 25,
                maxLength: 500,
                onChanged: (value) => _curriculumController.sintese = value,
              ),
            ],
          );
        },
      ),
    );
  }
}
