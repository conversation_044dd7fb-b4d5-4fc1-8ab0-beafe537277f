import 'package:flutter/material.dart';

import '../confirm_subscription_controller.dart';

class CidadesWidget extends StatelessWidget {
  const CidadesWidget({
    super.key,
    required this.controller,
  });

  final ConfirmSubscriptionController controller;

  @override
  Widget build(BuildContext context) {
    if (controller.jobDetails!.cidades!.length == 1) {
      controller.addCidadeInteresse(controller.jobDetails!.cidades!.first.id);
      return Text(
        controller.jobDetails!.cidades!.first.nome!,
        style: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: Colors.black,
        ),
      );
    }

    return Column(
      children: controller.jobDetails!.cidades!.map(
        (cidade) {
          return Row(
            children: [
              Checkbox(
                value: controller.cidadesInteresse.isNotEmpty &&
                    controller.cidadesInteresse
                        .where((cId) => cId == cidade.id)
                        .isNotEmpty,
                onChanged: (value) {
                  if (value!) {
                    controller.addCidadeInteresse(cidade.id);
                  } else {
                    controller.removeCidadeInteresse(cidade.id);
                  }
                },
              ),
              Text(
                cidade.nome!,
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          );
        },
      ).toList(),
    );
  }
}
