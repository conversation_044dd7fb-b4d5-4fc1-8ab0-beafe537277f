import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:percent_indicator/percent_indicator.dart';

import '../../../../shared/widgets/app_image_network_widget.dart';
import '../../../../shared/widgets/app_usuario_card/app_usuario_card_controller.dart';

class ProgressIndicatorAppBar extends StatelessWidget {
  final bool? isNotReturn;

  ProgressIndicatorAppBar({super.key, this.isNotReturn = false}) {
    controller.load();
  }

  final controller = Modular.get<AppUsuarioCardController>();

  @override
  Widget build(BuildContext context) {
    return Observer(
      builder: (_) => Row(
        children: [
          Visibility(
            visible: Modular.to.canPop(),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () => Modular.to.pop(),
                customBorder: const CircleBorder(),
                splashColor: Colors.grey.withValues(alpha: 0.6),
                child: Container(
                  padding: const EdgeInsets.all(8.0),
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.arrow_back,
                    color: Colors.black,
                    size: 28,
                  ),
                ),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              bottom: 13.0,
              top: 14.0,
              left: 6,
              right: 16,
            ),
            child: ClipOval(
              child: AppImageNetworkWidget(
                height: 45,
                width: 45,
                fit: BoxFit.cover,
                controller.foto,
                errorImage: 'lib/assets/images/person-filled.png',
                scale: 2,
              ),
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  controller.nome ?? '',
                  style: const TextStyle(
                    fontSize: 18,
                    color: Colors.black,
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Text(
                  'Progresso do seu perfil',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black,
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w400,
                  ),
                ),
                const SizedBox(height: 5),
                Observer(
                  builder: (_) => LinearPercentIndicator(
                    animation: true,
                    animationDuration: 2000,
                    lineHeight: 5.0,
                    percent: (controller.progresso / 100.0).clamp(0.0, 1.0),
                    backgroundColor: const Color(0xFF9E9EEF),
                    progressColor: const Color(0xFF3E3EDF),
                    padding: const EdgeInsets.only(
                      right: 85,
                    ),
                    barRadius: const Radius.circular(500),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
