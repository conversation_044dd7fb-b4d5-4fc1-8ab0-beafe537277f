import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../../../../shared/core/app_utils.dart';
import '../../../../../shared/models/responses/listar_grau_academico_response_model.dart';
import '../../../../../shared/widgets/app_dropdown_search.dart';
import '../../../../../shared/widgets/app_text_form_field.dart';
import '../../../../auth/signup/utils/custom_check_box.dart';
import '../../../../curriculum/curriculum_controller.dart';
import '../../curriculum_editing_controller.dart';
import '../../widgets/secao.dart';

class FormacaoAcademica extends StatefulWidget {
  const FormacaoAcademica({
    super.key,
    required this.formKey,
  });

  final GlobalKey<FormState> formKey;

  @override
  State<FormacaoAcademica> createState() => _FormacaoAcademicaState();
}

class _FormacaoAcademicaState extends State<FormacaoAcademica> {
  final CurriculumController _curriculumController = Modular.get();
  final controller = Modular.get<CurriculumEditingController>();

  String mesAnoToText(String? mes, String? ano) {
    if (mes == null || mes == '' || ano == null || ano == '') return '';

    mes = getMes(int.parse(mes)).substring(0, 3);
    mes = mes[0].toUpperCase() + mes.substring(1);
    return "$mes/$ano";
  }

  DateTime? mesAnoToDate(String? mes, String? ano) {
    if (mes == null || mes == '' || ano == null || ano == '') return null;

    return DateTime(int.parse(ano), int.parse(mes));
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    if (_curriculumController.formacaoToSave == null) {
      controller.formacaoAcademica = null;
      return const Center(
        child: Text("Erro ao carregar dados. Por favor, tente novamente."),
      );
    }

    controller.formacaoAcademica = _curriculumController.formacaoToSave?.grau;

    return Secao(
      fields: Form(
        key: widget.formKey,
        child: Observer(builder: (_) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(
                    top: _curriculumController.formacaoToSave?.grauID != null
                        ? 8
                        : 0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Padding(
                      padding: EdgeInsets.only(bottom: 5, left: 2),
                      child: Text(
                        'Grau acadêmico',
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontWeight: FontWeight.w400,
                          fontSize: 14,
                        ),
                      ),
                    ),
                    DropdownSearch(
                      context,
                      hintText: 'Grau acadêmico'.i18n,
                      title: 'Grau acadêmico'.i18n,
                      items: _curriculumController.grauAcademico!
                          .map((e) => DropdownSearchItem(
                                value: e,
                                searchKey: e.nome!,
                                text: e.nome,
                                child: Text(e.nome!),
                              ))
                          .toList(),
                      onSelected: (dynamic item) {
                        if (item != null) {
                          _curriculumController.formacaoToSave!.grauID =
                              item.id;
                          _curriculumController.formacaoToSave!.grau =
                              item.nome;
                          controller.formacaoAcademica = item.nome;

                          if (_curriculumController
                              .formacaoToSave!.isEnsinoFundamental) {
                            _curriculumController.formacaoToSave!
                                .setEnsinoFundamental();
                          } else if (_curriculumController
                              .formacaoToSave!.isEnsinoMedio) {
                            _curriculumController.formacaoToSave!
                                .setEnsinoMedio();
                          } else {
                            _curriculumController.formacaoToSave!
                                .setEnsinoSuperior();
                          }
                          setState(() {});
                        }
                      },
                      value: GrauAcademicoModel(
                        id: _curriculumController.formacaoToSave!.grauID,
                        nome: _curriculumController.formacaoToSave!.grau,
                      ),
                    ),
                  ],
                ),
              ),
              Visibility(
                visible: controller.formacaoAcademica != null &&
                    !_curriculumController.formacaoToSave!.isEnsinoFundamental,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Padding(
                      padding: EdgeInsets.only(
                        left: 2,
                        top: 10,
                        bottom: 5,
                      ),
                      child: Text(
                        'Instituição',
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontWeight: FontWeight.w400,
                          fontSize: 14,
                        ),
                      ),
                    ),
                    AppTextFormField(
                      hintText: 'Qual sua a instituição de ensino',
                      controller: TextEditingController(
                        text: _curriculumController.formacaoToSave!.local,
                      ),
                      textInputAction: TextInputAction.next,
                      onChanged: (value) {
                        _curriculumController.formacaoToSave!.local = value;
                      },
                      enabled: true,
                      isPassword: false,
                      radius: 6,
                      validator: (value) {
                        if (value!.isEmpty) return 'Campo obrigatório'.i18n;

                        return null;
                      },
                    ),
                  ],
                ),
              ),
              Visibility(
                visible: controller.formacaoAcademica != null &&
                    (!_curriculumController
                            .formacaoToSave!.isEnsinoFundamental &&
                        !_curriculumController.formacaoToSave!.isEnsinoMedio),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Padding(
                      padding: EdgeInsets.only(
                        left: 2,
                        top: 10,
                        bottom: 5,
                      ),
                      child: Text(
                        'Curso',
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontWeight: FontWeight.w400,
                          fontSize: 14,
                        ),
                      ),
                    ),
                    AppTextFormField(
                      hintText: 'Qual o curso',
                      controller: TextEditingController(
                        text: _curriculumController.formacaoToSave!.curso,
                      ),
                      textInputAction: TextInputAction.next,
                      onChanged: (value) {
                        _curriculumController.formacaoToSave!.curso = value;
                      },
                      enabled: true,
                      isPassword: false,
                      radius: 6,
                      validator: (value) {
                        if (value!.isEmpty) return 'Campo obrigatório'.i18n;

                        return null;
                      },
                    ),
                  ],
                ),
              ),
              Visibility(
                visible: controller.formacaoAcademica != null &&
                    (!_curriculumController
                        .formacaoToSave!.isEnsinoFundamental),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 5),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Padding(
                                padding: EdgeInsets.only(
                                  left: 2,
                                  top: 10,
                                  bottom: 5,
                                ),
                                child: Text(
                                  'Data de inicio',
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontWeight: FontWeight.w400,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                              AppTextFormField(
                                hintText: 'Data de início',
                                controller: TextEditingController(
                                  text: mesAnoToText(
                                    _curriculumController
                                        .formacaoToSave!.mesInicio,
                                    _curriculumController
                                        .formacaoToSave!.anoInicio,
                                  ),
                                ),
                                onTap: () async {
                                  DateTime? newDate =
                                      await controller.selectDate(
                                    context,
                                    'Data de início'.i18n,
                                    mesAnoToDate(
                                      _curriculumController
                                          .formacaoToSave!.mesInicio,
                                      _curriculumController
                                          .formacaoToSave!.anoInicio,
                                    ),
                                  );
                                  if (newDate != null) {
                                    setState(() {
                                      _curriculumController.formacaoToSave!
                                          .mesInicio = newDate.month.toString();
                                      _curriculumController.formacaoToSave!
                                          .anoInicio = newDate.year.toString();
                                    });
                                  }
                                },
                                readOnly: true,
                                textInputAction: TextInputAction.next,
                                validator: (value) {
                                  if (value!.isEmpty) {
                                    return 'Campo obrigatório'.i18n;
                                  }

                                  return null;
                                },
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Padding(
                                padding: EdgeInsets.only(
                                  left: 2,
                                  top: 10,
                                  bottom: 5,
                                ),
                                child: Text(
                                  'Data de término',
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontWeight: FontWeight.w400,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                              AppTextFormField(
                                hintText: 'Data de término',
                                controller: TextEditingController(
                                  text: mesAnoToText(
                                    _curriculumController
                                        .formacaoToSave!.mesFim,
                                    _curriculumController
                                        .formacaoToSave!.anoFim,
                                  ),
                                ),
                                onTap: () async {
                                  DateTime? newDate =
                                      await controller.selectDate(
                                    context,
                                    'Data/Previsão de término'.i18n,
                                    mesAnoToDate(
                                      _curriculumController
                                          .formacaoToSave!.mesFim,
                                      _curriculumController
                                          .formacaoToSave!.anoFim,
                                    ),
                                  );
                                  if (newDate != null) {
                                    setState(() {
                                      _curriculumController.formacaoToSave!
                                          .mesFim = newDate.month.toString();
                                      _curriculumController.formacaoToSave!
                                          .anoFim = newDate.year.toString();
                                    });
                                  }
                                },
                                readOnly: true,
                                textInputAction: TextInputAction.next,
                                validator: (value) {
                                  if (value!.isEmpty) {
                                    return 'Campo obrigatório'.i18n;
                                  }

                                  return null;
                                },
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 2.0,
                        vertical: 10,
                      ),
                      child: InkWell(
                        child: Row(
                          children: [
                            CustomCheckBox(
                              tamanho: 20,
                              value: _curriculumController
                                      .formacaoToSave!.incompleto ??
                                  false,
                              onChanged: (value) {
                                _curriculumController
                                    .formacaoToSave!.incompleto = value;
                                setState(() {});
                              },
                            ),
                            const Padding(
                              padding: EdgeInsets.only(
                                bottom: 5,
                                left: 10,
                                top: 5,
                              ),
                              child: Text(
                                'Matrícula trancada',
                                style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                        onTap: () {
                          _curriculumController.formacaoToSave!.incompleto =
                              !(_curriculumController
                                      .formacaoToSave?.incompleto ??
                                  false);
                          setState(() {});
                        },
                      ),
                    ),
                    const Text(
                      'Se você trancou sua matrícula ou apenas desistiu de continuar o curso, marque a opção acima.',
                      textAlign: TextAlign.justify,
                      style: TextStyle(
                        color: Colors.grey,
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w400,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        }),
      ),
    );
  }
}
