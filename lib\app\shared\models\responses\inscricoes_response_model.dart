class InscricoesResponseModel {
  final bool sucesso;
  final String mensagem;
  final int? totalVagas;
  final int? totalResgistro;
  final int? pagina;
  final int? itensPagina;
  final List<JobModel>? dados;

  InscricoesResponseModel(
    this.totalVagas,
    this.totalResgistro,
    this.pagina,
    this.itensPagina, {
    this.sucesso = false,
    this.mensagem = "",
    this.dados,
  });

  factory InscricoesResponseModel.fromJson(Map<String, dynamic> json) {
    return InscricoesResponseModel(
      json['totalVagas'] as int?,
      json['totalResgistro'] as int?,
      json['pagina'] as int?,
      json['itensPagina'] as int?,
      sucesso: json['sucesso'] ?? false,
      mensagem: json['mensagem'] ?? "",
      dados: (json['dados'] as List<dynamic>?)
          ?.map((e) => JobModel.from<PERSON>son(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class JobModel {
  final int? id;
  final String? url;
  final String? titulo;
  final String? chamada;
  final String? data;
  final int? timestamp;
  final String? salario;
  final String? nivel;
  final String? empresa;
  final LogoModel? logo;
  final bool? pcd;
  final int? tipoRecrutamento;
  final bool? recrutamentoCego;
  final List<String>? cidades;
  final int? candidaturaID;
  final int? status;

  JobModel({
    this.id,
    this.url,
    this.titulo,
    this.chamada,
    this.data,
    this.salario,
    this.nivel,
    this.empresa,
    this.logo,
    this.pcd,
    this.tipoRecrutamento,
    this.candidaturaID,
    this.status,
    this.timestamp,
    this.recrutamentoCego,
    this.cidades,
  });

  factory JobModel.fromJson(Map<String, dynamic> json) {
    return JobModel(
      id: json['id'] as int?,
      url: json['url'] as String?,
      titulo: json['titulo'] as String?,
      chamada: json['chamada'] as String?,
      data: json['data'] as String?,
      salario: json['salario'] as String?,
      nivel: json['nivel'] as String?,
      empresa: json['empresa'] as String?,
      logo: json['logo'] == null
          ? json['logoThumb'] != null
              ? LogoModel(thumb: json['logoThumb'])
              : null
          : LogoModel.fromJson(json['logo'] as Map<String, dynamic>),
      pcd: json['pcd'] as bool?,
      tipoRecrutamento: json['tipoRecrutamento'] as int?,
      candidaturaID: json['candidaturaID'] as int?,
      status: json['status'] as int?,
      timestamp: json['timestamp'] as int?,
      recrutamentoCego: json['recrutamentoCego'] is String
          ? json['recrutamentoCego'] != "False"
          : json['recrutamentoCego'],
      cidades:
          (json['cidades'] as List<dynamic>?)?.map((e) => e as String).toList(),
    );
  }
}

class LogoModel {
  final String? thumb;

  LogoModel({this.thumb});

  factory LogoModel.fromJson(Map<String, dynamic> json) {
    return LogoModel(
      thumb: json['thumb'] as String?,
    );
  }
}
