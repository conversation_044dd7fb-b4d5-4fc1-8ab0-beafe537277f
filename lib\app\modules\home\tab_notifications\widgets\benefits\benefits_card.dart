import 'package:flutter/material.dart';

import '../../../../../shared/core/app_config.dart';

class BenefitsCard extends StatelessWidget {
  const BenefitsCard({
    super.key,
    required this.isNew,
    this.discountText,
    this.imagePath,
    this.onTap,
  });

  final bool isNew;
  final String? discountText;
  final String newText = 'novo';
  final Color cardColor = const Color(0xFFF5F5F5);
  final Color discountColor = Colors.orange;
  final String? imagePath;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 5),
        child: DecoratedBox(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(6),
            color: Colors.white,
          ),
          child: SizedBox(
            height: MediaQuery.of(context).size.width * 0.35,
            width: MediaQuery.of(context).size.width * 0.4,
            child: Column(
              children: [
                DecoratedBox(
                  decoration: const BoxDecoration(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(6),
                      topRight: Radius.circular(6),
                    ),
                    color: AppConfig.colorPrimary,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.only(
                              right: 6.0,
                              left: 6.0,
                              top: 4.0,
                            ),
                            child: Text(
                              discountText ?? '',
                              maxLines: 1,
                              style: const TextStyle(
                                color: Colors.white,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Expanded(
                  child: Image.network(
                    imagePath ?? '',
                    fit: BoxFit.cover,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
