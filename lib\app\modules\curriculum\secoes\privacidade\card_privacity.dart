import 'package:flutter/material.dart';

class CardPrivacity extends StatelessWidget {
  const CardPrivacity({
    super.key,
    this.title,
    this.description,
    this.image,
    this.onTap,
    required this.isSelected,
    this.scale,
  });

  final String? title;
  final String? description;
  final String? image;
  final Function()? onTap;
  final bool isSelected;
  final double? scale;

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: const BorderRadius.all(Radius.circular(8)),
      child: InkWell(
        onTap: onTap,
        child: ColoredBox(
          color: isSelected ? const Color(0xFF181859) : const Color(0xFF3E3EDF),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 10.0),
            child: Row(
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 20.0),
                  child: Container(
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.all(
                        Radius.circular(6),
                      ),
                    ),
                    padding: const EdgeInsets.all(4.0),
                    child: Row(
                      children: [
                        SizedBox(
                          height: 22,
                          width: 22,
                          child: Image.asset(
                            image ?? '',
                            color: const Color(0xFF1B1B63),
                            scale: scale ?? 1,
                          ),
                        ),
                        const SizedBox(width: 5),
                        Text(
                          title ?? '',
                          style: const TextStyle(
                            fontSize: 12,
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w400,
                            color: Color(0xFF1B1B63),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(
                      right: 24,
                      left: 16,
                    ),
                    child: Text(
                      description ?? '',
                      style: const TextStyle(
                        fontSize: 13,
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w400,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
