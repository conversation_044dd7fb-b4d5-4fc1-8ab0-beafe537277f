import 'package:get/get.dart';

import '../../controllers/diversities_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../../../shared/helpers/snack_bar_helper.dart';
import '../../../../shared/models/responses/listar_diversidade_response_model.dart';
import '../../../../shared/models/salvar_diversidade_model.dart';
import '../../curriculum_controller.dart';
import '../../curriculum_editing/curriculum_editing_module.dart';
import '../../widgets/curriculum_default_field.dart';
import '../secao/secao_widget.dart';

class DiversidadeWidget extends StatefulWidget {
  const DiversidadeWidget({super.key});

  @override
  State<DiversidadeWidget> createState() => _DiversidadeWidgetState();
}

class _DiversidadeWidgetState extends State<DiversidadeWidget> {
  final DiversitiesController controller = Modular.get();

  void onAddOrEdit(DiversidadeModel toSave) async {
    controller.diversidadeToSave = SalvarDiversidadeModel().copyWith(
      compartilharDadosDiversidade: toSave.marcarCheckbox,
      identidadeGenero: toSave.identidadeGenero,
      orientacaoSexual: toSave.orientacaoSexual,
      pronome: toSave.pronome,
      racaCor: toSave.racaCor,
      naoInformarDadosDiversidade: toSave.naoInformarDadosDiversidade,
    );

    var result = await Modular.to.pushNamed(
      CurriculumEditingModule.route,
      arguments: CurriculumEditingModule.diversidade,
    );
    if (result == true) {
      SnackbarHelper.showSnackbarSucesso(
        context,
        'Diversidade salva com sucesso',
      );
      Modular.get<CurriculumController>().loadDadosPessoais();
      controller.loadDiversidade();
    }
  }

  Widget _buildDetail(String title, String? value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 5),
        Text(value ?? ''),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<DiversitiesController>(
      init: controller,
      builder: (_) => SecaoWidget(
        header: 'Diversidade'.i18n,
        onEdit: () {
          onAddOrEdit(DiversidadeModel());
        },
        replaceEditWithAdd:
            controller.diversidade?.dadosNulos == false ? true : false,
        hasContent: controller.diversidade?.dadosNulos == false,
        content: Container(
          width: double.maxFinite,
          color: Colors.white,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  if (controller.diversidade?.naoInformarDadosDiversidade ==
                      true)
                    const ColoredBox(
                      color: Colors.white,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          Padding(
                            padding: EdgeInsets.symmetric(vertical: 20),
                            child: Text(
                              'Prefiro não responder',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  if (controller.diversidade?.pronomeTexto !=
                      "Prefiro não responder")
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: 10,
                        horizontal: 24,
                      ),
                      child: _buildDetail(
                        'Pronome',
                        controller.diversidade?.pronomeTexto,
                      ),
                    ),
                  if (controller.diversidade?.identidadeGeneroTexto !=
                      "Prefiro não responder")
                    SizedBox(
                      width: MediaQuery.of(context).size.width / 1.5,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          vertical: 10,
                          horizontal: 24,
                        ),
                        child: _buildDetail(
                          'Identidade de gênero',
                          controller.diversidade?.identidadeGeneroTexto,
                        ),
                      ),
                    ),
                  if (controller.diversidade?.orientacaoSexualTexto !=
                      "Prefiro não responder")
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: 10,
                        horizontal: 24,
                      ),
                      child: _buildDetail(
                        'Orientação sexual',
                        controller.diversidade?.orientacaoSexualTexto,
                      ),
                    ),
                  if (controller.diversidade?.racaCorTexto !=
                      "Prefiro não responder")
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: 10,
                        horizontal: 24,
                      ),
                      child: _buildDetail(
                        'Raça ou cor',
                        controller.diversidade?.racaCorTexto,
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
        noContent: CurriculumDefaultField(
          bottomAdd: () => onAddOrEdit(DiversidadeModel()),
          textBottom: 'Adicionar diversidade',
          description:
              'O preenchimento desta seção é opcional. As informações serão usadas em todos os processos das empresas que utilizam a solução de diversidade. Nenhum dado será utilizado como critério de desclassificação.',
        ),
      ),
    );
  }
}
