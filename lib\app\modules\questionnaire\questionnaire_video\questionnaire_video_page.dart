import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

import '../../../shared/core/app_config.dart';
import '../../../shared/widgets/app_default_button.dart';
import 'questionnaire_video_controller.dart';

class QuestionnaireVideoPage extends StatefulWidget {
  final int? tempo;

  const QuestionnaireVideoPage({super.key, this.tempo});

  @override
  _QuestionnaireVideoPageState createState() => _QuestionnaireVideoPageState();
}

class _QuestionnaireVideoPageState extends State<QuestionnaireVideoPage> {
  final controller = Modular.get<QuestionnaireVideoController>();

  @override
  void initState() {
    super.initState();
    controller.initialize(tempo: widget.tempo ?? 0);
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (bool? value, n) async {
        controller.cameraController?.dispose();
      },
      child: Scaffold(
        backgroundColor: Colors.black,
        body: SafeArea(
          child: Observer(
            builder: (_) => Visibility(
              visible: !controller.inicializado,
              replacement: Stack(
                children: [
                  Column(
                    children: [
                      Expanded(
                        child: CameraPreview(controller.cameraController!),
                      ),
                      Container(
                        padding: const EdgeInsets.all(15),
                        child: Column(
                          children: [
                            Text(
                              controller.tempo,
                              style: const TextStyle(
                                fontWeight: FontWeight.w500,
                                color: Colors.white,
                                fontSize: 22,
                              ),
                            ),
                            const SizedBox(
                              height: 5,
                            ),
                            AppDefaultButton(
                              title: const Text(
                                'PARAR GRAVAÇÃO',
                                style: TextStyle(color: Colors.white),
                              ),
                              onPressed: () async {
                                await controller.stop();
                                setState(() {});
                              },
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                  Visibility(
                    visible: controller.contagem > 0,
                    child: Positioned(
                      top: 0,
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: Container(
                        color: Colors.black.withValues(alpha: 0.7),
                        child: Center(
                          child: Text(
                            controller.contagem.toString(),
                            style: const TextStyle(
                              fontSize: 90,
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    ),
                  )
                ],
              ),
              child: Container(
                color: Colors.white,
                child: const Center(
                  child: SpinKitCircle(color: AppConfig.colorPrimary),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
