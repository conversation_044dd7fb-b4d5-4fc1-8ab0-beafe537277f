class RedeSocialResponseModel {
  final bool sucesso;
  final String mensagem;
  final List<RedeSocialModel>? dados;

  RedeSocialResponseModel(
      {this.sucesso = false, this.mensagem = "", this.dados});

  factory RedeSocialResponseModel.fromJson(Map<String, dynamic> json) {
    return RedeSocialResponseModel(
      sucesso: json['sucesso'] ?? false,
      mensagem: json['mensagem'] ?? "",
      dados: (json['dados'] as List<dynamic>?)
          ?.map((e) => RedeSocialModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class RedeSocialModel {
  final int? id;
  final int? pessoaID;
  final int? redeSocialID;
  final String? url;
  final String? urlRedirect;
  final String? icone;
  final String? fonte;

  RedeSocialModel({
    this.id,
    this.pessoaID,
    this.redeSocialID,
    this.url,
    this.urlRedirect,
    this.icone,
    this.fonte,
  });

  factory RedeSocialModel.fromJson(Map<String, dynamic> json) {
    return RedeSocialModel(
      id: json['id'] as int?,
      pessoaID: json['pessoaID'] as int?,
      redeSocialID: json['redeSocialID'] as int?,
      url: json['url'] as String?,
      urlRedirect: json['urlRedirect'] as String?,
      icone: json['icone'] as String?,
      fonte: json['fonte'] as String?,
    );
  }
}
