class QuestionarioPendenteResponseModel {
  final bool sucesso;
  final String mensagem;
  final List<dynamic>? questionarios;

  QuestionarioPendenteResponseModel(
      {this.sucesso = false, this.mensagem = "", this.questionarios});

  factory QuestionarioPendenteResponseModel.fromJson(
      Map<String, dynamic> json) {
    return QuestionarioPendenteResponseModel(
      sucesso: json['sucesso'] ?? false,
      mensagem: json['mensagem'] ?? "",
      questionarios: json['questionarios'] as List<dynamic>?,
    );
  }
}
