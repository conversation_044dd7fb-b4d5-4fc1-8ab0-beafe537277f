import 'package:flutter_modular/flutter_modular.dart';

import 'pages/logs_lgpd_page.dart';
import 'pages/logs_login_page.dart';
import 'security_and_lgpd_controller.dart';
import 'security_and_lgpd_page.dart';

class SecurityAndLGPDModule extends Module {
  static const route = '/security-and-lgpd';

  @override
  void binds(i) {
    i.addLazySingleton(SecurityAndLGPDController.new);
  }

  @override
  void routes(r) {
    r.child(
      '/',
      child: (context) => const SecurityAndLGPDPage(),
      transition: TransitionType.noTransition,
    );

    r.child(
      LogsLgpdPage.route,
      child: (context) => const LogsLgpdPage(),
      transition: TransitionType.noTransition,
    );

    r.child(
      LogsLoginPage.route,
      child: (context) => const LogsLoginPage(),
      transition: TransitionType.noTransition,
    );
  }
}
