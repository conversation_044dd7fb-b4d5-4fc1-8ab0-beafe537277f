import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../../shared/widgets/app_default_button.dart';
import '../../../shared/widgets/app_text_form_field.dart';
import '../../home/<USER>';
import '../forgot_password_controller.dart';
import 'header_recovery_password.dart';
import 'widget_custom_toaster.dart';

class WidgetAccessRecovery extends StatefulWidget {
  static const route = '/access_recovery';

  const WidgetAccessRecovery({
    super.key,
  });

  @override
  State<WidgetAccessRecovery> createState() => _WidgetAccessRecoveryState();
}

class _WidgetAccessRecoveryState extends State<WidgetAccessRecovery> {
  final controller = Modular.get<ForgotPasswordController>();

  final newPassword = TextEditingController();
  final confirmNewPassword = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const HeaderRecoveryPassword(
            bottomBack: false,
            title: 'Recuperação de acesso',
          ),
          Padding(
            padding: const EdgeInsets.only(
              left: 24.0,
              right: 25.0,
              bottom: 32.0,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    const Text(
                      'Nova senha',
                      style: TextStyle(
                        fontSize: 14,
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w400,
                        color: Color(0xff161519),
                      ),
                    ),
                    const SizedBox(height: 10),
                    Observer(
                      builder: (control) {
                        return AppTextFormField(
                          controller: newPassword,
                          hintText: 'Digite uma nova senha',
                          isPassword: true,
                          radius: 6,
                        );
                      },
                    ),
                  ],
                ),
                const SizedBox(
                  height: 16,
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    const Text(
                      'Confirme sua nova senha',
                      style: TextStyle(
                        fontSize: 14,
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w400,
                        color: Color(0xff161519),
                      ),
                    ),
                    const SizedBox(height: 10),
                    Observer(
                      builder: (control) {
                        return AppTextFormField(
                          controller: confirmNewPassword,
                          hintText: 'Sua senha',
                          isPassword: true,
                          radius: 6,
                        );
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          Padding(
            padding: const EdgeInsets.only(
              left: 24.0,
              right: 24.0,
              bottom: 16,
            ),
            child: AppDefaultButton(
              title: Text(
                'Alterar senha'.i18n,
                style: const TextStyle(
                  fontSize: 16,
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w400,
                ),
              ),
              onPressed: () async {
                final response = await controller.newPassword(
                  newPassword.text.trim(),
                  confirmNewPassword.text.trim(),
                );

                if (response.sucesso) {
                  controller.reset();
                  Modular.to.navigate(HomeModule.route);
                } else {
                  WidgetCustomToaster(
                    context: context,
                    message: response.mensagem,
                    borderRadius: 4,
                    duration: const Duration(seconds: 6),
                  ).show();
                }
              },
            ),
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }
}
