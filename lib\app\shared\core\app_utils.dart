import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shimmer/shimmer.dart';
import 'package:timeago/timeago.dart' as package_timeago;
import 'package:uuid/uuid.dart';

import 'app_translation.i18n.dart';

final meses = [
  'janeiro'.i18n,
  'fevereiro'.i18n,
  'março'.i18n,
  'abril'.i18n,
  'maio'.i18n,
  'junho'.i18n,
  'julho'.i18n,
  'agosto'.i18n,
  'setembro'.i18n,
  'outubro'.i18n,
  'novembro'.i18n,
  'dezembro'.i18n
];

String formatSeconds(int s) {
  var tm = Duration(seconds: s).toString().split('.').first.split(":");
  return '${tm[1]}:${tm[2]}';
}


Color hexToColor(String code) {
  return Color(int.parse(code.substring(1, 7), radix: 16) + 0xFF000000);
}

String removeAllHtmlTags(String? htmlText,
    {bool replaceWhitespacesAndBreaklines = true}) {
  if (htmlText == null) return '';

  if (replaceWhitespacesAndBreaklines) {
    htmlText = htmlText.replaceAll(RegExp(r"&nbsp;"), ' ');
    htmlText = htmlText.replaceAll(RegExp(r"<p>"), '');
    htmlText = htmlText.replaceAll(RegExp(r"<i>|</i>"), '');
    htmlText = htmlText.replaceAll(RegExp(r"<br>|</p>"), '\n');
  }

  RegExp exp = RegExp(r"<[^>]*>", multiLine: true, caseSensitive: true);

  return htmlText.replaceAll(exp, '');
}

String getMes(int mes) => meses[mes - 1];

class ShimmerJobPage extends StatelessWidget {
  const ShimmerJobPage({super.key});

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: Colors.white,
      child: ListView.separated(
        itemBuilder: (_, i) {
          return const ShimmerJobPageItem();
        },
        separatorBuilder: (_, i) => const Divider(),
        itemCount: 5,
      ),
    );
  }
}

class ShimmerJobPageItem extends StatelessWidget {
  const ShimmerJobPageItem({super.key});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: const Padding(
        padding: EdgeInsets.all(15),
        child: Row(
          children: <Widget>[
            ColoredBox(
              color: Colors.white,
              child: SizedBox(width: 62, height: 62),
            ),
            SizedBox(width: 20),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  ColoredBox(
                    color: Colors.white,
                    child: SizedBox(width: double.maxFinite, height: 14),
                  ),
                  SizedBox(height: 20),
                  ColoredBox(
                    color: Colors.white,
                    child: SizedBox(width: double.maxFinite, height: 10),
                  ),
                  SizedBox(height: 10),
                  ColoredBox(
                    color: Colors.white,
                    child: SizedBox(width: double.maxFinite, height: 10),
                  ),
                  ColoredBox(
                    color: Colors.white,
                    child: SizedBox(height: 10, width: double.maxFinite),
                  ),
                  ColoredBox(
                    color: Colors.white,
                    child: SizedBox(width: double.maxFinite, height: 10),
                  ),
                  ColoredBox(
                    color: Colors.white,
                    child: SizedBox(width: double.maxFinite, height: 10),
                  ),
                  ColoredBox(
                    color: Colors.white,
                    child: SizedBox(width: double.maxFinite, height: 10),
                  ),
                  ColoredBox(
                    color: Colors.white,
                    child: SizedBox(width: double.maxFinite, height: 10),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}

class AppUtils {
  static AppUtils? _instance;

  final _scaffoldKey = GlobalKey<ScaffoldState>();

  GlobalKey<ScaffoldState> get scaffoldKey => _scaffoldKey;

  AppUtils._() {
    package_timeago.setLocaleMessages('pt_br', package_timeago.PtBrMessages());
    package_timeago.setLocaleMessages(
        'pt_pt', package_timeago.PtBrShortMessages());
    package_timeago.setLocaleMessages(
        'en_us', package_timeago.EnShortMessages());
    package_timeago.setLocaleMessages(
        'es_es', package_timeago.EsShortMessages());
  }

  static AppUtils? get instance {
    _instance ??= AppUtils._();
    return _instance;
  }

  String timeago(DateTime date) {
    return package_timeago.format(date, locale: 'pt_br');
  }

  DateTime? parseDate(String value) {
    try {
      return DateFormat('yyyy-MM-dd hh:mm').parse(value);
    } catch (e) {
      return DateTime.tryParse(value);
    }
  }

  showSnackBar(
    BuildContext context, {
    String? title,
    String? message,
    Color color = Colors.orangeAccent,
    Widget? child,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        duration: const Duration(seconds: 5),
        backgroundColor: color,
        content: SizedBox(
          height: 50,
          child: child ??
              Column(
                children: <Widget>[
                  Visibility(
                    visible: title != null,
                    child: Text(
                      title?.i18n ?? '',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Text(
                    message!.i18n,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
        ),
      ),
    );
  }
}

const umMb = 1048576;

Future<List<File>> splitFile(File fileToSplit,
    {int partSize = 2 * umMb}) async {
  String tempDir = (await getTemporaryDirectory()).path;

  int fileSize = fileToSplit.lengthSync();

  ByteBuffer buffer = fileToSplit.readAsBytesSync().buffer;

  String format = fileToSplit.path.split('/').last.split('.').last;

  List<File> files = [];

  int offsetInBytes = 0;
  while (fileSize > 0) {
    var part = File('$tempDir/${const Uuid().v1()}.$format')
      ..writeAsBytesSync(buffer.asInt8List(
          offsetInBytes, fileSize >= partSize ? partSize : fileSize));
    files.add(part);
    offsetInBytes += partSize;
    fileSize -= partSize;
  }

  return files;
}

Future<File> createTempFile(String sufix) async {
  String dir = (await getTemporaryDirectory()).path;
  return File('$dir/${const Uuid().v1()}.$sufix');
}
