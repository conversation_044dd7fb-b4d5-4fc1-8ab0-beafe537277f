import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../../../../shared/core/app_utils.dart';
import '../../../../../shared/widgets/app_dropdown_search.dart';
import '../../../../../shared/models/salvar_experiencia_model.dart';
import '../../../../../shared/widgets/app_text_form_field.dart';
import '../../../controllers/experience_controller.dart';
import '../../curriculum_editing_controller.dart';
import '../../widgets/secao.dart';

class Experiencia extends StatefulWidget {
  const Experiencia({super.key, required this.formKey});

  final GlobalKey<FormState> formKey;

  @override
  State<Experiencia> createState() => _ExperienciaState();
}

class _ExperienciaState extends State<Experiencia> {
  final ExperienceController _curriculumController = Modular.get();
  final controller = Modular.get<CurriculumEditingController>();

  String mesAnoToText(String? mes, String? ano) {
    if (mes == null || mes == '' || ano == null || ano == '') return '';

    mes = getMes(int.parse(mes)).substring(0, 3);
    mes = mes[0].toUpperCase() + mes.substring(1);
    return "$mes/$ano";
  }

  DateTime? mesAnoToDate(String? mes, String? ano) {
    if (mes == null || mes == '' || ano == null || ano == '') return null;

    return DateTime(int.parse(ano), int.parse(mes));
  }

  Map<String, int>? monthDic = {
    'Jan': 1,
    'Fev': 2,
    'Mar': 3,
    'Abr': 4,
    'Mai': 5,
    'Jun': 6,
    'Jul': 7,
    'Ago': 8,
    'Set': 9,
    'Out': 10,
    'Nov': 11,
    'Dez': 12
  };

  String? validateDate(String? value) {
    if (value == null || value.isEmpty) {
      return 'Campo obrigatório'.i18n;
    }

    List<String> parts = value.split('/');
    if (parts.length < 2) {
      return "Data inválida".i18n;
    }

    String monthString = parts[0];
    String yearString = parts[1];

    int? month = monthDic![monthString];
    if (month == null) {
      return "Mês inválido".i18n;
    }

    int year;
    try {
      year = int.parse(yearString);
    } catch (e) {
      return "Ano inválido".i18n;
    }

    DateTime selectDate = DateTime(year, month);
    DateTime currentDate = DateTime.now();

    if (currentDate.compareTo(selectDate) < 0) {
      return "Data inválida".i18n;
    }

    return null;
  }

  String? validateExperienceDate(String? value, bool isCurrent) {
    if (isCurrent) {
      if (value != null && value.isNotEmpty) {
        return 'Este é seu emprego atual, a data de saída não pode ser selecionada'
            .i18n;
      }
    } else {
      return validateDate(value);
    }

    return null;
  }

  final cargoFocus = FocusNode();

  @override
  void dispose() {
    super.dispose();

    cargoFocus.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Garantir que experienciaToSave não seja nulo
    _curriculumController.experienciaToSave ??=
        SalvarExperienciaModel(atual: false, voluntario: false);

    return Secao(
      fields: Form(
        key: widget.formKey,
        child: Observer(
          builder: (_) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Cargo'.i18n,
                  style: const TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 5),
                AppTextFormField(
                  hintText: "Cargo",
                  focusNode: cargoFocus,
                  controller: TextEditingController(
                    text: _curriculumController.experienciaToSave!.cargo,
                  ),
                  onChanged: (value) {
                    _curriculumController.experienciaToSave!.cargo = value;
                  },
                  validator: (value) {
                    if (value!.isEmpty) return 'Campo obrigatório'.i18n;

                    return null;
                  },
                ),
                const SizedBox(height: 10),
                Text(
                  'Voluntariado'.i18n,
                  style: const TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                    color: Colors.black,
                  ),
                ),
                InkWell(
                  child: SizedBox(
                    height: 60,
                    child: Row(
                      children: [
                        Checkbox(
                          value: _curriculumController
                                  .experienciaToSave!.voluntario ??
                              false,
                          onChanged: (value) {
                            _curriculumController
                                .experienciaToSave!.voluntario = value;
                            setState(() {});
                          },
                        ),
                        const Text(
                          'ESSE É UM TRABALHO VOLUNTÁRIO',
                          style: TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.w500,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                  onTap: () {
                    _curriculumController.experienciaToSave!.voluntario =
                        !(_curriculumController.experienciaToSave?.voluntario ??
                            false);
                    setState(() {});
                  },
                ),
                const SizedBox(height: 10),
                Text(
                  'Empresa'.i18n,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontFamily: 'Inter',
                    fontSize: 14,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 5),
                AppTextFormField(
                  hintText: "Empresa",
                  controller: TextEditingController(
                    text: _curriculumController.experienciaToSave!.empresa,
                  ),
                  onChanged: (value) {
                    _curriculumController.experienciaToSave!.empresa = value;
                  },
                  validator: (value) {
                    if (value!.isEmpty) return 'Campo obrigatório'.i18n;

                    return null;
                  },
                ),
                const SizedBox(height: 10),
                Text(
                  'Porte'.i18n,
                  style: const TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(
                  height: 5,
                ),
                DropdownSearch(
                  context,
                  hintText: 'Porte',
                  title: 'Porte'.i18n,
                  items: _curriculumController.portesEmpresa
                      .map((e) => DropdownSearchItem(
                            value: e.id,
                            searchKey: e.descricao!,
                            text: e.descricao,
                            child: Text(e.descricao!),
                          ))
                      .toList(),
                  onSelected: (dynamic item) {
                    if (item != null) {
                      _curriculumController.experienciaToSave!.porte = item;
                    }
                  },
                  value: _curriculumController.experienciaToSave?.porte,
                ),
                const SizedBox(height: 10),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Expanded(
                      child: AppTextFormField(
                        hintText: 'Data de entrada',
                        controller: TextEditingController(
                          text: mesAnoToText(
                            _curriculumController.experienciaToSave!.mesI,
                            _curriculumController.experienciaToSave!.anoI,
                          ),
                        ),
                        onTap: () async {
                          DateTime? newDate = await controller.selectDate(
                            context,
                            'Data de entrada'.i18n,
                            mesAnoToDate(
                              _curriculumController.experienciaToSave!.mesI,
                              _curriculumController.experienciaToSave!.anoI,
                            ),
                          );
                          if (newDate != null) {
                            _curriculumController.experienciaToSave!.mesI =
                                newDate.month.toString();

                            _curriculumController.experienciaToSave!.anoI =
                                newDate.year.toString();

                            setState(() {});
                          }
                        },
                        readOnly: true,
                        textInputAction: TextInputAction.next,
                        validator: (value) {
                          if (value!.isEmpty) return 'Campo obrigatório'.i18n;

                          List<String> qualquer = value.split('/');

                          int mes = monthDic![qualquer[0]] ?? 0;
                          int ano = int.parse(qualquer[1]);

                          DateTime selectDate = DateTime(ano, mes);

                          DateTime dataAtual = DateTime.now();

                          if (dataAtual.compareTo(selectDate) < 0) {
                            return "Data inválida".i18n;
                          }

                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 10),
                    Expanded(
                      child: AppTextFormField(
                        hintText: 'Data de saída',
                        enabled:
                            !(_curriculumController.experienciaToSave?.atual ??
                                false),
                        controller: TextEditingController(
                            text: !(_curriculumController
                                        .experienciaToSave?.atual ??
                                    false)
                                ? mesAnoToText(
                                    _curriculumController
                                        .experienciaToSave!.mesF,
                                    _curriculumController
                                        .experienciaToSave!.anoF,
                                  )
                                : ''),
                        onTap: () async {
                          DateTime? newDate = await controller.selectDate(
                            context,
                            'Data de saída'.i18n,
                            mesAnoToDate(
                              _curriculumController.experienciaToSave!.mesF,
                              _curriculumController.experienciaToSave!.anoF,
                            ),
                          );
                          if (newDate != null) {
                            _curriculumController.experienciaToSave!.mesF =
                                newDate.month.toString();

                            _curriculumController.experienciaToSave!.anoF =
                                newDate.year.toString();
                            setState(() {});
                          }
                        },
                        readOnly: true,
                        textInputAction: TextInputAction.next,
                        validator: (value) {
                          return validateExperienceDate(
                            value,
                            _curriculumController.experienciaToSave!.atual!,
                          );
                        },
                      ),
                    ),
                  ],
                ),
                InkWell(
                  child: SizedBox(
                    height: 60,
                    child: Row(
                      children: [
                        Checkbox(
                          value:
                              _curriculumController.experienciaToSave!.atual ??
                                  false,
                          onChanged: (value) {
                            _curriculumController.experienciaToSave!.atual =
                                value;

                            if (_curriculumController
                                .experienciaToSave!.atual!) {
                              _curriculumController.experienciaToSave!.mesF =
                                  null;
                              _curriculumController.experienciaToSave!.anoF =
                                  null;
                            }

                            setState(() {});
                          },
                        ),
                        const Text(
                          'Esse é meu emprego atual',
                          style: TextStyle(
                            color: Colors.grey,
                            fontSize: 13,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  onTap: () {
                    _curriculumController.experienciaToSave!.atual =
                        !(_curriculumController.experienciaToSave?.atual ??
                            false);

                    if (_curriculumController.experienciaToSave!.atual!) {
                      _curriculumController.experienciaToSave!.mesF = null;
                      _curriculumController.experienciaToSave!.anoF = null;
                    }

                    setState(() {});
                  },
                ),
                const SizedBox(height: 10),
                Text(
                  'Cidade/Estado/Pais'.i18n,
                  style: const TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 5),
                AppTextFormField(
                  hintText: 'Cidade/Estado/Pais',
                  controller: TextEditingController(
                    text: _curriculumController.experienciaToSave!.cidade,
                  ),
                  onChanged: (value) {
                    _curriculumController.experienciaToSave!.cidade = value;
                  },
                  validator: (value) {
                    if (value!.isEmpty) return 'Campo obrigatório'.i18n;

                    return null;
                  },
                ),
                const SizedBox(height: 10),
                Text(
                  'Atividades realizadas'.i18n,
                  style: const TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  'Descreva o que você realizava ou realiza na empresa'.i18n,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(
                  height: 5,
                ),
                AppTextFormField(
                  hintText: 'Atividades realizadas',
                  minLines: 5,
                  maxLines: 10,
                  maxLength: 1500,
                  controller: TextEditingController(
                    text: _curriculumController.experienciaToSave!.descricao,
                  ),
                  onChanged: (value) {
                    _curriculumController.experienciaToSave!.descricao = value;
                  },
                  validator: (value) {
                    if (value!.isEmpty) return 'Campo obrigatório'.i18n;

                    return null;
                  },
                ),
                const SizedBox(height: 10),
                Text(
                  'Resultados gerados'.i18n,
                  style: const TextStyle(
                    fontSize: 14,
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  'Descreva os resultados que você gerou para a empresa'.i18n,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 5),
                AppTextFormField(
                  hintText: 'Resultados gerados',
                  minLines: 5,
                  maxLines: 10,
                  maxLength: 1500,
                  controller: TextEditingController(
                    text: _curriculumController.experienciaToSave!.resultado,
                  ),
                  onChanged: (value) {
                    _curriculumController.experienciaToSave!.resultado = value;
                  },
                  validator: (_) => null,
                ),
                const SizedBox(height: 30),
              ],
            );
          },
        ),
      ),
    );
  }
}
