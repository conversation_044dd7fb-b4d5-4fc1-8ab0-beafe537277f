import 'package:dio/dio.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../core/app_config.dart';
import '../core/app_rest.dart';
import '../models/esqueci_senha_model.dart';
import '../models/responses/alterar_senha_email_response_model.dart';
import '../models/responses/iniciar_questionario_response_model.dart';
import '../models/responses/login_response_model.dart';
import '../models/responses/verificar_senha_response_model.dart';
import '../models/verificar_resposta_model.dart';

class ForgotPasswordRepository {
  final AppRest _rest = Modular.get();

  Future<VerificarSenhaResponseModel> postVerificarSenha(
    EsqueciSenhaModel esqueciSenhaModel,
  ) async {
    try {
      var response = await _rest.post(
        '/candidato/verificar-senha',
        data: esqueciSenhaModel.toJson(),
      );

      return VerificarSenhaResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return VerificarSenhaResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return VerificarSenhaResponseModel.fromJson(err.response!.data);
    }
  }

  Future<AlterarSenhaEmailResponseModel> postAlterarSenhaEmail(
    String? token,
  ) async {
    try {
      var response = await _rest.post(
        '/candidato/alterar-senha-email/$token',
      );

      return AlterarSenhaEmailResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return AlterarSenhaEmailResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return AlterarSenhaEmailResponseModel.fromJson(err.response!.data);
    }
  }

  Future<LoginResponseModel> newPassword(
    String newPassword,
    String confirmNewPassword,
    String hash,
  ) async {
    try {
      var response = await Dio().post(
        '${AppConfig.siteUrl}/pt-br/Login/NovaSenha',
        data: FormData.fromMap({
          "nova-senha": newPassword,
          "confirmar-senha": confirmNewPassword,
          "hash": hash,
        }),
      );

      return LoginResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return LoginResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return LoginResponseModel.fromJson(err.response!.data);
    }
  }

  Future<IniciarQuestionarioResponseModel> postIniciarQuestionario(
    String? token,
  ) async {
    try {
      final response = await _rest.post(
        '/candidato/iniciar-questionario/$token',
      );
      return IniciarQuestionarioResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return IniciarQuestionarioResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return IniciarQuestionarioResponseModel.fromJson(err.response!.data);
    }
  }

  Future<IniciarQuestionarioResponseModel> postVerificarResposta(
    VerificarRespostaModel verificarRespostaModel,
  ) async {
    try {
      final response = await _rest.post(
        '/candidato/verificar-resposta',
        data: verificarRespostaModel.toJson(),
      );

      return IniciarQuestionarioResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return IniciarQuestionarioResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return IniciarQuestionarioResponseModel.fromJson(err.response!.data);
    }
  }
}
