import 'package:flutter_modular/flutter_modular.dart';
import 'package:mobx/mobx.dart';

import '../../shared/models/responses/job_details_response_model.dart';
import '../../shared/models/responses/verificar_inscricao_response_model.dart';
import '../../shared/repositories/job_repository.dart';

part 'confirm_subscription_controller.g.dart';

class ConfirmSubscriptionController = _ConfirmSubscriptionBase
    with _$ConfirmSubscriptionController;

abstract class _ConfirmSubscriptionBase with Store {
  final JobRepository _jobRepository = Modular.get();

  @observable
  JobDetailsResponseModel? _jobDetails;
  JobDetailsResponseModel? get jobDetails => _jobDetails;

  @observable
  List<int?> _cidadesInteresse = <int>[].asObservable();
  List<int?> get cidadesInteresse => _cidadesInteresse;

  @action
  setJobDetails(value) => _jobDetails = value;

  @action
  addCidadeInteresse(int? cidadeId) => _cidadesInteresse.add(cidadeId);

  @action
  removeCidadeInteresse(int? cidadeId) => _cidadesInteresse.remove(cidadeId);

  @action
  Future<VerificarInscricaoResponseModel> confirmarInscricao() async {
    return await _jobRepository.postJobConfirmarInscricao(
      _jobDetails?.id,
      _cidadesInteresse,
    );
  }
}
