import 'package:get/get.dart';

import '../../curriculum_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../../shared/core/app_translation.i18n.dart';
import '../../../../shared/core/app_utils.dart';
import '../../../../shared/helpers/snack_bar_helper.dart';
import '../../../../shared/models/responses/listar_complementar_response_model.dart';
import '../../../../shared/models/salvar_complementar_model.dart';
import '../../../../shared/widgets/app_add_info.dart';
import '../../../../shared/widgets/app_badge.dart';
import '../../controllers/complementary_controller.dart';
import '../../curriculum_editing/curriculum_editing_module.dart';
import '../secao/secao_widget.dart';

class InformacoesComplementaresWidget extends StatefulWidget {
  const InformacoesComplementaresWidget({super.key});

  @override
  _InformacoesComplementaresWidgetState createState() =>
      _InformacoesComplementaresWidgetState();
}

class _InformacoesComplementaresWidgetState
    extends State<InformacoesComplementaresWidget> {
  final ComplementaryController controller = Modular.get();
  void onAddOrEdit() async {
    controller.complementarToSave = SalvarComplementarModel(
      curriculoAdicional:
          controller.complementares?.map((c) => c.id).toList() ?? [],
      filhos:
          (Modular.get<CurriculumController>().pessoaSeguro.filhosID ?? -1) >=
                      0 &&
                  (Modular.get<CurriculumController>().pessoaSeguro.filhosID ??
                          -1) <=
                      6
              ? Modular.get<CurriculumController>().pessoaSeguro.filhosID ?? 0
              : 0,
    );
    var result = await Modular.to.pushNamed(
      CurriculumEditingModule.route,
      arguments: CurriculumEditingModule.informacoesComplementares,
    );
    if (result == true) {
      SnackbarHelper.showSnackbarSucesso(
        context,
        'Informações Complementares salvas com sucesso',
      );
      Modular.get<CurriculumController>().loadDadosPessoais();
      controller.loadInformacoesComplementares();
    }
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ComplementaryController>(
      init: controller,
      builder: (_) => _buildWidget(),
    );
  }

  Widget _buildWidget() {
    List<String?> complementares = [];
    if (Modular.get<CurriculumController>().pessoaSeguro.filhosID != null) {
      complementares
          .add(Modular.get<CurriculumController>().pessoaSeguro.filhos ?? '');
    }
    String habilitacao = 'Habilitação '.i18n;
    for (var c in controller.complementares ?? []) {
      if (c.id == ComplementarModel.habA ||
          c.id == ComplementarModel.habB ||
          c.id == ComplementarModel.habC ||
          c.id == ComplementarModel.habD ||
          c.id == ComplementarModel.habE) {
        habilitacao += '${removeAllHtmlTags(c.textoSeguro)}, ';
      } else {
        complementares.add(removeAllHtmlTags(c.textoSeguro));
      }
    }
    if (habilitacao != 'Habilitação '.i18n) {
      habilitacao = habilitacao.substring(0, habilitacao.length - 2);
      complementares.add(habilitacao);
    }

    return SecaoWidget(
      header: 'Informações adicionais'.i18n,
      hasContent: complementares.isNotEmpty,
      showEdit: true,
      replaceEditWithAdd: true,
      onEdit: onAddOrEdit,
      content: ColoredBox(
        color: Colors.white,
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 24.0,
            vertical: 15.0,
          ),
          child: Align(
            alignment: Alignment.centerLeft,
            child: Wrap(
              children: complementares.map((c) {
                return SizedBox(
                  height: 35,
                  child: AppBadge(title: c),
                );
              }).toList(),
            ),
          ),
        ),
      ),
      noContent: AppAddInfo(
        title: 'Informações adicionais que também são importantes'.i18n,
        description:
            'Quantidade de filhos, disponibilidade para viajar e mudar de residência, habilitação, etc'
                .i18n,
        btnAddTitle: 'Adicionar informações'.i18n,
        onAdd: onAddOrEdit,
      ),
    );
  }
}
