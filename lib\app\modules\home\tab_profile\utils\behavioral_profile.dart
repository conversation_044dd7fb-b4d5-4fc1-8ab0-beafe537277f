import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../../../shared/core/app_config.dart';
import '../../../../shared/repositories/personality_test_repository.dart';

class BehavioralProfile extends StatefulWidget {
  static const route = '/behavioral_profile';

  const BehavioralProfile({super.key});

  @override
  State<BehavioralProfile> createState() => _BehavioralProfileState();
}

class _BehavioralProfileState extends State<BehavioralProfile> {
  @override
  void initState() {
    super.initState();

    initWebViewController();
  }

  bool isNotFound = false;
  void setIsNotFound(bool value) {
    isNotFound = value;
    setState(() {});
  }

  bool loading = true;
  void setLoading(bool value) {
    loading = value;
    setState(() {});
  }

  WebViewController? controller;
  void initWebViewController() {
    try {
      final webviewController = WebViewController();

      webviewController.setJavaScriptMode(JavaScriptMode.unrestricted);
      webviewController.setNavigationDelegate(
        NavigationDelegate(
          onPageFinished: (v) async {
            try {
              final currentUrl = await webviewController.currentUrl();
              if (mounted) {
                setIsNotFound(currentUrl?.contains("Erros/Http404") ?? false);
                setLoading(false);
              }
            } catch (e) {
              if (mounted) {
                setIsNotFound(true);
                setLoading(false);
              }
            }
          },
        ),
      );

      talentoday().then(
        (url) {
          if (url != null && url.isNotEmpty && mounted) {
            webviewController.loadRequest(Uri.parse(url));
          } else {
            if (mounted) {
              setIsNotFound(true);
              setLoading(false);
            }
          }
        },
      ).catchError((error) {
        if (mounted) {
          setIsNotFound(true);
          setLoading(false);
        }
      });

      controller = webviewController;
      if (mounted) setState(() {});
    } catch (e) {
      if (mounted) {
        setIsNotFound(true);
        setLoading(false);
      }
    }
  }

  Future<String?> talentoday() async {
    final prepository = Modular.get<PersonalityTestRepository>();
    return await prepository.getTalentoday();
  }

  @override
  Widget build(BuildContext context) {
    // Garante que o controller é inicializado antes de renderizar
    if (controller == null && !isNotFound && !loading) {
      initWebViewController();
    }

    return Scaffold(
      appBar: AppBar(
        elevation: 1,
        toolbarHeight: 75,
        leadingWidth: 0,
        shadowColor: Colors.black38,
        leading: const SizedBox.shrink(),
        backgroundColor: Colors.white,
        title: const Text(
          'Perfil comportamental',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.w500,
            fontFamily: 'Inter',
            color: Colors.black,
          ),
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  if (mounted) {
                    Navigator.of(context).pop();
                  }
                },
                customBorder: const CircleBorder(),
                splashColor: Colors.grey.withValues(alpha: 0.6),
                child: Container(
                  padding: const EdgeInsets.all(8.0),
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.close,
                    color: Colors.black,
                    size: 36,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
      body: Visibility(
        visible: isNotFound,
        replacement: Stack(
          children: [
            if (controller != null) WebViewWidget(controller: controller!),
            Visibility(
              visible: loading,
              child: Container(
                color: Colors.white,
                child: const Center(
                  child: SpinKitCircle(
                    color: AppConfig.colorPrimary,
                  ),
                ),
              ),
            )
          ],
        ),
        child: const Center(
          child: Text(
            "Não foi possível encontrar!",
            style: TextStyle(
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }
}
