import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class IconEditableField extends StatelessWidget {
  const IconEditableField({
    super.key,
    required this.icon,
    required this.label,
    required this.hint,
    this.text,
    required this.onChange,
    this.inputFormatters,
    this.keyboardType,
  });

  final IconData icon;
  final String label;
  final String hint;
  final String? text;
  final Function onChange;
  final List<TextInputFormatter>? inputFormatters;
  final TextInputType? keyboardType;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            FaIcon(icon, size: 14),
            const SizedBox(width: 5),
            Text(label),
          ],
        ),
        TextField(
          keyboardType: keyboardType,
          inputFormatters: inputFormatters,
          controller: TextEditingController(text: text),
          decoration: InputDecoration(
            hintText: hint,
            isDense: true,
          ),
          onChanged: onChange as void Function(String)?,
        ),
        const SizedBox(height: 15),
      ],
    );
  }
}
