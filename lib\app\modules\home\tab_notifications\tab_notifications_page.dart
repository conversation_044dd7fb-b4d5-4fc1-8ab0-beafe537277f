import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:launch_app_store/launch_app_store.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../shared/app_container.dart';
import '../../../shared/core/app_config.dart';
import '../../../shared/core/app_translation.i18n.dart';
import '../../../shared/widgets/app_image_network_widget.dart';
import '../../../shared/widgets/app_usuario_card/app_usuario_card_controller.dart';
import 'tab_notifications_controller.dart';
import 'widgets/app_usuario_mod.dart';
import 'widgets/benefits/benefits_card.dart';
import 'widgets/benefits/benefits_dialog.dart';
import 'widgets/benefits/benefits_more.dart';
import 'widgets/recommendation_job_card.dart';
import 'widgets/shimmer_benefits.dart';
import 'widgets/widegt_more_articles.dart';
import 'widgets/widget_our_articles.dart';
import 'widgets/widget_quest_card.dart';

class TabNotificationsPage extends StatefulWidget {
  final Function? onSearchTap;

  const TabNotificationsPage({super.key, this.onSearchTap});

  @override
  _TabNotificationsPageState createState() => _TabNotificationsPageState();
}

class _TabNotificationsPageState extends State<TabNotificationsPage> {
  final controller = Modular.get<TabNotificationsController>();
  final appUsuarioController = Modular.get<AppUsuarioCardController>();

  final _refreshController = RefreshController(
    initialRefresh: false,
  );
  @override
  void initState() {
    super.initState();
    controller.load();
    controller.loadBeneficios();
    controller.loadArticles();
    controller.loadRecomendacoes();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Padding(
          padding: const EdgeInsets.only(left: 16.0),
          child: Image.asset(
            'lib/assets/images/logo-empregare-splash.png',
            scale: 1.8,
          ),
        ),
        elevation: 0.5,
        backgroundColor: AppConfig.white,
        actions: <Widget>[
          Observer(
            builder: (_) => Padding(
              padding: const EdgeInsets.only(
                bottom: 6.0,
                top: 6.0,
                right: 26,
              ),
              child: ClipOval(
                child: AppImageNetworkWidget(
                  height: 45,
                  width: 45,
                  fit: BoxFit.cover,
                  appUsuarioController.foto ?? '',
                  errorImage: 'lib/assets/images/person-filled.png',
                  scale: 2,
                ),
              ),
            ),
          ),
        ],
      ),
      backgroundColor: Colors.white,
      body: SmartRefresher(
        dragStartBehavior: DragStartBehavior.down,
        header: CustomHeader(
          builder: (_, mode) {
            if (mode == RefreshStatus.refreshing) {
              return const SizedBox(
                width: 50,
                child: SpinKitCircle(color: AppConfig.colorPrimary),
              );
            }

            if (mode == RefreshStatus.canRefresh) {
              return const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.arrow_downward),
                  Text('Puxe para atualizar')
                ],
              );
            }

            return const SizedBox.shrink();
          },
        ),
        controller: _refreshController,
        onRefresh: () async {
          await controller.load();
          await controller.loadRecomendacoes();
          _refreshController.refreshCompleted();
        },
        child: Observer(
          builder: (_) {
            return Stack(
              children: <Widget>[
                Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    AppUsuarioCardMod(
                      key: GlobalKey(),
                      onEdit: () {},
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                        left: 24.0,
                        top: 32,
                        bottom: 12,
                      ),
                      child: Row(
                        children: <Widget>[
                          Text(
                            'Notificações'.i18n,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Container(
                            height: 20,
                            width: 20,
                            padding: const EdgeInsets.all(2),
                            margin: const EdgeInsets.all(5),
                            decoration: BoxDecoration(
                              color: const Color(0xfff05656),
                              borderRadius: BorderRadius.circular(50),
                            ),
                            child: Center(
                              child: Text(
                                '${controller.countNotificacao}',
                                style: const TextStyle(
                                  fontSize: 10,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Visibility(
                      visible: controller.hasNewVersion,
                      child: Padding(
                        padding: const EdgeInsets.only(bottom: 16),
                        child: InkWell(
                          onTap: () {
                            LaunchReview.launch(
                              writeReview: false,
                              iOSAppId: AppConfig.kAppIos,
                            );
                          },
                          child: AppContainer(
                            color: const Color(0xFFFFF5E8),
                            width: MediaQuery.of(context).size.width,
                            child: const Row(
                              children: [
                                Icon(
                                  Icons.download,
                                  color: Color(0xFFFF762E),
                                ),
                                SizedBox(width: 7),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'nova versão disponível',
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color: Color(0xFFFF762E),
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    Text(
                                      'toque aqui para atualizar seu aplicativo.',
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color: Color(0xFFFF762E),
                                        fontSize: 12,
                                        fontWeight: FontWeight.w300,
                                      ),
                                    ),
                                  ],
                                )
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                    WidgetQuestCard(),
                    Visibility(
                      visible: controller.countNotificacao == 0,
                      child: Padding(
                        padding: const EdgeInsets.only(
                          left: 20,
                          right: 20,
                          top: 10,
                          bottom: 10,
                        ),
                        child: DecoratedBox(
                          decoration: BoxDecoration(
                            color: const Color(0xFFF5F5F5),
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: SizedBox(
                            height: 85,
                            width: MediaQuery.of(context).size.width,
                            child: const Center(
                              child: Text(
                                'Não há notificações',
                                style: TextStyle(
                                  fontWeight: FontWeight.w300,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    // Seção de Recomendações
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24.0,
                        vertical: 12.0,
                      ),
                      child: Row(
                        children: [
                          const Text(
                            'Recomendações',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontFamily: 'Inter',
                              fontSize: 18,
                            ),
                          ),
                          const Spacer(),
                          Observer(
                            builder: (_) => controller.hasMoreRecomendacoes
                                ? InkWell(
                                    onTap: () {
                                      Modular.to.pushNamed('/recommendations');
                                    },
                                    child: const SizedBox(
                                      height: 25,
                                      width: 75,
                                      child: Center(
                                        child: Text(
                                          'Ver mais',
                                          style: TextStyle(
                                            fontFamily: 'Inter',
                                            fontSize: 14,
                                            color: Color(0xff3e3edf),
                                            fontWeight: FontWeight.w400,
                                          ),
                                        ),
                                      ),
                                    ),
                                  )
                                : const SizedBox.shrink(),
                          ),
                        ],
                      ),
                    ),
                    SingleChildScrollView(
                      clipBehavior: Clip.none,
                      scrollDirection: Axis.horizontal,
                      child: Padding(
                        padding: const EdgeInsets.only(left: 20.0, right: 24),
                        child: controller.recomendacoes.isNotEmpty
                            ? Row(
                                children: List.generate(
                                  controller.recomendacoes.length,
                                  (index) {
                                    final recomendacao =
                                        controller.recomendacoes[index];
                                    return SizedBox(
                                      width: MediaQuery.of(context).size.width *
                                          0.8,
                                      child: RecommendationJobCard(
                                        job: recomendacao,
                                        showActionsInModal: false,
                                      ),
                                    );
                                  },
                                ),
                              )
                            : controller.loadingRecomendacoes
                                ? ShimmerBenefits(
                                    height: MediaQuery.of(context).size.width *
                                        0.55,
                                    width:
                                        MediaQuery.of(context).size.width * 0.7,
                                  )
                                : Container(
                                    height: 150,
                                    width:
                                        MediaQuery.of(context).size.width * 0.8,
                                    decoration: BoxDecoration(
                                      color: const Color(0xFFF5F5F5),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: const Center(
                                      child: Text(
                                        'Nenhuma recomendação disponível',
                                        style: TextStyle(
                                          fontWeight: FontWeight.w300,
                                          color: Colors.grey,
                                        ),
                                      ),
                                    ),
                                  ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24.0,
                        vertical: 12.0,
                      ),
                      child: Row(
                        children: [
                          const Text(
                            'Benefícios',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontFamily: 'Inter',
                            ),
                          ),
                          const Spacer(),
                          InkWell(
                            onTap: () {
                              showDialog(
                                context: context,
                                builder: (_) => BenefitsMore(
                                  model: controller.beneficiosDetalhes,
                                  modelPromo: controller.beneficiosPromocoes,
                                ),
                              );
                            },
                            child: const SizedBox(
                              height: 25,
                              width: 75,
                              child: Center(
                                child: Text(
                                  'Ver mais',
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontSize: 14,
                                    color: Color(0xff3e3edf),
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    SingleChildScrollView(
                      clipBehavior: Clip.none,
                      scrollDirection: Axis.horizontal,
                      child: Padding(
                        padding: const EdgeInsets.only(left: 20.0, right: 24),
                        child: controller.beneficios != null
                            ? Row(
                                children: List.generate(
                                  controller.beneficios?.dados?.length ?? 0,
                                  (index) {
                                    final beneficios =
                                        controller.beneficios?.dados?[index];
                                    return BenefitsCard(
                                      imagePath: beneficios?.logo,
                                      isNew: true,
                                      discountText: beneficios?.beneficio,
                                      onTap: () async {
                                        if (controller.loadingPromo == true) {
                                          return;
                                        }

                                        await controller
                                            .getClubeBeneficiosDetalhes(
                                          beneficios?.id ?? 0,
                                        );

                                        await controller
                                            .getClubeBeneficiosPromocoes(
                                          beneficios?.id ?? 0,
                                        );

                                        showDialog(
                                          context: context,
                                          builder: (_) => BenefitsDialog(
                                            model:
                                                controller.beneficiosDetalhes,
                                            modelPromo:
                                                controller.beneficiosPromocoes,
                                          ),
                                        );
                                      },
                                    );
                                  },
                                ),
                              )
                            : ShimmerBenefits(
                                height:
                                    MediaQuery.of(context).size.width * 0.35,
                                width: MediaQuery.of(context).size.width * 0.45,
                              ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(24.0),
                      child: Row(
                        children: [
                          const Text(
                            'Nossos artigos',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontSize: 20,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const Spacer(),
                          InkWell(
                            onTap: () {
                              showDialog(
                                context: context,
                                builder: (_) => WidgetMoreArticles(
                                  controller: controller,
                                ),
                              );
                            },
                            child: const SizedBox(
                              height: 25,
                              width: 75,
                              child: Center(
                                child: Text(
                                  'Ver mais',
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400,
                                    color: Color(0xff3e3edf),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    SingleChildScrollView(
                      clipBehavior: Clip.none,
                      scrollDirection: Axis.horizontal,
                      child: Padding(
                        padding: const EdgeInsets.only(
                          left: 24,
                          right: 24,
                        ),
                        child: controller.articles != null
                            ? Row(
                                children: [
                                  ...List.generate(
                                    controller.articles?.dados.length ?? 0,
                                    (index) {
                                      var item =
                                          controller.articles!.dados[index];

                                      return OurArticles(
                                        path: item.image,
                                        title: item.title.rendered,
                                        link: item.link,
                                        descripition: item.excerpt.rendered,
                                      );
                                    },
                                  ),
                                ],
                              )
                            : ShimmerBenefits(
                                height:
                                    MediaQuery.of(context).size.width * 0.55,
                                width: MediaQuery.of(context).size.width * 0.7,
                              ),
                      ),
                    ),
                    const SizedBox(height: 50),
                  ],
                ),
                Visibility(
                  visible: controller.loadingUrl,
                  child: Positioned(
                    left: 0,
                    right: 0,
                    top: 0,
                    bottom: 0,
                    child: Container(
                      color: Colors.black.withValues(alpha: 0.5),
                      child: const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: <Widget>[
                            SizedBox(height: 20),
                            SpinKitCircle(color: AppConfig.colorPrimary),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}
