import 'package:flutter_modular/flutter_modular.dart';
import 'package:mobx/mobx.dart';

import '../../../shared/models/responses/pessoa_response_model.dart';
import '../../../shared/repositories/curriculum_repository.dart';

part 'tab_profile_controller.g.dart';

class TabProfileController = _TabProfileControllerBase
    with _$TabProfileController;

abstract class _TabProfileControllerBase with Store {
  final CurriculumRepository _repository = Modular.get();

  @observable
  bool loadingWord = false;

  @observable
  bool loadingPdf = false;

  @observable
  bool loadingPrivacidade = false;

  @observable
  bool salvandoPrivacidade = false;

  @observable
  PrivacidadeResponseModel? _privacidade;
  PrivacidadeResponseModel? get privacidade => _privacidade;

  @observable
  int _privacy = 0;
  int get privacy => _privacy;

  @action
  setPrivacy(int value) => _privacy = value;

  @action
  Future<void> getPrivacidade() async {
    try {
      loadingPrivacidade = true;
      _privacidade = (await _repository.getDadosPessoais()).pessoa?.privacidade;
      _privacy = _privacidade?.tipo ?? 0;
    } finally {
      loadingPrivacidade = false;
    }
  }

  @action
  Future<void> salvarPrivacidade(int tipo) async {
    try {
      salvandoPrivacidade = true;
      await _repository.salvarPrivacidade(tipo);
      _privacidade = PrivacidadeResponseModel(tipo: tipo);
      _privacy = tipo;
    } finally {
      salvandoPrivacidade = false;
    }
  }

  @action
  Future<void> downloadWord(Function showMessage) async {
    try {
      loadingWord = true;
      await _repository.downloadWord();
      await Future.delayed(const Duration(seconds: 2));
    } catch (e) {
      showMessage();
    } finally {
      loadingWord = false;
    }
  }

  @action
  Future<void> downloadPdf(Function showMessage) async {
    try {
      loadingPdf = true;
      await _repository.downloadPdf();
      await Future.delayed(const Duration(seconds: 2));
    } catch (e) {
      showMessage();
    } finally {
      loadingPdf = false;
    }
  }
}
