class VerificarInscricaoResponseModel {
  final bool sucesso;
  final String mensagem;
  final int? id;
  final bool? questionario;
  final int? pessoaID;
  final String? retorno;
  final String? retornoMensagem;

  VerificarInscricaoResponseModel({
    this.sucesso = false,
    this.mensagem = "",
    this.id,
    this.questionario,
    this.pessoaID,
    this.retorno,
    this.retornoMensagem,
  });

  factory VerificarInscricaoResponseModel.fromJson(Map<String, dynamic> json) {
    return VerificarInscricaoResponseModel(
      sucesso: json['sucesso'] ?? false,
      mensagem: json['mensagem'] ?? "",
      id: json['id'] as int?,
      questionario: json['questionario'] as bool?,
      pessoaID: json['pessoaID'] as int?,
      retorno: json['retorno'] as String?,
      retornoMensagem: json['retornoMensagem'] as String?,
    );
  }
}
