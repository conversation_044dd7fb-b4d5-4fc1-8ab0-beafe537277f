import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:get/get.dart';

import '../../../../shared/core/app_translation.i18n.dart';
import '../../../../shared/helpers/snack_bar_helper.dart';
import '../../curriculum_controller.dart';
import '../../curriculum_editing/curriculum_editing_module.dart';
import '../../widgets/curriculum_default_field.dart';
import '../secao/secao_widget.dart';

class SinteseWidget extends StatefulWidget {
  const SinteseWidget({super.key});

  @override
  _SinteseWidgetState createState() => _SinteseWidgetState();
}

class _SinteseWidgetState extends State<SinteseWidget> {
  final CurriculumController controller = Modular.get();

  void onAddOrEdit() async {
    var result = await Modular.to.pushNamed(
      CurriculumEditingModule.route,
      arguments: CurriculumEditingModule.cartaApresentacao,
    );
    if (result == true) {
      SnackbarHelper.showSnackbarSucesso(context, 'Síntese salva com sucesso');
      controller.loadDadosPessoais();
    }
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CurriculumController>(
      builder: (_) => SecaoWidget(
        header: 'Síntese'.i18n,
        onEdit: onAddOrEdit,
        replaceEditWithAdd: controller.pessoa?.miniCurriculo != null,
        hasContent: controller.pessoa?.miniCurriculo != null,
        content: ColoredBox(
          color: Colors.white,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 15),
            child: SizedBox(
              width: double.maxFinite,
              child: DecoratedBox(
                decoration: BoxDecoration(
                  color: const Color(0xFFf3f3fc).withValues(alpha: 0.5),
                  borderRadius: const BorderRadius.all(
                    Radius.circular(4),
                  ),
                  border: Border.all(
                    color: const Color(0xFFdee2e6),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(22.0),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          controller.pessoa?.miniCurriculo ?? '',
                          style: const TextStyle(
                            color: Color(0xFF212529),
                            fontFamily: 'Inter',
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
        noContent: CurriculumDefaultField(
          bottomAdd: onAddOrEdit,
          textBottom: 'Adicionar síntese',
          description:
              'Descreva em poucas palavras quais resultados você conquistou em suas experiências e o que você é capaz de realizar.',
        ),
      ),
    );
  }
}
