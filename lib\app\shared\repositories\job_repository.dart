import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../core/app_rest.dart';
import '../models/job_insight_model.dart';
import '../models/job_parameter_model.dart';
import '../models/responses/job_details_response_model.dart';
import '../models/responses/job_sugestoes_response_model.dart';
import '../models/responses/verificar_inscricao_response_model.dart';

class JobRepository {
  final AppRest _rest = Modular.get();

  Future<JobSugestoesResponseModel> getSugestoes(
      {JobParameterModel? filtros}) async {
    try {
      final response = await _rest.get('/vagas/sugestoes',
          queryParameters: filtros?.toJson());
      return JobSugestoesResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return JobSugestoesResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return JobSugestoesResponseModel.fromJson(err.response!.data);
    }
  }

  Future<JobSugestoesResponseModel> getJobs({
    JobParameterModel? filtros,
  }) async {
    try {
      final response = await _rest.get(
        "/vagas/buscar",
        queryParameters: filtros?.toJson(),
      );

      return JobSugestoesResponseModel.fromJson(
        response.data,
      );
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return JobSugestoesResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return JobSugestoesResponseModel.fromJson(err.response!.data);
    }
  }

  Future<JobDetailsResponseModel> getJobDetails(int? id) async {
    try {
      final response = await _rest.get('/vagas/detalhes/$id');
      return JobDetailsResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return JobDetailsResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return JobDetailsResponseModel.fromJson(err.response!.data);
    }
  }

  Future postJobInsight({required JobInsightModel insight}) async {
    final response =
        await _rest.post('/vagas/insights', data: insight.toJson());
    return response;
  }

  Future<VerificarInscricaoResponseModel> getVerificarInscricao(
    int? vagaID,
  ) async {
    try {
      final response = await _rest.get(
        '/vagas/verificar-inscricao/$vagaID',
      );
      return VerificarInscricaoResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return VerificarInscricaoResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return VerificarInscricaoResponseModel.fromJson(err.response!.data);
    }
  }

  Future<VerificarInscricaoResponseModel> postJobConfirmarInscricao(
    int? vagaID,
    List<int?> cidadesInteresse,
  ) async {
    try {
      final response = await _rest.post(
        '/vagas/confirmar-inscricao',
        data: {
          "vagaID": vagaID,
          "cidadesInteresse": cidadesInteresse,
          "variasCidades": cidadesInteresse.length > 1,
        },
      );

      if (response.statusCode == 200) {
        return VerificarInscricaoResponseModel.fromJson(response.data);
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
        );
      }
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return VerificarInscricaoResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return VerificarInscricaoResponseModel.fromJson(err.response!.data);
    }
  }

  Future<JobSugestoesResponseModel> getRecomendacoes({int pagina = 1}) async {
    try {
      final queryParams = {
        'culture': 'pt-BR',
        'filtros[pagina]': pagina,
      };

      final response = await _rest.get('/vagas/buscar-recomendacoes',
          queryParameters: queryParams);

      // Extrair dados da estrutura aninhada {sucesso: true, model: {dados: [...]}}
      Map<String, dynamic> parsedData = response.data;
      if (parsedData.containsKey('model') &&
          parsedData['model'] is Map<String, dynamic>) {
        parsedData = parsedData['model'] as Map<String, dynamic>;
      }

      final result = JobSugestoesResponseModel.fromJson(parsedData);

      if (kDebugMode) {
        debugPrint(
            '✅ Recomendações carregadas: ${result.dados?.length ?? 0} jobs');
      }

      return result;
    } on DioException catch (err) {
      if (kDebugMode) {
        debugPrint('❌ Erro ao carregar recomendações: ${err.message}');
      }

      if (err.response?.data is! Map) {
        return JobSugestoesResponseModel.fromJson({});
      }

      return JobSugestoesResponseModel.fromJson(err.response!.data);
    }
  }
}
