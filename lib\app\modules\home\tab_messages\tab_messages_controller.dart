import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../shared/models/responses/listar_chat_response_model.dart';
import '../../../shared/repositories/message_repository.dart';
import '../../../shared/services/storage_service.dart';

class TabMessagesController with ChangeNotifier {
  final MessageRepository _repository = Modular.get();

  bool loading = false;
  void changeLoading(bool value) {
    loading = value;
    notifyListeners();
  }

  List<ChatModel>? _chatsList = [];
  List<ChatModel> chats = [];

  Future<void> load() async {
    try {
      _chatsList = List<ChatModel>.from(
          (await StorageService().get('chats'))?.map((item) {
                return ChatModel.fromJson(item);
              }) ??
              []);

      filtrar(false);

      if (_chatsList?.isEmpty ?? true) {
        changeLoading(true);
      }

      _chatsList = (await _repository.getMessages()).chats;

      filtrar(false);

      await StorageService().put(
        'chats',
        _chatsList?.map((item) => item.toJson()).toList(),
      );
    } finally {
      changeLoading(false);
    }
  }

  Future<void> filtrar(bool encerrado) async {
    chats = _chatsList?.where((e) => e.encerrado == encerrado).toList() ?? [];
    notifyListeners();
  }
}
