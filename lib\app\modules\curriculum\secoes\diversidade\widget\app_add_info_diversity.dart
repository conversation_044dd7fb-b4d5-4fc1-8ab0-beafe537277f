import 'package:flutter/material.dart';

import '../../../../../shared/core/app_config.dart';
import '../../../../../shared/core/app_translation.i18n.dart';

class AppAddInfoDiversity extends StatelessWidget {
  final String? title;
  final String? description;
  final String? btnAddTitle;
  final Function? onAdd;
  final Function? onNotAdd;

  const AppAddInfoDiversity(
      {super.key,
      this.title,
      this.description,
      this.btnAddTitle,
      this.onAdd,
      this.onNotAdd});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 7, vertical: 10),
      decoration: BoxDecoration(
        color: const Color(0xFFf3f3fc).withValues(alpha: 0.5),
        border: Border.all(color: const Color(0xFFdee2e6)),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        children: <Widget>[
          Text(
            title!,
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            description!,
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w300,
            ),
          ),
          const SizedBox(height: 10),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              mainAxisAlignment: onNotAdd != null
                  ? MainAxisAlignment.spaceBetween
                  : MainAxisAlignment.center,
              children: <Widget>[
                InkWell(
                  onTap: onAdd as void Function()?,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      const Icon(Icons.add, color: AppConfig.colorPrimary),
                      Text(
                        btnAddTitle!,
                        style: const TextStyle(
                          color: AppConfig.colorPrimary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                Visibility(
                  visible: onNotAdd != null,
                  child: InkWell(
                    onTap: onNotAdd as void Function()?,
                    child: Text(
                      'PREFIRO NÃO RESPONDER'.i18n,
                      style: const TextStyle(
                        color: Colors.grey,
                        fontWeight: FontWeight.w500,
                        fontSize: 12,
                      ),
                    ),
                  ),
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}
