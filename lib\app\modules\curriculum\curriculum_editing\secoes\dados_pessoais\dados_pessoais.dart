import 'package:dio/dio.dart';
import 'package:empregare_app/app/shared/widgets/app_text_form_field.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:i18n_extension/default.i18n.dart';
import 'package:intl/intl.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';

import '../../../../../shared/core/app_config.dart';
import '../../../../../shared/widgets/app_dropdown_search.dart';
import '../../../../../shared/widgets/helpers/int_iphone_field_mod.dart';
import '../../../curriculum_controller.dart';
import '../../curriculum_editing_controller.dart';
import '../../widgets/secao.dart';
import 'utils/profile_date_select.dart';

class DadosPessoais extends StatefulWidget {
  const DadosPessoais({super.key, required this.formKey});

  final GlobalKey<FormState> formKey;

  @override
  State<DadosPessoais> createState() => _DadosPessoaisState();
}

class _DadosPessoaisState extends State<DadosPessoais> {
  final CurriculumController _curriculumController = Modular.get();
  final controller = Modular.get<CurriculumEditingController>();

  final maskFormatterBrazil = MaskTextInputFormatter(mask: "(##) #####-####");
  final maskCepFormatterBrazil = MaskTextInputFormatter(mask: "#####-###");

  bool get isBr => _curriculumController.pessoaToSave?.paisID == "BR";

  String? initCellNumber;
  String? initTellNumber;

  final celController = TextEditingController();
  final telController = TextEditingController();
  final cepController = TextEditingController();

  final _dataNascFocusNode = FocusNode();
  late final ValueNotifier<DateTime?> _dataNascNotifier;

  @override
  void initState() {
    celController.text = getInitialCellNumber();
    telController.text = getInitiaTellNumber();
    cepController.text = getInitiaCepNumber();

    _dataNascNotifier = ValueNotifier<DateTime?>(
        _curriculumController.pessoaToSave?.dataNascimento);

    _dataNascNotifier.addListener(
      () {
        _curriculumController.pessoaToSave?.dataNascimento =
            _dataNascNotifier.value;
      },
    );

    initCellNumber = _curriculumController.pessoaToSave?.celularPaisCode!
        .replaceAll('+', '');

    initTellNumber = _curriculumController.pessoaToSave?.telefonePaisCode!
        .replaceAll('+', '');

    cepController.addListener(() {
      if (cepController.text.length < 8) return;

      Dio()
          .get("https://viacep.com.br/ws/${cepController.text}/json/")
          .then((resp) {
        final body = resp.data;

        _curriculumController.pessoaToSave?.estadoID =
            _curriculumController.estados.firstWhereOrNull((v) {
          return v.nome == body['estado'];
        })?.id;

        _curriculumController.getCidades().then((_) {
          _curriculumController.pessoaToSave?.cidadeID =
              _curriculumController.cidades.firstWhereOrNull((v) {
            return v.nome == body['localidade'];
          })?.id;

          if (mounted) setState(() {});
        });

        _curriculumController.pessoaToSave?.logradouro = body['logradouro'];
        _curriculumController.pessoaToSave?.bairro = body['bairro'];

        if (mounted) setState(() {});
      });
    });

    super.initState();
  }

  @override
  void dispose() {
    super.dispose();

    celController.dispose();
    telController.dispose();
    cepController.dispose();

    _dataNascFocusNode.dispose();
    _dataNascNotifier.dispose();
  }

  String dateToText() {
    final DateTime fourteenYears = DateTime.now().subtract(
      const Duration(days: 5110),
    );
    var formatter = DateFormat('dd/MM/yyyy');
    return formatter.format(
      _curriculumController.pessoaToSave?.dataNascimento ?? fourteenYears,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Secao(
      fields: Form(
        key: widget.formKey,
        child: GetBuilder<CurriculumController>(
          builder: (curriculumController) {
            controller.codeCel1 =
                _curriculumController.pessoaToSave?.celularPaisCode;
            controller.codeCel2 =
                _curriculumController.pessoaToSave?.telefonePaisCode;
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Padding(
                      padding: EdgeInsets.only(bottom: 4, left: 4),
                      child: Text('Nome'),
                    ),
                    AppTextFormField(
                      hintText: 'Digite seu nome completo',
                      controller: TextEditingController(
                        text: _curriculumController.pessoaToSave?.nome,
                      ),
                      onChanged: (value) {
                        _curriculumController.pessoaToSave?.nome = value;
                      },
                      enabled: true,
                      isPassword: false,
                      radius: 6,
                      validator: (value) {
                        if (value!.isEmpty) return 'Campo obrigatório'.i18n;

                        return null;
                      },
                    )
                  ],
                ),
                const SizedBox(height: 5),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Padding(
                      padding: EdgeInsets.only(bottom: 4, left: 4),
                      child: Text(
                        'Data de Nascimento',
                      ),
                    ),
                    ProfileDateSelect(
                      dateNotifier: _dataNascNotifier,
                      focusNode: _dataNascFocusNode,
                      hintTextDay: 'Dia',
                      hintTextMonth: 'Mês',
                      hintTextYear: 'Ano',
                      validatorDay: (value) {
                        if (value!.isEmpty) return 'Campo obrigatório'.i18n;
                        return null;
                      },
                      validatorMonth: (value) {
                        if (value!.isEmpty) return 'Campo obrigatório'.i18n;
                        return null;
                      },
                      validatorYear: (value) {
                        if (value!.isEmpty) return 'Campo obrigatório'.i18n;
                        return null;
                      },
                    ),
                    const SizedBox(height: 4),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Padding(
                      padding: EdgeInsets.only(bottom: 4, left: 4),
                      child: Text('Gênero'),
                    ),
                    DropdownSearch(
                      this.context,
                      hintText: 'GÊNERO'.i18n,
                      title: 'GÊNERO'.i18n,
                      items: _curriculumController.generos
                          .map((e) => DropdownSearchItem(
                                value: e.id,
                                searchKey: e.nome!,
                                text: e.nome,
                                child: Text(e.nome!),
                              ))
                          .toList(),
                      onSelected: (dynamic value) {
                        if (value != null) {
                          _curriculumController.pessoaToSave?.sexo = value;
                        }
                      },
                      value: _curriculumController.pessoaToSave?.sexo,
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Padding(
                      padding: EdgeInsets.only(bottom: 4, left: 4),
                      child: Text('Estado Civil'),
                    ),
                    DropdownSearch(
                      this.context,
                      hintText: 'ESTADO CIVIL'.i18n,
                      title: 'ESTADO CIVIL'.i18n,
                      items: _curriculumController.estadosCivis
                          .map((e) => DropdownSearchItem(
                                value: e.id,
                                searchKey: e.nome!,
                                text: e.nome,
                                child: Text(e.nome!),
                              ))
                          .toList(),
                      onSelected: (dynamic item) {
                        if (item != null) {
                          _curriculumController.pessoaToSave?.estadoCivil =
                              item;
                        }
                      },
                      value: _curriculumController.pessoaToSave?.estadoCivil,
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Padding(
                      padding: EdgeInsets.only(bottom: 4, left: 4),
                      child: Text('E-mail'),
                    ),
                    AppTextFormField(
                      hintText: 'Qual seu endereço de e-mail',
                      controller: TextEditingController(
                        text: _curriculumController.pessoaToSave?.email,
                      ),
                      textInputAction: TextInputAction.next,
                      keyboardType: TextInputType.emailAddress,
                      onChanged: (value) {
                        _curriculumController.pessoaToSave?.email = value;
                      },
                      enabled: true,
                      isPassword: false,
                      radius: 6,
                      validator: (value) {
                        if (value!.isEmpty) return 'Campo obrigatório'.i18n;

                        return null;
                      },
                    )
                  ],
                ),
                SizedBox(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Padding(
                        padding: EdgeInsets.only(bottom: 4, left: 4, top: 10),
                        child: Text('Celular'),
                      ),
                      IntlPhoneFieldMod(
                        controller: celController,
                        decoration: const InputDecoration(
                          hintText: 'Digite seu número de celular',
                          isDense: true,
                        ),
                        keyboardType: TextInputType.number,
                        maxLength: controller.codeCel1 == '+55' ? 15 : null,
                        initialCountryCode: initCellNumber,
                        initialValue: initCellNumber,
                        dropdownTextStyle: const TextStyle(fontSize: 16),
                        inputFormatters: controller.codeCel1 == '+55'
                            ? [maskFormatterBrazil]
                            : null,
                        onChanged: (number) {
                          var update =
                              number.countryCode != controller.codeCel1;
                          if (number.number.isNotEmpty) {
                            if (number.countryCode != '+55') {
                              number.number =
                                  maskFormatterBrazil.unmaskText(number.number);
                            } else {
                              number.number =
                                  maskFormatterBrazil.maskText(number.number);
                            }
                          }

                          if (update) {
                            celController.text = number.number;
                          }

                          _curriculumController.pessoaToSave?.celular =
                              number.number.isEmpty ? null : number.number;
                        },
                        onCountryChanged: (country) {
                          var dialCode = "+${country.dialCode}";
                          if (dialCode != '+55') {
                            celController.text = maskFormatterBrazil
                                .unmaskText(celController.text);
                          } else {
                            celController.text = maskFormatterBrazil
                                .maskText(celController.text);
                          }

                          controller.changeCodeCel1(dialCode);
                          _curriculumController.pessoaToSave?.celularPaisCode =
                              dialCode.isEmpty ? null : dialCode;
                        },
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Padding(
                        padding: EdgeInsets.only(bottom: 4, left: 4, top: 10),
                        child: Text('Telefone adicional'),
                      ),
                      IntlPhoneFieldMod(
                        controller: telController,
                        decoration: const InputDecoration(
                          hintText: 'Digite o telefone adicional',
                          isDense: true,
                        ),
                        keyboardType: TextInputType.number,
                        maxLength: controller.codeCel2 == '+55' ? 15 : null,
                        initialCountryCode: initTellNumber,
                        initialValue: initTellNumber,
                        dropdownTextStyle: const TextStyle(fontSize: 16),
                        inputFormatters: controller.codeCel2 == '+55'
                            ? [maskFormatterBrazil]
                            : null,
                        onChanged: (number) {
                          var update =
                              number.countryCode != controller.codeCel2;
                          if (number.number.isNotEmpty) {
                            if (number.countryCode != '+55') {
                              number.number =
                                  maskFormatterBrazil.unmaskText(number.number);
                            } else {
                              number.number =
                                  maskFormatterBrazil.maskText(number.number);
                            }
                          }

                          if (update) {
                            telController.text = number.number;
                          }

                          _curriculumController.pessoaToSave?.telefone =
                              number.number.isEmpty ? null : number.number;
                        },
                        onCountryChanged: (country) {
                          var dialCode = "+${country.dialCode}";
                          if (dialCode != '+55') {
                            telController.text = maskFormatterBrazil
                                .unmaskText(telController.text);
                          } else {
                            telController.text = maskFormatterBrazil
                                .maskText(telController.text);
                          }

                          controller.changeCodeCel2(dialCode);
                          _curriculumController.pessoaToSave?.telefonePaisCode =
                              dialCode.isEmpty ? null : dialCode;
                        },
                      ),
                    ],
                  ),
                ),
                _curriculumController.loadingPaises
                    ? const SpinKitCircle(color: AppConfig.colorPrimary)
                    : Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Padding(
                            padding: EdgeInsets.only(
                              bottom: 4,
                              left: 4,
                              top: 10,
                            ),
                            child: Text('País'),
                          ),
                          DropdownSearch(
                            this.context,
                            hintText: 'PAÍS'.i18n,
                            title: 'PAÍS'.i18n,
                            items: _curriculumController.paises
                                .map((e) => DropdownSearchItem(
                                      value: e.id,
                                      searchKey: e.nome!,
                                      text: e.nome,
                                      child: Text(e.nome!),
                                    ))
                                .toList(),
                            onSelected: (dynamic item) {
                              if (item != null) {
                                _curriculumController.pessoaToSave?.paisID =
                                    item;
                                _curriculumController.getEstados(clean: true);

                                if (isBr) {
                                  cepController.text = maskCepFormatterBrazil
                                      .maskText(cepController.text);
                                } else {
                                  cepController.text = maskCepFormatterBrazil
                                      .unmaskText(cepController.text);
                                }
                                _curriculumController.pessoaToSave?.cep =
                                    cepController.text;

                                if (mounted) setState(() {});
                              }
                            },
                            value: _curriculumController.pessoaToSave?.paisID,
                          ),
                        ],
                      ),
                const SizedBox(height: 10),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Padding(
                      padding: EdgeInsets.only(bottom: 4, left: 4),
                      child: Text('CEP'),
                    ),
                    AppTextFormField(
                      hintText: 'Digite seu CEP',
                      controller: cepController,
                      textInputAction: TextInputAction.next,
                      keyboardType: TextInputType.number,
                      onChanged: (value) {
                        if (_curriculumController.pessoaToSave?.paisID ==
                            "BR") {
                          value = maskCepFormatterBrazil.unmaskText(value);
                        }
                        _curriculumController.pessoaToSave?.cep = value;
                      },
                      enabled: true,
                      isPassword: false,
                      radius: 6,
                      inputFormatters:
                          _curriculumController.pessoaToSave?.paisID == "BR"
                              ? [maskCepFormatterBrazil]
                              : null,
                      validator: (value) {
                        if (value!.isEmpty) return 'Campo obrigatório'.i18n;

                        if (isBr && value.length < 9) {
                          return 'CEP incorreto'.i18n;
                        }

                        return null;
                      },
                    ),
                  ],
                ),
                _curriculumController.loadingEstados
                    ? const SpinKitCircle(color: AppConfig.colorPrimary)
                    : Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Padding(
                            padding: EdgeInsets.only(
                              bottom: 4,
                              left: 4,
                              top: 10,
                            ),
                            child: Text('Estado'),
                          ),
                          DropdownSearch(
                            this.context,
                            title: 'ESTADO'.i18n,
                            hintText: 'ESTADO',
                            items: _curriculumController.estados
                                .map((e) => DropdownSearchItem(
                                      value: e.id,
                                      searchKey: e.nome!,
                                      text: e.nome,
                                      child: Text(e.nome!),
                                    ))
                                .toList(),
                            onSelected: (dynamic item) {
                              _curriculumController.pessoaToSave?.estadoID =
                                  item;
                              _curriculumController.getCidades().then((_) {
                                if (mounted) setState(() {});
                              });
                            },
                            value: _curriculumController.pessoaToSave?.estadoID,
                          ),
                        ],
                      ),
                _curriculumController.cidades.isEmpty &&
                        !_curriculumController.loadingCidades
                    ? const SizedBox.shrink()
                    : GetBuilder<CurriculumController>(
                        builder: (controller) {
                          return _curriculumController.loadingCidades
                              ? const SpinKitCircle(
                                  color: AppConfig.colorPrimary)
                              : Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const SizedBox(height: 10),
                                    const Padding(
                                      padding: EdgeInsets.only(
                                        bottom: 4,
                                        left: 4,
                                      ),
                                      child: Text('Cidade'),
                                    ),
                                    DropdownSearch(
                                      this.context,
                                      title: 'CIDADE'.i18n,
                                      items: _curriculumController.cidades
                                          .map((e) => DropdownSearchItem(
                                                value: e.id,
                                                searchKey: e.nome!,
                                                text: e.nome,
                                                child: Text(e.nome!),
                                              ))
                                          .toList(),
                                      onSelected: (dynamic item) {
                                        if (item != null) {
                                          _curriculumController
                                              .pessoaToSave?.cidadeID = item;
                                        }
                                      },
                                      value: _curriculumController
                                          .pessoaToSave?.cidadeID,
                                    ),
                                  ],
                                );
                        },
                      ),
                const SizedBox(width: 10),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Padding(
                      padding: EdgeInsets.only(bottom: 4, left: 4, top: 10),
                      child: Text('Bairro'),
                    ),
                    AppTextFormField(
                      hintText: 'Digite seu Bairro',
                      controller: TextEditingController(
                        text: _curriculumController.pessoaToSave?.bairro,
                      ),
                      textInputAction: TextInputAction.next,
                      onChanged: (value) {
                        _curriculumController.pessoaToSave?.bairro = value;
                      },
                      enabled: true,
                      isPassword: false,
                      radius: 6,
                      validator: (value) {
                        if (value!.isEmpty) return 'Campo obrigatório'.i18n;

                        return null;
                      },
                    ),
                  ],
                ),
                const Padding(
                  padding: EdgeInsets.only(bottom: 4, left: 4, top: 10),
                  child: Text('Nome da rua'),
                ),
                Padding(
                  padding: const EdgeInsets.only(bottom: 24.0),
                  child: AppTextFormField(
                    hintText: 'Digite sua Rua',
                    controller: TextEditingController(
                      text: _curriculumController.pessoaToSave?.logradouro,
                    ),
                    textInputAction: TextInputAction.next,
                    onChanged: (value) {
                      _curriculumController.pessoaToSave?.logradouro = value;
                    },
                    enabled: true,
                    isPassword: false,
                    radius: 6,
                    validator: (value) {
                      if (value!.isEmpty) return 'Campo obrigatório'.i18n;

                      return null;
                    },
                  ),
                )
              ],
            );
          },
        ),
      ),
    );
  }

  String getInitialCellNumber() {
    return _curriculumController.pessoaToSave?.celularPaisCode == '+55'
        ? maskFormatterBrazil.maskText(
            _curriculumController.pessoaToSave?.celular ?? '',
          )
        : maskFormatterBrazil.unmaskText(
            _curriculumController.pessoaToSave?.celular ?? '',
          );
  }

  String getInitiaTellNumber() {
    return _curriculumController.pessoaToSave?.telefonePaisCode == '+55'
        ? maskFormatterBrazil.maskText(
            _curriculumController.pessoaToSave?.telefone ?? '',
          )
        : maskFormatterBrazil.unmaskText(
            _curriculumController.pessoaToSave?.telefone ?? '',
          );
  }

  String getInitiaCepNumber() {
    return isBr
        ? maskCepFormatterBrazil.maskText(
            _curriculumController.pessoaToSave?.cep ?? '',
          )
        : maskCepFormatterBrazil.unmaskText(
            _curriculumController.pessoaToSave?.cep ?? '',
          );
  }
}
