import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../../shared/widgets/app_default_button.dart';
import '../forgot_password_controller.dart';
import 'header_recovery_password.dart';
import 'widget_access_recovery.dart';
import 'widget_custom_toaster.dart';
import 'widget_reset_by_email.dart';
import 'widget_step_question.dart';

class WidgetSelectMethodOfRecovery extends StatefulWidget {
  static const route = '/select_method_recovery';
  const WidgetSelectMethodOfRecovery({super.key});

  @override
  State<WidgetSelectMethodOfRecovery> createState() =>
      _WidgetSelectMethodOfRecoveryState();
}

class _WidgetSelectMethodOfRecoveryState
    extends State<WidgetSelectMethodOfRecovery> {
  final controller = Modular.get<ForgotPasswordController>();

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          HeaderRecoveryPassword(
            title: 'Selecione uma opção',
            subTitle: 'Selecione como você deseja recuperar seu acesso',
            onTap: () {},
            bottomBack: true,
          ),
          const SizedBox(height: 20),
          Padding(
            padding: const EdgeInsets.only(left: 24.0, right: 24.0, bottom: 16),
            child: AppDefaultButton(
              title: Text(
                'Redefinição por e-mail'.i18n,
                style: const TextStyle(
                  fontSize: 16,
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w400,
                ),
              ),
              onPressed: orientacoesPorEmail,
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 24.0, right: 24.0, bottom: 16),
            child: AppDefaultButton(
              withBorder: true,
              title: Text(
                'Alterar via questionário'.i18n,
                style: const TextStyle(
                  fontSize: 16,
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w400,
                ),
              ),
              onPressed: alterarPorQuestionario,
            ),
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Future<void> orientacoesPorEmail() async {
    final response = await controller.postAlterarSenhaEmail();

    if (response.sucesso) {
      Modular.to.pushNamed(
        WidgetResetByEmail.route,
        arguments: response.email,
      );
    } else {
      WidgetCustomToaster(
        context: context,
        message: response.mensagem,
        borderRadius: 4,
        duration: const Duration(seconds: 6),
      );
    }
  }

  void alterarPorQuestionario() async {
    final response = await controller.postIniciarQuestionario();

    if (response.sucesso) {
      if (controller.isFinalized) {
        Modular.to.pushNamed(WidgetAccessRecovery.route);
      } else {
        await Modular.to.pushNamed(WidgetStepQuestion.route);
        controller.nextStep();
      }
    } else {
      WidgetCustomToaster(
        context: context,
        message: response.mensagem,
        borderRadius: 4,
        duration: const Duration(seconds: 6),
      ).show();
    }
  }
}
