import 'package:flutter/material.dart';

class CustomRadioListWithInfoColumn extends StatelessWidget {
  final List<Map<String, dynamic>> dataList;
  final String titleKey;
  final int? groupValue;
  final Function(int?) onChanged;
  final String? Function(String?)? getToolTip;

  const CustomRadioListWithInfoColumn({
    super.key,
    required this.dataList,
    required this.titleKey,
    required this.groupValue,
    required this.onChanged,
    this.getToolTip,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: dataList.map((data) {
        return Theme(
          data: Theme.of(context).copyWith(
            listTileTheme: const ListTileThemeData(
              horizontalTitleGap: 0,
            ),
          ),
          child: RadioListTile<int?>(
            contentPadding: EdgeInsets.zero,
            dense: true,
            activeColor: Theme.of(context).primaryColor,
            controlAffinity: ListTileControlAffinity.platform,
            title: Row(
              children: [
                Text(
                  "${data[titleKey]}",
                  style: const TextStyle(fontSize: 12.5),
                ),
                if (getToolTip != null && getToolTip!(data[titleKey]) != null)
                  Row(
                    children: [
                      const SizedBox(width: 5),
                      InkWell(
                        onTap: () {
                          showDialog(
                            context: context,
                            builder: (BuildContext context) {
                              return AlertDialog(
                                backgroundColor: const Color(0xffd9edf7),
                                contentPadding: const EdgeInsets.only(
                                  top: 15,
                                  left: 8,
                                  right: 8,
                                  bottom: 0,
                                ),
                                content: Text(
                                  getToolTip!(data[titleKey]) ?? '',
                                  textAlign: TextAlign.left,
                                ),
                                actionsAlignment: MainAxisAlignment.center,
                                actions: [
                                  TextButton(
                                    onPressed: () {
                                      Navigator.of(context).pop();
                                    },
                                    child: const Text('Fechar'),
                                  ),
                                ],
                              );
                            },
                          );
                        },
                        child: const Icon(
                          Icons.info,
                          color: Color(0xff212529),
                        ),
                      ),
                    ],
                  )
              ],
            ),
            groupValue: groupValue,
            value: data['value'],
            visualDensity: VisualDensity.compact,
            onChanged: onChanged,
          ),
        );
      }).toList(),
    );
  }
}
