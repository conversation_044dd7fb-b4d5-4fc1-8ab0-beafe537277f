import 'package:flutter_modular/flutter_modular.dart';

import 'questionnaire_controller.dart';
import 'questionnaire_page.dart';

class QuestionnaireModule extends Module {
  static const route = '/questionnaire';

  @override
  void binds(i) {
    i.add<PERSON><PERSON>y<PERSON><PERSON>leton(QuestionnaireController.new);
  }

  @override
  void routes(r) {
    r.child(
      Modular.initialRoute,
      child: (context) => QuestionnairePage(questionarioId: r.args.data),
    );
  }
}
