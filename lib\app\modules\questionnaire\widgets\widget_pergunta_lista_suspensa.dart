import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../shared/widgets/app_expandable_text.dart';
import '../questionnaire_controller.dart';

class WidgetPerguntaListaSuspensa extends StatefulWidget {
  final Function(int?)? onChanged;
  const WidgetPerguntaListaSuspensa({
    super.key,
    this.onChanged,
  });

  @override
  State<WidgetPerguntaListaSuspensa> createState() =>
      _WidgetPerguntaListaSuspensaState();
}

class _WidgetPerguntaListaSuspensaState
    extends State<WidgetPerguntaListaSuspensa> {
  final controller = Modular.get<QuestionnaireController>();

  @override
  Widget build(BuildContext context) {
    var alternativas = controller.pergunta!.alternativas;

    if (controller.pergunta?.alternativas == null) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.only(top: 16),
      child: Container(
        padding: const EdgeInsets.only(left: 16, right: 24),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: Colors.white70,
        ),
        child: DropdownButton<int>(
          style: const TextStyle(
            fontSize: 16,
            color: Colors.black,
          ),
          alignment: Alignment.center,
          hint: const Text(
            'SELECIONE UMA ALTERNATIVA:',
            textAlign: TextAlign.center,
          ),
          icon: const Icon(Icons.arrow_drop_down),
          elevation: 4,
          itemHeight: null,
          isExpanded: true,
          underline: const ColoredBox(color: Colors.black),
          items: alternativas!.map((item) {
            return DropdownMenuItem(
              value: item.id,
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 2.0),
                child: Container(
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(6),
                    color: Colors.grey.withValues(alpha: 0.1),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(10),
                    child: ExpandableText(
                      item.descricao!.replaceAll(RegExp(r'<[^>]*>'), ''),
                      textAlign: TextAlign.justify,
                      maxLines: 4,
                      expandText: 'Ver mais',
                      collapseText: 'Ver menos',
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
          value: controller.alternativa,
          onChanged: widget.onChanged,
        ),
      ),
    );
  }
}
