import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:shimmer/shimmer.dart';

import '../../../shared/core/app_config.dart';
import '../../../shared/utils/card_default_alert.dart';
import '../../../shared/widgets/app_image_network_widget.dart';
import '../../../shared/widgets/app_usuario_card/app_usuario_card_controller.dart';
import '../../chat/chat_module.dart';
import 'tab_messages_controller.dart';

class TabMessagesPage extends StatefulWidget {
  const TabMessagesPage({super.key});

  @override
  State<TabMessagesPage> createState() => _TabMessagesPageState();
}

class _TabMessagesPageState extends State<TabMessagesPage> {
  final controller = Modular.get<TabMessagesController>();

  final appUsuarioController = Modular.get<AppUsuarioCardController>();

  @override
  void initState() {
    super.initState();
    controller.load();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F8FA),
      appBar: AppBar(
        title: Padding(
          padding: const EdgeInsets.only(left: 16.0),
          child: Image.asset(
            'lib/assets/images/logo-empregare-splash.png',
            scale: 1.8,
          ),
        ),
        elevation: 0.5,
        backgroundColor: AppConfig.white,
        actions: <Widget>[
          Observer(
            builder: (_) => Padding(
              padding: const EdgeInsets.only(
                bottom: 6.0,
                top: 6.0,
                right: 26,
              ),
              child: ClipOval(
                child: AppImageNetworkWidget(
                  height: 45,
                  width: 45,
                  fit: BoxFit.cover,
                  appUsuarioController.foto ?? '',
                  errorImage: 'lib/assets/images/person-filled.png',
                  scale: 2,
                ),
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          SizedBox(
            // height: 145,
            width: MediaQuery.of(context).size.width,
            child: const DecoratedBox(
              decoration: BoxDecoration(color: Color(0xFFF5F8FA)),
              child: Padding(
                padding: EdgeInsets.only(left: 24.0, top: 16, bottom: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Mensagens',
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontSize: 32,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(height: 5),
                    Text(
                      'Sua caixa de entrada',
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(top: 12),
              child: ListenableBuilder(
                listenable: controller,
                builder: (context, child) {
                  if (controller.loading) {
                    return ListView.builder(
                      itemCount: 3,
                      itemBuilder: (_, i) {
                        return ColoredBox(
                          color: Colors.white,
                          child: Shimmer.fromColors(
                            baseColor: Colors.grey[300]!,
                            highlightColor: Colors.grey[100]!,
                            child: const Padding(
                              padding: EdgeInsets.all(15),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: <Widget>[
                                  ColoredBox(
                                    color: Colors.white,
                                    child: SizedBox(
                                      height: 14,
                                      width: double.maxFinite,
                                    ),
                                  ),
                                  SizedBox(height: 20),
                                  ColoredBox(
                                    color: Colors.white,
                                    child: SizedBox(
                                      height: 10,
                                      width: double.maxFinite,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    );
                  }

                  if (controller.chats.isEmpty) {
                    return const Center(
                      child: CardAlertDefault(
                        title: 'Nenhuma mensagem encontrada!',
                      ),
                    );
                  }

                  return ListView.separated(
                    physics: const BouncingScrollPhysics(),
                    itemBuilder: (_, i) {
                      var chat = controller.chats[i];
                      return Material(
                        color: chat.naoLida ?? false
                            ? const Color(0xFFE4FBFF)
                            : Colors.white,
                        child: InkWell(
                          onTap: () {
                            Modular.to.pushNamed(ChatModule.route, arguments: {
                              'title': chat.nome,
                              'subTitle': 'Vaga: ${chat.vaga}',
                              'chatID': chat.id
                            });
                          },
                          child: Container(
                            padding: const EdgeInsets.all(16),
                            color: Colors.transparent,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: <Widget>[
                                const CircleAvatar(
                                  backgroundColor: Colors.white,
                                  backgroundImage: AssetImage(
                                    'lib/assets/icons/icon.png',
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        chat.nome ?? '',
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      Text(
                                        chat.mensagem ?? '',
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        style: const TextStyle(
                                          color: Colors.grey,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                    itemCount: controller.chats.length,
                    separatorBuilder: (_, i) {
                      return DecoratedBox(
                        decoration: BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              width: 0.5,
                              color: Theme.of(context).dividerColor,
                            ),
                          ),
                        ),
                      );
                    },
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
