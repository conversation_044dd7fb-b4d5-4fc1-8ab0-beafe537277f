import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';

import '../../../../../shared/widgets/helpers/int_iphone_field_mod.dart';
import '../../../../../shared/widgets/app_text_form_field.dart';
import '../../../../curriculum/curriculum_controller.dart';
import '../../curriculum_editing_controller.dart';
import '../../widgets/secao.dart';

class RedesSociais extends StatefulWidget {
  const RedesSociais({super.key});

  @override
  State<RedesSociais> createState() => _RedesSociaisState();
}

class _RedesSociaisState extends State<RedesSociais> {
  final CurriculumController _curriculumController = Modular.get();

  final controller = Modular.get<CurriculumEditingController>();
  var maskFormatterBrazil = MaskTextInputFormatter(
    mask: "(##) #####-####",
  );

  String? initCellNumber;

  final whatsAppFocus = FocusNode();
  final phoneController = TextEditingController();

  @override
  void initState() {
    super.initState();

    phoneController.text = getInitialWhatsNumber();

    initCellNumber = _curriculumController.socialMedia.redesSociaisMap
        .firstWhereOrNull((map) {
          return map.containsValue('social-whatsapp');
        })?["value"]
        ?.split(" ")
        .first
        .replaceAll('+', '');

    controller.changeCodeWhatsapp(
      _curriculumController.socialMedia.redesSociaisMap
          .firstWhereOrNull((map) {
            return map.containsValue('social-whatsapp');
          })?["value"]
          ?.split(" ")
          .first,
    );
  }

  @override
  void dispose() {
    super.dispose();

    whatsAppFocus.dispose();
    phoneController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Observer(
      builder: (_) => Secao(
        fields: Column(
          children: [
            const SizedBox(
              height: 10,
            ),
            const Row(
              children: [
                FaIcon(FontAwesomeIcons.linkedinIn, size: 14),
                SizedBox(width: 5),
                Text('LINKEDIN'),
              ],
            ),
            const SizedBox(height: 5),
            AppTextFormField(
              hintText: 'https://www.linkedin.com/in/seuusuario',
              controller: TextEditingController(
                text:
                    _curriculumController.socialMedia.getRedeSocial('linkedin'),
              ),
              textInputAction: TextInputAction.next,
              keyboardType: TextInputType.emailAddress,
              onChanged: (value) =>
                  _curriculumController.socialMedia.updateMapRedesSociais(
                'linkedin',
                value,
              ),
              enabled: true,
              isPassword: false,
              radius: 6,
            ),
            const SizedBox(height: 15),
            const Row(
              children: [
                FaIcon(FontAwesomeIcons.whatsapp, size: 14),
                SizedBox(width: 5),
                Text('WHATSAPP'),
              ],
            ),
            const SizedBox(height: 5),
            IntlPhoneFieldMod(
              controller: phoneController,
              decoration: const InputDecoration(
                hintText: 'Selecione o número de telefone',
                isDense: true,
              ),
              keyboardType: TextInputType.number,
              maxLength: controller.codeCel1 == '+55' ? 15 : null,
              initialCountryCode: initCellNumber,
              initialValue: initCellNumber,
              dropdownTextStyle: const TextStyle(fontSize: 16),
              inputFormatters: controller.codeWhatsapp == '+55'
                  ? [maskFormatterBrazil]
                  : null,
              onChanged: (number) {
                var update = number.countryCode != controller.codeWhatsapp;
                if (number.number.isNotEmpty) {
                  if (number.countryCode != '+55') {
                    number.number =
                        maskFormatterBrazil.unmaskText(number.number);
                  } else {
                    number.number = maskFormatterBrazil.maskText(number.number);
                  }
                }

                if (update) {
                  phoneController.text = number.number;
                }

                _curriculumController.socialMedia.updateMapRedesSociais(
                  'whatsapp',
                  number.number.isEmpty
                      ? null
                      : "${number.countryCode} ${number.number}",
                );
              },
              onCountryChanged: (country) {
                var dialCode = "+${country.dialCode}";
                if (dialCode != '+55') {
                  phoneController.text =
                      maskFormatterBrazil.unmaskText(phoneController.text);
                } else {
                  phoneController.text =
                      maskFormatterBrazil.maskText(phoneController.text);
                }

                controller.changeCodeWhatsapp(dialCode);
                _curriculumController.socialMedia.updateMapRedesSociais(
                  'whatsapp',
                  dialCode.isEmpty
                      ? null
                      : "$dialCode ${maskFormatterBrazil.unmaskText(phoneController.text)}",
                );
              },
            ),
            const SizedBox(height: 15),
            const Row(
              children: [
                FaIcon(FontAwesomeIcons.facebook, size: 14),
                SizedBox(width: 5),
                Text('FACEBOOK'),
              ],
            ),
            const SizedBox(height: 5),
            AppTextFormField(
              hintText: 'Perfil do Facebook',
              controller: TextEditingController(
                text:
                    _curriculumController.socialMedia.getRedeSocial('facebook'),
              ),
              textInputAction: TextInputAction.next,
              keyboardType: TextInputType.emailAddress,
              onChanged: (value) =>
                  _curriculumController.socialMedia.updateMapRedesSociais(
                'facebook',
                value,
              ),
              enabled: true,
              isPassword: false,
              radius: 6,
            ),
            const SizedBox(height: 15),
            const Row(
              children: [
                FaIcon(FontAwesomeIcons.instagram, size: 14),
                SizedBox(width: 5),
                Text('INSTAGRAM'),
              ],
            ),
            const SizedBox(height: 5),
            AppTextFormField(
              hintText: 'Usuário do Instagram',
              controller: TextEditingController(
                text: _curriculumController.socialMedia
                    .getRedeSocial('instagram'),
              ),
              textInputAction: TextInputAction.next,
              keyboardType: TextInputType.emailAddress,
              onChanged: (value) =>
                  _curriculumController.socialMedia.updateMapRedesSociais(
                'instagram',
                value,
              ),
              enabled: true,
              isPassword: false,
              radius: 6,
            ),
            const SizedBox(height: 15),
            const Row(
              children: [
                FaIcon(FontAwesomeIcons.github, size: 14),
                SizedBox(width: 5),
                Text('GITHUB'),
              ],
            ),
            const SizedBox(height: 5),
            AppTextFormField(
              hintText: 'Link do perfil do Github',
              controller: TextEditingController(
                text: _curriculumController.socialMedia.getRedeSocial('github'),
              ),
              textInputAction: TextInputAction.next,
              keyboardType: TextInputType.emailAddress,
              onChanged: (value) =>
                  _curriculumController.socialMedia.updateMapRedesSociais(
                'github',
                value,
              ),
              enabled: true,
              isPassword: false,
              radius: 6,
            ),
          ],
        ),
      ),
    );
  }

  String getInitialWhatsNumber() {
    List<String>? numberSplited = _curriculumController
        .socialMedia.redesSociaisMap
        .firstWhereOrNull((map) {
      return map.containsValue('social-whatsapp');
    })?["value"]?.split(" ");

    if (numberSplited?.isEmpty ?? true) return "";

    return numberSplited?.first != '+55'
        ? maskFormatterBrazil.unmaskText(numberSplited!.sublist(1).join())
        : maskFormatterBrazil.maskText(numberSplited!.sublist(1).join());
  }
}
