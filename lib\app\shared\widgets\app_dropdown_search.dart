// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:collection/collection.dart' show IterableExtension;
import 'package:flutter/material.dart';

class DropdownSearchItem<T> extends DropdownMenuItem<T> {
  final String searchKey;
  final String? text;

  const DropdownSearchItem({
    super.key,
    required T super.value,
    required super.child,
    required this.searchKey,
    this.text,
  });
}

class DropdownSearch<T> extends FormField<String> {
  final BuildContext context;
  final List<DropdownSearchItem<T>>? items;
  final String? title;
  final bool showClear;
  final Function(T?)? onSelected;
  final T? value;
  final String? hintText;

  DropdownSearch(
    this.context, {
    super.key,
    this.items,
    this.value,
    this.title,
    this.hintText,
    this.showClear = false,
    this.onSelected,
  }) : super(
          builder: (state) {
            return TextFormField(
              controller: TextEditingController(
                text: state.value ??
                    items!.firstWhereOrNull((e) => e.value == value)?.text ??
                    '',
              ),
              onTap: () async {
                var result = await showDialog<DropdownSearchItem<T>>(
                    context: context,
                    barrierDismissible: true,
                    builder: (context) {
                      return DropdownDialog<T>(
                        title: title,
                        items: items ?? [],
                      );
                    });

                if (result != null) {
                  state.didChange(result.text);
                  if (onSelected != null) {
                    onSelected(result.value);
                  }
                }
              },
              readOnly: true,
              textInputAction: TextInputAction.next,
              decoration: InputDecoration(
                isDense: true,
                hintText: hintText,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                hintStyle: const TextStyle(
                  fontSize: 14,
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w400,
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide:
                      const BorderSide(color: Color(0xffeeeff3), width: 1.8),
                ),
                suffixIcon: const Icon(
                  Icons.keyboard_arrow_down_outlined,
                  color: Color(0xff161519),
                  size: 30,
                ),
              ),
            );
          },
        );
}

class DropdownDialog<T> extends StatefulWidget {
  final List<DropdownSearchItem<T>> items;
  final String? title;

  const DropdownDialog({
    super.key,
    required this.items,
    this.title,
  });

  @override
  _DropdownDialogState<T> createState() => _DropdownDialogState<T>();
}

class _DropdownDialogState<T> extends State<DropdownDialog> {
  TextEditingController txtSearch = TextEditingController();
  Function? searchFn;

  List<int>? shownIndexes = [];

  @override
  void initState() {
    matchFn(item, keyword) {
      return (item.searchKey
          .toString()
          .toLowerCase()
          .contains(keyword.toLowerCase()));
    }

    searchFn = (keyword, items) {
      List<int> shownIndexes = [];
      int i = 0;
      for (var item in widget.items) {
        if (matchFn(item, keyword) || (keyword?.isEmpty ?? true)) {
          shownIndexes.add(i);
        }
        i++;
      }
      return (shownIndexes);
    };
    assert(searchFn != null);

    _updateShownIndexes('');

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      padding: MediaQuery.of(context).viewInsets,
      duration: const Duration(milliseconds: 300),
      child: Card(
        margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 4),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 15),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              titleBar(),
              searchBar(),
              const SizedBox(height: 12),
              list(),
              closeButtonWrapper(),
            ],
          ),
        ),
      ),
    );
  }

  void _updateShownIndexes(String keyword) {
    shownIndexes = searchFn!(keyword, widget.items);
  }

  Widget titleBar() {
    return Text(widget.title ?? '');
  }

  Widget searchBar() {
    return Stack(
      children: <Widget>[
        TextField(
          controller: txtSearch,
          decoration: const InputDecoration(
              contentPadding:
                  EdgeInsets.symmetric(horizontal: 32, vertical: 12)),
          autofocus: true,
          onChanged: (value) {
            _updateShownIndexes(value);
            setState(() {});
          },
        ),
        const Positioned(
          left: 0,
          top: 0,
          bottom: 0,
          child: Center(
            child: Icon(
              Icons.search,
              size: 24,
            ),
          ),
        ),
        txtSearch.text.isNotEmpty
            ? Positioned(
                right: 0,
                top: 0,
                bottom: 0,
                child: Center(
                  child: InkWell(
                    onTap: () {
                      setState(() {
                        txtSearch.text = '';
                      });
                    },
                    borderRadius: const BorderRadius.all(Radius.circular(32)),
                    child: const SizedBox(
                      width: 32,
                      height: 32,
                      child: Center(
                        child: Icon(
                          Icons.close,
                          size: 24,
                        ),
                      ),
                    ),
                  ),
                ),
              )
            : Container(),
      ],
    );
  }

  Widget list() {
    return Expanded(
      child: Scrollbar(
        child: ListView.separated(
          separatorBuilder: (_, i) => const Divider(),
          itemBuilder: (context, index) {
            DropdownSearchItem item = widget.items[shownIndexes![index]];
            return InkWell(
              onTap: () {
                Navigator.pop(context, item);
              },
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 7),
                child: item.child,
              ),
            );
          },
          itemCount: shownIndexes!.length,
        ),
      ),
    );
  }

  Widget closeButtonWrapper() {
    return Align(
      alignment: Alignment.centerRight,
      child: TextButton(
          onPressed: () {
            Navigator.pop(context);
          },
          child: const Text('Fechar')),
    );
  }
}
