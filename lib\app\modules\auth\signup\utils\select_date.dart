import 'package:flutter/material.dart';
import 'package:flutter_holo_date_picker/date_picker_constants.dart';
import 'package:flutter_holo_date_picker/flutter_holo_date_picker.dart';
import 'package:flutter_holo_date_picker/widget/date_ext.dart';
import 'package:intl/intl.dart';

import '../../../../shared/widgets/app_text_form_field.dart';
import '../signup_controller.dart';

class DateSelect extends StatefulWidget {
  const DateSelect({
    super.key,
    required this.controller,
    required this.dataNascFocusNode,
  });

  final SignupController controller;
  final FocusNode dataNascFocusNode;

  @override
  _DateSelectState createState() => _DateSelectState();
}

class _DateSelectState extends State<DateSelect> {
  late TextEditingController dayController;
  late TextEditingController monthController;
  late TextEditingController yearController;
  final DateFormat monthFormat = DateFormat.MMMM('pt_BR');

  @override
  void initState() {
    super.initState();
    dayController = TextEditingController();
    monthController = TextEditingController();
    yearController = TextEditingController();

    widget.dataNascFocusNode.addListener(_handleFocusChange);
  }

  @override
  void dispose() {
    widget.dataNascFocusNode.removeListener(_handleFocusChange);
    dayController.dispose();
    monthController.dispose();
    yearController.dispose();
    super.dispose();
  }

  void _handleFocusChange() {
    if (widget.dataNascFocusNode.hasFocus) {
      _selectDate();
    }
  }

  Future<DateTime?> selectDate(
    BuildContext context,
    String title,
    DateTime? currentDate, {
    String format = 'dd-MMMM-yyyy',
  }) async {
    final DateTime now = DateTime.now();
    final DateTime hundredYears = now.subtract(const Duration(days: 36500));

    var datePicked = await showSimpleDatePicker(
      context,
      titleText: title,
      confirmText: 'Confirmar',
      cancelText: 'Cancelar',
      initialDate: currentDate ?? now,
      firstDate: hundredYears,
      lastDate: now,
      dateFormat: format,
      locale: DateTimePickerLocale.pt_br,
    );
    return datePicked;
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await selectDate(
      context,
      'Selecione a data',
      widget.controller.dataNascimento,
    );

    if (picked != null) {
      setState(() {
        widget.controller.setDataNascimento(picked);
        dayController.text = picked.day.toString();
        monthController.text = monthFormat.format(picked);
        yearController.text = picked.year.toString();
      });
    }

    widget.dataNascFocusNode.unfocus();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          flex: 1,
          child: AppTextFormField(
            hintText: 'Dia',
            suffixIcon: Padding(
              padding: const EdgeInsets.only(right: 2.0),
              child: InkWell(
                onTap: _selectDate,
                child: const Icon(
                  Icons.keyboard_arrow_down_outlined,
                  color: Colors.black,
                ),
              ),
            ),
            readOnly: true,
            controller: dayController,
            textInputAction: TextInputAction.next,
            keyboardType: TextInputType.datetime,
            enabled: true,
            isPassword: false,
            radius: 6,
            validator: (value) {
              if (value!.isEmpty) return 'Campo obrigatório';
              return null;
            },
            focusNode: widget.dataNascFocusNode,
          ),
        ),
        const SizedBox(width: 7),
        Expanded(
          flex: 2,
          child: AppTextFormField(
            hintText: 'Mês',
            suffixIcon: Padding(
              padding: const EdgeInsets.only(right: 16.0),
              child: InkWell(
                onTap: _selectDate,
                child: const Icon(
                  Icons.keyboard_arrow_down_outlined,
                  color: Colors.black,
                ),
              ),
            ),
            readOnly: true,
            controller: monthController,
            textInputAction: TextInputAction.next,
            keyboardType: TextInputType.text,
            enabled: true,
            isPassword: false,
            radius: 6,
            validator: (value) {
              if (value!.isEmpty) return 'Campo obrigatório';
              return null;
            },
            focusNode: widget.dataNascFocusNode,
          ),
        ),
        const SizedBox(width: 7),
        Expanded(
          flex: 2,
          child: AppTextFormField(
            hintText: 'Ano',
            suffixIcon: Padding(
              padding: const EdgeInsets.only(right: 16.0),
              child: InkWell(
                onTap: _selectDate,
                child: const Icon(
                  Icons.keyboard_arrow_down_outlined,
                  color: Colors.black,
                ),
              ),
            ),
            readOnly: true,
            controller: yearController,
            textInputAction: TextInputAction.next,
            keyboardType: TextInputType.datetime,
            enabled: true,
            isPassword: false,
            radius: 6,
            validator: (value) {
              if (value!.isEmpty) return 'Campo obrigatório';
              return null;
            },
            focusNode: widget.dataNascFocusNode,
          ),
        ),
      ],
    );
  }
}

Future<DateTime?> showSimpleDatePicker(
  BuildContext context, {
  DateTime? firstDate,
  DateTime? lastDate,
  DateTime? initialDate,
  String? dateFormat,
  DateTimePickerLocale locale = DATETIME_PICKER_LOCALE_DEFAULT,
  DateTimePickerMode pickerMode = DateTimePickerMode.date,
  Color? backgroundColor,
  Color? textColor,
  TextStyle? itemTextStyle,
  String? titleText,
  String? confirmText,
  String? cancelText,
  bool looping = false,
  bool reverse = false,
}) {
  DateTime? selectedDate = initialDate ?? DateTime.now().startOfDay();
  final List<Widget> listButtonActions = [
    TextButton(
      style: TextButton.styleFrom(foregroundColor: textColor),
      child: Text(cancelText ?? "Cancel"),
      onPressed: () {
        Navigator.pop(context);
      },
    ),
    TextButton(
      style: TextButton.styleFrom(foregroundColor: textColor),
      child: Text(confirmText ?? "OK"),
      onPressed: () {
        Navigator.pop(context, selectedDate);
      },
    ),
  ];

  firstDate ??= DateTime.parse(DATE_PICKER_MIN_DATETIME);
  lastDate ??= DateTime.parse(DATE_PICKER_MAX_DATETIME);

  initialDate ??= DateTime.now();

  backgroundColor ??= DateTimePickerTheme.Default.backgroundColor;

  textColor ??= DateTimePickerTheme.Default.itemTextStyle.color;

  var datePickerDialog = AlertDialog(
    title: Text(
      titleText ?? "Select Date",
      style: TextStyle(color: textColor),
    ),
    contentPadding: const EdgeInsets.symmetric(vertical: 0, horizontal: 14),
    backgroundColor: backgroundColor,
    content: SizedBox(
      width: 300,
      child: DatePickerWidget(
        firstDate: firstDate,
        lastDate: lastDate,
        initialDate: initialDate,
        dateFormat: dateFormat,
        locale: locale,
        pickerTheme: DateTimePickerTheme(
          backgroundColor: backgroundColor,
          itemTextStyle: itemTextStyle ?? TextStyle(color: textColor),
        ),
        onChange: ((DateTime date, list) {
          selectedDate = date;
        }),
        looping: looping,
      ),
    ),
    actions: reverse ? listButtonActions.reversed.toList() : listButtonActions,
  );
  return showDialog(
    useRootNavigator: false,
    context: context,
    builder: (context) => datePickerDialog,
  );
}
