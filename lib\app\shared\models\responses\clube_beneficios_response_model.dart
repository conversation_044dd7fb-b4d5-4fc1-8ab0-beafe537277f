class ClubeBeneficiosResponseModel {
  final bool? sucesso;
  final List<BeneficiosModel>? dados;

  ClubeBeneficiosResponseModel({
    this.sucesso,
    this.dados,
  });

  factory ClubeBeneficiosResponseModel.fromJson(Map<String, dynamic> json) {
    return ClubeBeneficiosResponseModel(
      sucesso: json['sucesso'] as bool?,
      dados: (json['dados'] as List<dynamic>?)
          ?.map((e) => BeneficiosModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class BeneficiosModel {
  final int? id;
  final String? nome;
  final String? beneficio;
  final String? logo;

  BeneficiosModel({this.id, this.nome, this.beneficio, this.logo});

  factory BeneficiosModel.fromJson(Map<String, dynamic> json) {
    return BeneficiosModel(
      id: json['id'] as int?,
      nome: json['nome'] as String?,
      beneficio: json['beneficio'] as String?,
      logo: json['logo'] as String?,
    );
  }
}
