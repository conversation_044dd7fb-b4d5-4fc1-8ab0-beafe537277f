import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../../forgot_password/forgot_password_module.dart';

class ForgotPasswordWidget extends StatelessWidget {
  const ForgotPasswordWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        InkWell(
          customBorder: RoundedRectangleBorder(borderRadius: BorderRadius.circular(25.0)),
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text(
              'Esqueci minha senha'.i18n,
              style: const TextStyle(
                fontSize: 14,
                fontFamily: 'Inter',
                color: Color(0xff161519),
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
          onTap: () => Modular.to.pushNamed(ForgotPasswordModule.route),
        ),
      ],
    );
  }
}
