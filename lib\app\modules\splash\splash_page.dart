import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

import '../../shared/core/app_config.dart';
import '../../shared/core/app_translation.i18n.dart';
import 'splash_controller.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  _SplashPageState createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  final controller = Modular.get<SplashController>();

  @override
  void initState() {
    super.initState();
    controller.startTimer();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConfig.colorBackground,
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: <Widget>[
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Image.asset('lib/assets/images/logo-branco.png', width: 192),
                  const SizedBox(height: 20),
                  Text(
                    'Vagas de emprego e cadastro de currículo'.i18n,
                    style: const TextStyle(color: Colors.white),
                  ),
                  const SizedBox(height: 20),
                  const SpinKitSpinningCircle(color: AppConfig.colorPrimary)
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
