import 'package:bottom_sheet/bottom_sheet.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:share_plus/share_plus.dart';

import '../../../../shared/core/app_config.dart';
import '../../../../shared/models/responses/inscricoes_response_model.dart';
import '../../../../shared/widgets/app_image_network_widget.dart';
import '../../../jobs/job_detail/job_detail_controller.dart';
import '../../../jobs/job_detail/widget/app_body_builder_job_detaisl.dart';
import '../../../jobs/job_detail/widget/app_header_builder_job_details.dart';
import '../../utils/subscription_button.dart';

class RecommendationJobCard extends StatelessWidget {
  final JobModel job;
  final bool showActionsInModal;

  const RecommendationJobCard({
    super.key,
    required this.job,
    this.showActionsInModal = true,
  });
  // String _formatSalaryRange(String salary) {
  //   String formatted = salary.replaceAll(RegExp(r'[^\d,.\-\s]'), '').trim();

  //   if (formatted.contains('-') || formatted.toLowerCase().contains(' a ')) {
  //     return formatted;
  //   }

  //   List<String> parts = formatted.split(RegExp(r'\s+'));

  //   if (parts.length == 2) {
  //     String first = parts[0].trim();
  //     String second = parts[1].trim();

  //     if (RegExp(r'^\d+[,.]?\d*$').hasMatch(first) &&
  //         RegExp(r'^\d+[,.]?\d*$').hasMatch(second)) {
  //       return '$first - $second';
  //     }
  //   }

  //   if (formatted.isNotEmpty && RegExp(r'^\d+[,.]?\d*$').hasMatch(formatted)) {
  //     return 'até $formatted';
  //   }

  //   return salary;
  // }

  Widget _buildBottomActionButtons(BuildContext context, JobDetailController controllerJob) {
    String formatHtml(String text) {
      final replacements = {
        '<ul>': '\n', // Adiciona quebra de linha para início de listas
        '</ul>': '', // Remove o fechamento da lista
        '<li>': '\n- ', // Substitui <li> por marcadores de lista
        '</li>': '', // Remove </li>
        '<p>': '\n', // Adiciona quebra de linha para parágrafos
        '</p>': '', // Remove fechamento de parágrafo
        '<br>': '\n', // Substitui <br> por quebra de linha
      };

      replacements.forEach((tag, replacement) {
        text = text.replaceAll(tag, replacement);
      });

      text = text.replaceAll(RegExp(r'<[^>]+>'), '');
      text = text.replaceAll(RegExp(r'\n{2,}'), '\n\n');

      return text.trim();
    }

    String plainText = formatHtml(controllerJob.jobDetails?.descricao ?? '');

    return Container(
      height: 73,
      width: MediaQuery.of(context).size.width,
      color: const Color(0xfff5f5f5),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.max,
        children: [
          SubscriptionButton(controllerJob: controllerJob),
          Padding(
            padding: const EdgeInsets.only(left: 25.0),
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6.0),
                ),
                shadowColor: Colors.transparent,
                elevation: 0,
                backgroundColor: const Color(0xff9e9eef),
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 17,
                ),
              ),
              child: Image.asset(
                'lib/assets/images/share.png',
                scale: 2,
              ),
              onPressed: () async {
                await Share.share(
                  "${controllerJob.jobDetails?.titulo ?? ''}\n"
                  "$plainText\n"
                  "${AppConfig.siteUrl}${controllerJob.jobDetails?.url ?? ''}",
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final controllerJob = Modular.get<JobDetailController>();

    return InkWell(
      onTap: () async {
        await controllerJob.getJobDetails(job.id!);

        await showStickyFlexibleBottomSheet(
          minHeight: .82,
          initHeight: .82,
          maxHeight: .92,
          maxHeaderHeight: 118,
          minHeaderHeight: 100,
          isSafeArea: true,
          bottomSheetBorderRadius: const BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
          anchors: [0.8],
          decoration: const BoxDecoration(
            color: Colors.white,
          ),
          context: context,
          isDismissible: true,
          duration: const Duration(milliseconds: 500),
          headerBuilder: (BuildContext context, double offset) {
            return AppHeaderBuildeJobDetails(
              image: job.logo?.thumb ?? "",
              title: job.titulo ?? "",
              company: job.empresa ?? "",
              date: job.data ?? "",
              offset: offset,
            );
          },
          bodyBuilder: (context, bottomSheetOffset) {
            return SliverChildListDelegate([
              Padding(
                padding: const EdgeInsets.only(left: 24, right: 24),
                child: AppBodyBuilderJobDetails(jobId: job.id!),
              ),
              const SizedBox(height: 20),
              if (showActionsInModal) _buildBottomActionButtons(context, controllerJob),
            ]);
          },
        );

        controllerJob.removeJob(job.id!);
      },
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  if (job.logo?.thumb != null)
                    ClipRRect(
                      borderRadius: BorderRadius.circular(4),
                      child: AppImageNetworkWidget(
                        job.logo!.thumb!,
                        width: 40,
                        height: 40,
                        fit: BoxFit.contain,
                        alignment: Alignment.center,
                        filterQuality: FilterQuality.high,
                        errorImage: 'lib/assets/images/company-default.png',
                      ),
                    )
                  else
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: AppConfig.grey,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: const Icon(
                        Icons.business,
                        color: AppConfig.colorIcon,
                        size: 20,
                      ),
                    ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          job.empresa ?? 'Empresa',
                          style: const TextStyle(
                            fontSize: 12,
                            color: AppConfig.colorIcon,
                            fontWeight: FontWeight.w400,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 2),
                        Text(
                          job.titulo ?? 'Título da vaga',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              if (job.chamada != null && job.chamada!.isNotEmpty)
                Text(
                  job.chamada!,
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppConfig.colorIcon,
                    height: 1.4,
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              const SizedBox(height: 12),
              // Localização
              if (job.cidades != null && job.cidades!.isNotEmpty)
                Row(
                  children: [
                    const Icon(
                      Icons.location_on_outlined,
                      size: 14,
                      color: AppConfig.colorIcon,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        job.cidades!.first,
                        style: const TextStyle(
                          fontSize: 11,
                          color: AppConfig.colorIcon,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              // if (job.salario != null && job.salario!.isNotEmpty) ...[
              //   const SizedBox(height: 6),
              //   Row(
              //     children: [
              //       const Icon(
              //         Icons.attach_money,
              //         size: 14,
              //         color: AppConfig.colorPrimary,
              //       ),
              //       const SizedBox(width: 4),
              // Expanded(
              //   child: Text(
              //     _formatSalaryRange(job.salario!),
              //     style: const TextStyle(
              //       fontSize: 12,
              //       color: AppConfig.colorPrimary,
              //       fontWeight: FontWeight.w600,
              //     ),
              //     maxLines: 1,
              //     overflow: TextOverflow.ellipsis,
              //   ),
              // ),
              //     ],
              //   ),
              // ],
              // if (job.nivel != null && job.nivel!.isNotEmpty) ...[
              //   const SizedBox(height: 8),
              //   Container(
              //     padding: const EdgeInsets.symmetric(
              //       horizontal: 8,
              //       vertical: 4,
              //     ),
              //     decoration: BoxDecoration(
              //       color: AppConfig.grey,
              //       borderRadius: BorderRadius.circular(12),
              //     ),
              //     child: Text(
              //       job.nivel!,
              //       style: const TextStyle(
              //         fontSize: 10,
              //         color: AppConfig.colorIcon,
              //         fontWeight: FontWeight.w400,
              //       ),
              //     ),
              //   ),
              // ],
            ],
          ),
        ),
      ),
    );
  }
}
