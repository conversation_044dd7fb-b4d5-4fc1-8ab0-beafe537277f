import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../../../shared/core/app_config.dart';
import '../../../../shared/core/app_utils.dart';
import '../../../../shared/models/responses/dash_notificacoes_response_model.dart';
import '../../../questionnaire/questionnaire_controller.dart';
import '../../../questionnaire/questionnaire_module.dart';
import '../../../schedule/schedule_module.dart';
import '../../tab_profile/utils/behavioral_profile.dart';
import '../tab_notifications_controller.dart';

class WidgetQuestCard extends StatelessWidget {
  WidgetQuestCard({super.key});

  final controller = Modular.get<TabNotificationsController>();
  final controllerQuest = Modular.get<QuestionnaireController>();

  @override
  Widget build(BuildContext context) {
    return Observer(
      builder: (_) => Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20.0),
        child: ListView.separated(
          shrinkWrap: true,
          itemBuilder: (_, index) {
            int qLength = controller.questionarios.length;

            if (index < qLength) {
              controllerQuest.load(controller.questionarios[index].id);
              return InkWell(
                onTap: () async {
                  await Modular.to.pushNamed(
                    QuestionnaireModule.route,
                    arguments: controller.questionarios[index].id,
                  );
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: const Color(0xFFF5F5F5),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  height: 85,
                  child: Padding(
                    padding: const EdgeInsets.only(
                      left: 10.0,
                      top: 8,
                      bottom: 4,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          controller.questionarios[index].titulo!,
                          style: const TextStyle(
                            fontSize: 14,
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          controller.questionarios[index].vaga!,
                          style: const TextStyle(
                            fontSize: 12,
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w400,
                            color: Color(0xff666666),
                          ),
                        ),
                        Card(
                          elevation: 0,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Container(
                                alignment: Alignment.center,
                                height: 15,
                                width: 15,
                                padding: const EdgeInsets.all(2),
                                margin: const EdgeInsets.all(5),
                                decoration: BoxDecoration(
                                  color: const Color(0xfff05656),
                                  borderRadius: BorderRadius.circular(50),
                                ),
                                child: Center(
                                  child: Text(
                                    removeAllHtmlTags(
                                      controller
                                              .questionarios[index].questoes ??
                                          "",
                                    ),
                                    textAlign: TextAlign.center,
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 8,
                                    ),
                                  ),
                                ),
                              ),
                              Text(
                                removeAllHtmlTags(
                                  controller.questionarios[index].situacao ??
                                      "",
                                ),
                                style: const TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              const SizedBox(width: 5)
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              );
            }

            if (index == controller.countNotificacao - 1) {
              return ListTile(
                onTap: () {
                  Modular.to.pushNamed(BehavioralProfile.route);
                },
                title: Text(
                  'Perfil Comportamental'.i18n,
                  style: const TextStyle(
                    fontSize: 16,
                  ),
                ),
                subtitle: const Text(
                  'Responda e veja seu perfil comportamental ao finalizar todas as questões.',
                  style: TextStyle(fontSize: 14),
                ),
                trailing: Text(
                  'RESPONDER'.i18n,
                  style: const TextStyle(
                    color: AppConfig.colorPrimary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              );
            }

            AgendaModel agenda = controller.agendas[index - qLength];
            return ListTile(
              onTap: () async {
                await Modular.to.pushNamed(
                  ScheduleModule.route,
                  arguments: agenda.token,
                );
              },
              title: Text(
                'Compromisso'.i18n,
                style: const TextStyle(
                  fontSize: 16,
                ),
              ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    removeAllHtmlTags(agenda.data),
                    style: const TextStyle(fontSize: 14),
                  ),
                  const SizedBox(height: 5),
                  Text(
                    'Situação: '.i18n + removeAllHtmlTags(agenda.situacao),
                    style: const TextStyle(fontSize: 11),
                  ),
                ],
              ),
              trailing: Text(
                'VISUALIZAR'.i18n,
                style: const TextStyle(
                  color: AppConfig.colorPrimary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            );
          },
          separatorBuilder: (_, index) {
            return const Divider(height: 5, color: AppConfig.grey);
          },
          itemCount: controller.countNotificacao,
        ),
      ),
    );
  }
}
