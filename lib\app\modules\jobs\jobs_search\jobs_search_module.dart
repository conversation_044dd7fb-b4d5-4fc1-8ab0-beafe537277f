import 'package:flutter_modular/flutter_modular.dart';

import 'jobs_search_controller.dart';
import 'jobs_search_page.dart';

class JobsSearchModule extends Module {
  static const route = '/jobs_search';

  @override
  void binds(i) {
    i.addLazySingleton(JobsSearchController.new);
  }

  @override
  void routes(r) {
    r.child(
      Modular.initialRoute,
      child: (context) => JobsSearchPage(busca: r.args.data),
    );
  }
}
