// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';

class CardDefault extends StatelessWidget {
  const CardDefault({
    super.key,
    required this.title,
    this.color,
    this.colorText,
    this.borderColor,
  });

  final String title;
  final Color? color;
  final Color? colorText;
  final Color? borderColor;

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      shadowColor: Colors.transparent,
      color: color,
      shape: RoundedRectangleBorder(
        borderRadius: const BorderRadius.all(Radius.circular(500.0)),
        side: BorderSide(
          color: borderColor ?? Colors.transparent,
          width: 1.2,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(10),
        child: Text(
          title,
          style: TextStyle(
            color: colorText ?? Colors.white,
          ),
        ),
      ),
    );
  }
}
