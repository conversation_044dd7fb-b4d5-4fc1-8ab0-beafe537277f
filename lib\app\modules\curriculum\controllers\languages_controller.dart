import 'package:empregare_app/app/shared/mixins/loader_mixin.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:get/get.dart';

import '../../../shared/models/responses/categorias_idiomas_response_model.dart';
import '../../../shared/models/responses/listar_idiomas_response_model.dart';
import '../../../shared/repositories/curriculum_repository.dart';
import '../curriculum_controller.dart';

class LanguagesController extends GetxController with LoaderMixin {
  final CurriculumRepository _repository = Modular.get();

  IdiomaModel? idiomaToSave;

  List<CategoriaIdiomaModel>? categoriasIdiomas = <CategoriaIdiomaModel>[];

  List<NivelIdiomaModel>? niveisIdiomas = <NivelIdiomaModel>[];

  List<IdiomaModel>? idiomas = <IdiomaModel>[];

  Future load() async {
    try {
      changeLoading(true);
      await Future.wait([
        loadCategoriasIdiomas(),
        loadIdiomas(),
      ]);
    } finally {
      changeLoading(false);
    }
  }

  Future loadCategoriasIdiomas() async {
    CategoriaIdiomaResponseModel response =
        (await _repository.getCategoriasIdiomas());
    categoriasIdiomas = response.idiomas;
    niveisIdiomas = response.nivel;
    update();
  }

  Future loadIdiomas() async {
    final response = await _repository.getIdiomas();
    idiomas = response.dados;
    update();
  }

  Future<void> reloadFromCurriculumController() async {
    try {
      final curriculumController = Modular.get<CurriculumController>();
      await curriculumController.reloadSection('idiomas');
    } catch (e) {
      await loadIdiomas();
    }
  }

  Future<bool> saveIdioma() async {
    bool response = false;
    try {
      changeLoading(true);
      response = (await _repository.saveIdioma(idiomaToSave!));
      if (response) {
        await reloadFromCurriculumController();
      }
    } finally {
      changeLoading(false);
    }
    return response;
  }

  Future<bool> deleteIdioma(int? id) async {
    bool response = false;
    try {
      changeLoading(true);
      response = (await _repository.deleteIdioma(id));
      if (response) {
        await reloadFromCurriculumController();
      }
    } finally {
      changeLoading(false);
    }
    return response;
  }
}
