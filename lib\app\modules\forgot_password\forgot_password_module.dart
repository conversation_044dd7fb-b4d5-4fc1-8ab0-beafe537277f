import 'package:flutter_modular/flutter_modular.dart';

import '../../shared/repositories/forgot_password_repository.dart';
import 'forgot_password_controller.dart';
import 'forgot_password_page.dart';

class ForgotPasswordModule extends Module {
  static const route = '/forgot-password';

  @override
  void binds(i) {
    i.addLazy<PERSON>ingleton(ForgotPasswordController.new);
    i.addLazySingleton(ForgotPasswordRepository.new);
  }

  @override
  void routes(r) {
    r.child('/', child: (context) => const ForgotPasswordPage());
  }
}
