import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../security_and_lgpd_controller.dart';

class LogsLgpdPage extends StatelessWidget {
  const LogsLgpdPage({super.key});

  static const route = '/logs-lgpd';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Logs LGPD'),
      ),
      body: Observer(
        builder: (context) {
          var control = Modular.get<SecurityAndLGPDController>();

          if (control.logsLgpd.isEmpty) {
            return const Center(
              child: Text("Nada para mostrar"),
            );
          }

          return ListView.builder(
            itemCount: control.logsLgpd.length,
            itemBuilder: (context, index) {
              return const LogTile(
                title: "Termos",
                date: "12/13/123 13:12",
                origin: "site",
              );
            },
          );
        },
      ),
    );
  }
}

class LogTile extends StatelessWidget {
  const LogTile({
    super.key,
    required this.title,
    required this.date,
    required this.origin,
  });

  final String title;
  final String date;
  final String origin;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ListTile(
          isThreeLine: true,
          title: Text(title),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 8),
              Text(date),
              const SizedBox(height: 5),
              Text(origin),
            ],
          ),
        ),
        const Divider(),
      ],
    );
  }
}
