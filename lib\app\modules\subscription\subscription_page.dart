import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:shimmer/shimmer.dart';

import '../../shared/app_container.dart';
import '../../shared/core/app_config.dart';
import '../../shared/core/app_translation.i18n.dart';
import '../../shared/core/app_utils.dart';
import '../../shared/widgets/app_bar_default.dart';
import '../../shared/widgets/app_default_button.dart';
import '../../shared/widgets/app_info.dart';
import '../jobs/job_detail/job_detail_module.dart';
import '../questionnaire/questionnaire_module.dart';
import 'subscription_controller.dart';

class SubscriptionPage extends StatefulWidget {
  final int? candidaturaID;

  const SubscriptionPage({super.key, this.candidaturaID});

  @override
  _SubscriptionPageState createState() => _SubscriptionPageState();
}

class _SubscriptionPageState extends State<SubscriptionPage> {
  final controller = Modular.get<SubscriptionController>();

  @override
  void initState() {
    super.initState();
    controller.load(widget.candidaturaID);
  }

  @override
  Widget build(BuildContext context) {
    int qIndex = 0;
    return Scaffold(
      backgroundColor: AppConfig.grey,
      appBar: AppBarDefault(titleText: 'Detalhes da Inscrição'.i18n),
      body: SingleChildScrollView(
        child: Observer(
          builder: (_) {
            if (controller.loading) {
              return Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(
                  padding: const EdgeInsets.all(15),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Container(height: 70, color: Colors.white),
                      const SizedBox(height: 20),
                      Container(height: 20, color: Colors.white),
                      const SizedBox(height: 10),
                      Container(height: 10, color: Colors.white),
                      const SizedBox(height: 10),
                      Container(height: 40, color: Colors.white),
                      const SizedBox(height: 10),
                      Container(height: 40, color: Colors.white),
                      const SizedBox(height: 10),
                      Container(height: 100, color: Colors.white),
                      const SizedBox(height: 10),
                      Container(height: 100, color: Colors.white),
                      const SizedBox(height: 10),
                      Container(height: 100, color: Colors.white),
                    ],
                  ),
                ),
              );
            }

            if (controller.inscricao == null) {
              return const SizedBox.shrink();
            }

            return Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                const SizedBox(height: 10),
                AppContainer(
                  color: hexToColor(
                    controller.inscricao!.candidatura!.situacaoCorFundo!,
                  ),
                  child: Column(
                    children: <Widget>[
                      Text(
                        controller.inscricao!.candidatura!.situacaoText ?? '',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: hexToColor(
                            controller
                                .inscricao!.candidatura!.situacaoCorTexto!,
                          ),
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        controller.inscricao!.candidatura!.situacaoDescricao ??
                            '',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: hexToColor(
                            controller
                                .inscricao!.candidatura!.situacaoCorTexto!,
                          ),
                          fontSize: 12,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 12),
                AppContainer(
                  child: Column(
                    children: <Widget>[
                      Text(
                        controller.inscricao!.vaga!.titulo ?? '',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 5),
                      Text(
                        controller.inscricao!.vaga!.empresa ?? '',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 25),
                      SizedBox(
                        width: double.maxFinite,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                const Icon(
                                  Icons.attach_money,
                                  color: Colors.grey,
                                ),
                                const SizedBox(width: 7),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    FittedBox(
                                      child: Text(
                                        'SALÁRIO'.i18n,
                                        style: const TextStyle(
                                          color: Color(0xFF888888),
                                          fontSize: 11,
                                          fontWeight: FontWeight.w300,
                                        ),
                                      ),
                                    ),
                                    FittedBox(
                                      child: Text(
                                        '${controller.inscricao!.vaga!.salario ?? ''} ',
                                        style: const TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    )
                                  ],
                                ),
                              ],
                            ),
                            const SizedBox(width: 10),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                const Icon(Icons.work, color: Colors.grey),
                                const SizedBox(width: 7),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    FittedBox(
                                      child: Text(
                                        'NÍVEL'.i18n,
                                        style: const TextStyle(
                                          fontWeight: FontWeight.w300,
                                          fontSize: 11,
                                          color: Color(0xFF888888),
                                        ),
                                      ),
                                    ),
                                    FittedBox(
                                      child: Text(
                                        controller.inscricao!.vaga!.nivel ?? '',
                                        style: const TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    )
                                  ],
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 18),
                      AppInfo(
                        icon: const Icon(
                          Icons.calendar_today,
                          color: Colors.grey,
                        ),
                        title: Text(
                          'DATA DE INSCRIÇÃO'.i18n,
                          style: const TextStyle(
                            fontSize: 11,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        subtitle: Text(
                          controller.inscricao!.candidatura!.data ?? '',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const SizedBox(height: 18),
                      SizedBox(
                        height: 45,
                        width: double.infinity,
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppConfig.red,
                          ),
                          onPressed: () {
                            _cancelarInscricao(context);
                          },
                          child: Text(
                            'CANCELAR INSCRIÇÃO'.i18n,
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 18),
                      SizedBox(
                        height: 45,
                        width: double.infinity,
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppConfig.colorPrimary,
                          ),
                          onPressed: () {
                            Modular.to.pushNamed(JobDetailModule.route,
                                arguments: controller.inscricao!.vaga!.id);
                          },
                          child: Text(
                            'VISUALIZAR VAGA'.i18n,
                            style: const TextStyle(
                              fontWeight: FontWeight.w500,
                              color: Colors.white,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 12),
                ...controller.inscricao!.questionarios!.map(
                  (q) {
                    qIndex++;
                    return Column(
                      children: <Widget>[
                        Material(
                          color: Colors.white,
                          child: InkWell(
                            onTap: q.situacao != 'Respondido'
                                ? () {
                                    Modular.to.pushNamed(
                                        QuestionnaireModule.route,
                                        arguments: q.id);
                                  }
                                : null,
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 15,
                                vertical: 10,
                              ),
                              color: Colors.transparent,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: <Widget>[
                                  Visibility(
                                    visible: qIndex == 1,
                                    child: Column(
                                      children: <Widget>[
                                        Text('Testes e Questionários'.i18n),
                                        const SizedBox(height: 25)
                                      ],
                                    ),
                                  ),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: <Widget>[
                                      Expanded(
                                        child: Text(q.questionarioTitulo ?? ''),
                                      ),
                                      Visibility(
                                        visible: q.situacao != 'Respondido',
                                        child: const Text(
                                          'RESPONDER',
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: AppConfig.colorPrimary,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 8),
                                  Row(
                                    children: <Widget>[
                                      Text(
                                        'Situação: '.i18n,
                                        style: const TextStyle(
                                          color: Colors.grey,
                                        ),
                                      ),
                                      Text(
                                        q.situacao ?? '',
                                        style: TextStyle(
                                          color: hexToColor(q.situacaoCor!),
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 8),
                                  Visibility(
                                    visible: q.responderAte != '',
                                    child: Row(
                                      children: <Widget>[
                                        Text(
                                          'Responder até: '.i18n,
                                          style: const TextStyle(
                                              color: Colors.grey),
                                        ),
                                        Text(
                                          q.responderAte!,
                                          style: const TextStyle(
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Row(
                                    children: <Widget>[
                                      Text(
                                        'Solicitado: '.i18n,
                                        style: const TextStyle(
                                          color: Colors.grey,
                                        ),
                                      ),
                                      Text(
                                        q.solicitado!,
                                        style: const TextStyle(
                                          color: Colors.grey,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 8),
                                  Visibility(
                                    visible: q.iniciado!.isNotEmpty,
                                    child: Row(
                                      children: <Widget>[
                                        Text(
                                          'Iniciado: '.i18n,
                                          style: const TextStyle(
                                            color: Colors.grey,
                                          ),
                                        ),
                                        Text(
                                          q.iniciado ?? '',
                                          style: const TextStyle(
                                            color: Colors.grey,
                                            fontSize: 12,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Visibility(
                                    visible: q.respondido!.isNotEmpty,
                                    child: Row(
                                      children: <Widget>[
                                        Text(
                                          'Respondido: '.i18n,
                                          style: const TextStyle(
                                            color: Colors.grey,
                                          ),
                                        ),
                                        Text(
                                          q.respondido!,
                                          style: const TextStyle(
                                            color: Colors.grey,
                                            fontSize: 12,
                                          ),
                                        ),
                                      ],
                                    ),
                                  )
                                ],
                              ),
                            ),
                          ),
                        ),
                        const Divider(height: 1),
                      ],
                    );
                  },
                ),
                const SizedBox(height: 20),
              ],
            );
          },
        ),
      ),
    );
  }

  void _cancelarInscricao(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => SimpleDialog(
        contentPadding: const EdgeInsets.all(15),
        title: const Text(
          'Confirma o cancelamento da inscrição ?',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 20,
          ),
        ),
        children: <Widget>[
          Row(
            children: <Widget>[
              Expanded(
                child: AppDefaultButton(
                  color: AppConfig.red,
                  onPressed: () => Modular.to.pop(),
                  title: const Text(
                    'NÃO',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ),
              const SizedBox(
                width: 5,
              ),
              Expanded(
                child: AppDefaultButton(
                  color: Colors.green,
                  onPressed: () => Modular.to.pop(),
                  title: const Text(
                    'SIM',
                    style: TextStyle(
                      color: Colors.white,
                    ),
                  ),
                ),
              )
            ],
          ),
        ],
      ),
    );
  }
}
