import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class ShimmerDetailsJob extends StatelessWidget {
  const ShimmerDetailsJob({super.key});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: const Padding(
        padding: EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                ColoredBox(
                  color: Colors.white,
                  child: SizedBox(width: 55, height: 14),
                ),
                SizedBox(width: 20),
                ColoredBox(
                  color: Colors.white,
                  child: SizedBox(width: 55, height: 14),
                ),
              ],
            ),
            SizedBox(height: 20),
            Row(
              children: [
                ColoredBox(
                  color: Colors.white,
                  child: SizedBox(width: 150, height: 14),
                ),
              ],
            ),
            SizedBox(height: 30),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                ColoredBox(
                  color: Colors.white,
                  child: SizedBox(width: 75, height: 14),
                ),
                SizedBox(height: 5),
                ColoredBox(
                  color: Colors.white,
                  child: SizedBox(width: 25, height: 14),
                ),
                SizedBox(height: 10),
                ColoredBox(
                  color: Colors.white,
                  child: SizedBox(width: 100, height: 14),
                ),
                SizedBox(height: 5),
                ColoredBox(
                  color: Colors.white,
                  child: SizedBox(width: 50, height: 14),
                ),
                SizedBox(height: 10),
                ColoredBox(
                  color: Colors.white,
                  child: SizedBox(width: 100, height: 14),
                ),
                SizedBox(height: 5),
                ColoredBox(
                  color: Colors.white,
                  child: SizedBox(width: 35, height: 14),
                ),
                SizedBox(height: 10),
                ColoredBox(
                  color: Colors.white,
                  child: SizedBox(width: 75, height: 14),
                ),
                SizedBox(height: 5),
                ColoredBox(
                  color: Colors.white,
                  child: SizedBox(width: 25, height: 14),
                ),
                SizedBox(height: 10),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
