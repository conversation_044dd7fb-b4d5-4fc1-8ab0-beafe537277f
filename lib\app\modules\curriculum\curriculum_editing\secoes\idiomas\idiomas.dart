import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../../../../shared/models/responses/categorias_idiomas_response_model.dart';
import '../../../../../shared/widgets/app_dropdown_search.dart';
import '../../../../../shared/widgets/app_text_form_field.dart';
import '../../../../curriculum/curriculum_controller.dart';
import '../../curriculum_editing_controller.dart';
import '../../widgets/secao.dart';

class Idiomas extends StatefulWidget {
  const Idiomas({super.key});

  @override
  State<Idiomas> createState() => _IdiomasState();
}

class _IdiomasState extends State<Idiomas> {
  final CurriculumController _curriculumController = Modular.get();
  final controller = Modular.get<CurriculumEditingController>();

  @override
  Widget build(BuildContext context) {
    return Secao(
      fields: Observer(builder: (_) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(
                  top: _curriculumController.languages.idiomaToSave?.idiomaID !=
                          null
                      ? 8
                      : 0),
              child: DropdownSearch(
                context,
                hintText: 'IDIOMA'.i18n,
                title: 'IDIOMA'.i18n,
                items: _curriculumController.languages.categoriasIdiomas!
                    .map((e) => DropdownSearchItem(
                          value: e,
                          searchKey: e.nome!,
                          text: e.nome,
                          child: Text(e.nome!),
                        ))
                    .toList(),
                onSelected: (dynamic item) {
                  if (item != null) {
                    _curriculumController.languages.idiomaToSave!.idiomaID =
                        item.id;
                    _curriculumController.languages.idiomaToSave!.nome =
                        item.nome;
                  }
                },
                value: CategoriaIdiomaModel(
                  id: _curriculumController.languages.idiomaToSave?.idiomaID,
                  nome: _curriculumController.languages.idiomaToSave?.nome,
                ),
              ),
            ),
            const SizedBox(height: 5),
            Padding(
              padding: EdgeInsets.only(
                top: _curriculumController.languages.idiomaToSave?.nivel != null
                    ? 8
                    : 0,
              ),
              child: DropdownSearch(
                context,
                hintText: 'NÍVEL'.i18n,
                title: 'NÍVEL'.i18n,
                items: _curriculumController.languages.niveisIdiomas!
                    .map((e) => DropdownSearchItem(
                          value: e,
                          searchKey: e.nome!,
                          text: e.nome,
                          child: Text(e.nome!),
                        ))
                    .toList(),
                onSelected: (dynamic item) {
                  if (item != null) {
                    _curriculumController.languages.idiomaToSave!.nivel =
                        int.parse(item.id);
                    _curriculumController.languages.idiomaToSave!.nivelString =
                        item.nome;
                  }
                },
                value: NivelIdiomaModel(
                  id: _curriculumController.languages.idiomaToSave?.nivel
                      .toString(),
                  nome:
                      _curriculumController.languages.idiomaToSave?.nivelString,
                ),
              ),
            ),
            const SizedBox(height: 5),
            Padding(
              padding: const EdgeInsets.only(top: 5, bottom: 5, left: 3),
              child: Text(
                'EXPERIÊNCIAS INTERNACIONAIS'.i18n,
                style: const TextStyle(
                  fontSize: 14,
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
            DecoratedBox(
              decoration: BoxDecoration(
                color: const Color(0xFFf3f3fc).withValues(alpha: 0.5),
                borderRadius: const BorderRadius.all(
                  Radius.circular(4),
                ),
                border: Border.all(
                  color: const Color(0xFFdee2e6),
                ),
              ),
              child: AppTextFormField(
                hintText:
                    'Comente caso você teve alguma experiência fora do país...',
                minLines: 5,
                maxLines: 10,
                controller: TextEditingController(
                  text: _curriculumController.languages.idiomaToSave!.descricao,
                ),
                onChanged: (value) {
                  _curriculumController.languages.idiomaToSave!.descricao =
                      value;
                },
              ),
            ),
          ],
        );
      }),
    );
  }
}
