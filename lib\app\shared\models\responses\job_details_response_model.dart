class JobDetailsResponseModel {
  final int? id;
  final String? url;
  final String? titulo;
  final String? salario;
  final String? nivel;
  final bool? selecaoCega;
  final List<CidadeModel>? cidades;
  final String? horario;
  final String? contratacao;
  final String? descricao;
  final String? requisito;
  final String? beneficio;
  final String? empresa;
  final String? logo;
  final int? modalidade;
  final String? modalidadeTitulo;
  final String? dataCadastro;
  final String? dataAtualizacao;
  final List<VagasRelacionadasModel>? vagasRelacionadas;

  JobDetailsResponseModel({
    this.id,
    this.url,
    this.titulo,
    this.salario,
    this.nivel,
    this.selecaoCega,
    this.cidades,
    this.horario,
    this.contratacao,
    this.descricao,
    this.requisito,
    this.beneficio,
    this.empresa,
    this.logo,
    this.modalidade,
    this.modalidadeTitulo,
    this.dataCadastro,
    this.dataAtualizacao,
    this.vagasRelacionadas,
  });

  factory JobDetailsResponseModel.fromJson(Map<String, dynamic> json) {
    return JobDetailsResponseModel(
      id: json['id'] as int?,
      url: json['url'] as String?,
      titulo: json['titulo'] as String?,
      salario: json['salario'] as String?,
      nivel: json['nivel'] as String?,
      selecaoCega: json['selecaoCega'] as bool?,
      cidades: (json['cidades'] as List<dynamic>?)
          ?.map((e) => CidadeModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      horario: json['horario'] as String?,
      contratacao: json['contratacao'] as String?,
      descricao: json['descricao'] as String?,
      requisito: json['requisito'] as String?,
      beneficio: json['beneficio'] as String?,
      empresa: json['empresa'] as String?,
      logo: json['logo'] as String?,
      modalidade: json['modalidade'] as int?,
      modalidadeTitulo: json['modalidadeTitulo'] as String?,
      dataCadastro: json['dataCadastro'] as String?,
      dataAtualizacao: json['dataAtualizacao'] as String?,
      vagasRelacionadas: (json['vagasRelacionadas'] as List<dynamic>?)
          ?.map(
              (e) => VagasRelacionadasModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class CidadeModel {
  final int? id;
  final String? nome;

  CidadeModel({this.id, this.nome});

  factory CidadeModel.fromJson(Map<String, dynamic> json) {
    return CidadeModel(
      id: json['id'] as int?,
      nome: json['nome'] as String?,
    );
  }
}

class VagasRelacionadasModel {
  final String? url;
  final String? logo;
  final String? titulo;
  final String? empresa;
  final String? cidades;

  VagasRelacionadasModel({
    this.url,
    this.logo,
    this.titulo,
    this.empresa,
    this.cidades,
  });

  factory VagasRelacionadasModel.fromJson(Map<String, dynamic> json) {
    return VagasRelacionadasModel(
      url: json['url'] as String?,
      logo: json['logo'] as String?,
      titulo: json['titulo'] as String?,
      empresa: json['empresa'] as String?,
      cidades: json['cidades'] as String?,
    );
  }
}
