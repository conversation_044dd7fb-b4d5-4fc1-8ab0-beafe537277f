import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:image_crop_plus/image_crop_plus.dart';

import '../../shared/core/app_config.dart';
import 'imagecrop_controller.dart';

class ImagecropPage extends StatefulWidget {
  final File? image;

  const ImagecropPage({super.key, this.image});

  @override
  _ImagecropPageState createState() => _ImagecropPageState();
}

class _ImagecropPageState extends State<ImagecropPage> {
  File? _file;
  final ImagecropController controller = Modular.get();

  @override
  void initState() {
    super.initState();
    _file = widget.image;
    controller.start();
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarBrightness: Brightness.dark,
      statusBarIconBrightness: Brightness.light,
      systemNavigationBarIconBrightness: Brightness.light,
    ));
  }

  @override
  void dispose() {
    Modular.dispose<ImagecropController>();
    _file?.delete();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: SafeArea(
        child: Container(
          color: Colors.black,
          padding: const EdgeInsets.symmetric(vertical: 40.0, horizontal: 20.0),
          child: _buildCroppingImage(),
        ),
      ),
    );
  }

  Widget _buildCroppingImage() {
    return Observer(
      builder: (_) {
        return Stack(
          children: [
            Column(
              children: <Widget>[
                Expanded(
                  child: Crop.file(_file!,
                      key: controller.cropKey, aspectRatio: 1),
                ),
                Container(
                  padding: const EdgeInsets.only(top: 20.0),
                  alignment: AlignmentDirectional.center,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: <Widget>[
                      TextButton(
                        child: Text(
                          'Salvar Foto',
                          style: Theme.of(context)
                              .textTheme
                              .labelLarge!
                              .copyWith(color: Colors.white),
                        ),
                        onPressed: () => controller.cropImage(_file),
                      ),
                    ],
                  ),
                )
              ],
            ),
            Visibility(
                visible: controller.loading,
                child: Container(
                  color: Colors.black,
                  child: const Center(
                    child: SpinKitCircle(color: AppConfig.white),
                  ),
                )),
          ],
        );
      },
    );
  }
}
