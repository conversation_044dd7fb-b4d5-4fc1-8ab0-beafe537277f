import 'dart:io';

import 'package:file_picker/file_picker.dart';

class $FilePickerLib {
  Future<File?> pickFile({
    List<String>? allowedExtensions,
    bool allowCompression = true,
  }) async {
    var result = await FilePicker.platform.pickFiles(
      type: allowedExtensions == null ? FileType.any : FileType.custom,
      allowedExtensions: allowedExtensions,
      allowCompression: allowCompression,
    );
    return result != null ? File(result.files.elementAt(0).path!) : null;
  }
}

// ignore: non_constant_identifier_names
final $FilePickerLib FilePickerLib = $FilePickerLib();
