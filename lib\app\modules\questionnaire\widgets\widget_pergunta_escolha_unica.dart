import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../shared/app_container.dart';
import '../../../shared/core/app_config.dart';
import '../../../shared/core/app_translation.i18n.dart';
import '../../../shared/widgets/app_html.dart';
import '../questionnaire_controller.dart';

class WidgetPerguntaEscolhaUnica extends StatefulWidget {
  const WidgetPerguntaEscolhaUnica({
    super.key,
    required this.onChanged,
  });

  final Function(int?)? onChanged;

  @override
  State<WidgetPerguntaEscolhaUnica> createState() =>
      _WidgetPerguntaEscolhaUnicaState();
}

class _WidgetPerguntaEscolhaUnicaState
    extends State<WidgetPerguntaEscolhaUnica> {
  final controller = Modular.get<QuestionnaireController>();

  @override
  Widget build(BuildContext context) {
    return AppContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 25, vertical: 10),
            child: Text(
              'SELECIONE UMA ALTERNATIVA:'.i18n,
              textAlign: TextAlign.left,
            ),
          ),
          Column(
            children: List.generate(
              controller.pergunta!.alternativas!.length,
              (i) {
                var alternativa = controller.pergunta!.alternativas![i];

                return Column(
                  children: [
                    const Divider(
                      height: 3,
                      color: Colors.grey,
                    ),
                    RadioListTile<int?>(
                      activeColor: AppConfig.colorPrimary,
                      controlAffinity: ListTileControlAffinity.platform,
                      selected: controller.alternativa == alternativa.id,
                      value: alternativa.id,
                      dense: true,
                      title: ConstrainedBox(
                        constraints: const BoxConstraints(
                          maxHeight: 150,
                        ),
                        child: AppHtml(
                          data: alternativa.descricao,
                        ),
                      ),
                      contentPadding: EdgeInsets.zero,
                      onChanged: widget.onChanged,
                      groupValue: controller.alternativa,
                      key: Key(alternativa.id.toString()),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
