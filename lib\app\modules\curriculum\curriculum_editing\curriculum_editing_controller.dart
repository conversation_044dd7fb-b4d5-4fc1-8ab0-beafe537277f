import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_holo_date_picker/date_picker.dart';
import 'package:flutter_holo_date_picker/i18n/date_picker_i18n.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:get/get.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../../shared/core/app_config.dart';
import '../../../shared/core/app_utils.dart';
import '../../../shared/libs/file_picker_lib.dart';
import '../../../shared/models/responses/listar_deficiencias_response_model.dart';
import '../../../shared/repositories/curriculum_repository.dart';
import '../../../shared/widgets/app_default_button.dart';
import '../controllers/deficiencies_controller.dart';

class CurriculumEditingController extends GetxController {
  final CurriculumRepository _curriculumRepository = Modular.get();
  final DeficienciesController _deficienciesController = Modular.get();

  String? formacaoAcademica;

  bool naoPossuoDeficiencia = false;

  String? selectedOption;

  int? selectedDiversidadeOption;

  bool diretrizesLeiCotasPcd = false;

  bool? uploadingLaudo;

  File? _anexo;

  File? get anexo => _anexo;

  String? codeWhatsapp;
  String? codeCountryWhatsapp;

  String? codeCel1;

  String? codeCel2;

  int? pronomeAltDiversidade;

  int? orientacaoAltDiversidade;

  int? identidadeAltDiversidade;

  int? racaAltDiversidade;

  bool? naoInformarDiversidade;

  Future<String?> selecionarArquivo() async {
    uploadingLaudo = true;
    update();
    _anexo =
        await FilePickerLib.pickFile(allowedExtensions: ['jpg', 'pdf', 'png']);
    if (_anexo != null) {
      int fileSize = _anexo!.lengthSync();
      if (fileSize > (5 * umMb)) {
        uploadingLaudo = false;
        update();
        return 'Arquivo ultrapassa o limite de 5MB';
      }

      try {
        await _curriculumRepository.uploadLaudo(file: _anexo!);
      } catch (e) {
        uploadingLaudo = false;
        update();
        return e.toString();
      }
    }
    uploadingLaudo = false;
    update();
    return null;
  }

  void changeCodeWhatsapp(String? newCode, {bool shouldUpdate = true}) {
    codeWhatsapp = newCode;
    // if (shouldUpdate) {
    update();
    // }
  }

  void changeCodeCel1(String? newCode, {bool shouldUpdate = true}) {
    codeCel1 = newCode;
    // if (shouldUpdate) {
    update();
    // }
  }

  void changeCodeCel2(String? newCode) {
    codeCel2 = newCode;
    update();
  }

  void changeDiretrizesLeiCotasPcd([bool? newValue, bool shouldUpdate = true]) {
    if (newValue != null) {
      diretrizesLeiCotasPcd = newValue;
    } else {
      diretrizesLeiCotasPcd = !diretrizesLeiCotasPcd;
    }
    // if (shouldUpdate) {
    update();
    // }
  }

  Future<DateTime?> selectDate(
    BuildContext context,
    String title,
    DateTime? currentDate, {
    String format = 'MMMM-yyyy',
  }) async {
    final DateTime now = DateTime.now();
    final DateTime hundredYears = now.subtract(const Duration(days: 36500));

    var datePicked = await DatePicker.showSimpleDatePicker(
      context,
      titleText: title,
      cancelText: 'Cancelar'.i18n,
      initialDate: currentDate ?? now,
      firstDate: hundredYears,
      lastDate: now,
      dateFormat: format,
      locale: DateTimePickerLocale.pt_br,
    );
    return datePicked;
  }

  void showSnackbarErro(BuildContext context, String mensagem) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        behavior: SnackBarBehavior.floating,
        backgroundColor: Colors.redAccent,
        content: Text(mensagem),
      ),
    );
  }

  void showSnackbarSucesso(BuildContext context, String mensagem) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        backgroundColor: Colors.green,
        content: Text(mensagem),
      ),
    );
  }

  void showSnackBarRequiredField(String fieldName, BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        behavior: SnackBarBehavior.floating,
        backgroundColor: AppConfig.red,
        content: Text('Campo %s é obrigatório'.i18n.fill([fieldName])),
      ),
    );
  }

  Future<void> changeSelectedOption(String type,
      {bool shouldUpdate = true}) async {
    if (selectedOption == type) {
      selectedOption = null;
    } else {
      selectedOption = type;
    }

    if (selectedOption == "reabilitadoPeloInss") {
      _deficienciesController.deficienciasToSave.clear();
      _deficienciesController.addDeficienciaToSave(
        _deficienciesController.categoriasDeficiencias.firstWhere((d) {
          return d.nome?.contains("INSS") ?? false;
        }),
        'Reabilitado',
      );
    } else {
      _deficienciesController.removeDeficienciaToSave(
        _deficienciesController.deficienciasToSave.firstWhereOrNull((d) {
          return d.nome?.contains("INSS") ?? false;
        })?.deficienciaID,
      );
    }

    naoPossuoDeficiencia = selectedOption == "naoPossuo";
    if (naoPossuoDeficiencia) {
      _deficienciesController.deficienciasToSave.clear();
    }

    if (shouldUpdate) {
      update();
    }
  }

  Future<bool?> showDialogConfirmDelete(
      BuildContext context, String secao) async {
    return await showDialog(
      context: context,
      builder: (_) {
        return AlertDialog(
          title: Text('Remover $secao'),
          content: Text('Deseja realmente remover este(a) $secao?'),
          actions: [
            AppDefaultButton(
              color: AppConfig.red,
              title: Text(
                'Sim, remover'.i18n,
                style: const TextStyle(color: Colors.white),
              ),
              width: 120,
              onPressed: () => Modular.to.pop(true),
            ),
            AppDefaultButton(
              color: AppConfig.grey,
              title: Text('Cancelar'.i18n),
              width: 120,
              onPressed: () => Modular.to.pop(false),
            ),
          ],
        );
      },
    );
  }

  Future<void> onRemoveLaudo(
      BuildContext context, DeficienciaLaudoModel toRemove) async {
    if ((await showDialogConfirmDelete(context, 'Laudo'))!) {
      var result = await _deficienciesController.deleteLaudo(toRemove.id);
      if (result == true) {
        showSnackbarSucesso(context, 'Laudo removido com sucesso');
        _deficienciesController.loadDeficiencias();
      }
    }
  }

  void showDialogUploadLaudo(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (_) {
        return SimpleDialog(
          contentPadding: const EdgeInsets.symmetric(
            vertical: 20,
            horizontal: 30,
          ),
          title: Text(
            'Carregar arquivo'.i18n,
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 20,
              color: Colors.lightBlueAccent,
            ),
          ),
          children: [
            AppDefaultButton(
              color: AppConfig.colorPrimary,
              onPressed: () async {
                Modular.to.pop();
                String? error = await selecionarArquivo();
                if (error != null) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      behavior: SnackBarBehavior.floating,
                      backgroundColor: Colors.redAccent,
                      content: Text(error),
                    ),
                  );
                } else {
                  _deficienciesController.loadDeficiencias();
                }
              },
              title: Text(
                'Selecionar arquivo'.i18n,
                style: const TextStyle(color: AppConfig.white),
              ),
            ),
            const SizedBox(height: 10),
            Text(
              'Somente arquivos nos formatos: .pdf, .jpg ou .png'.i18n,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 12,
              ),
            ),
            const SizedBox(height: 5),
            Text(
              'Tamanho máximo: 5MB'.i18n,
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 12),
            ),
            const Text(
              'Aperte o botão acima para selecionar o arquivo e enviá-lo.',
              textAlign: TextAlign.center,
            ),
          ],
        );
      },
    );
  }
}
