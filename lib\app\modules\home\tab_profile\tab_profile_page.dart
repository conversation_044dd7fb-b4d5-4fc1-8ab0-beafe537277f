import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import 'tab_profile_controller.dart';
import 'utils/profile_body.dart';
import 'utils/progress_indicator_app_bar.dart';

class TabProfilePage extends StatefulWidget {
  const TabProfilePage({super.key});

  @override
  _TabProfilePageState createState() => _TabProfilePageState();
}

class _TabProfilePageState extends State<TabProfilePage> {
  @override
  void dispose() {
    Modular.dispose<TabProfileController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        elevation: 1,
        backgroundColor: Colors.white,
        toolbarHeight: 75,
        shadowColor: Colors.black38,
        title: ProgressIndicatorAppBar(
          isNotReturn: true,
        ),
      ),
      body: const ProfileBody(),
    );
  }
}
