class FormacaoResponseModel {
  final bool sucesso;
  final String mensagem;
  final List<FormacaoModel>? dados;

  FormacaoResponseModel({this.sucesso = false, this.mensagem = "", this.dados});

  factory FormacaoResponseModel.fromJson(Map<String, dynamic> json) {
    return FormacaoResponseModel(
      sucesso: json['sucesso'] ?? false,
      mensagem: json['mensagem'] ?? "",
      dados: (json['dados'] as List<dynamic>?)
          ?.map((e) => FormacaoModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class FormacaoModel {
  int? id;
  int? grauID;
  String? grau;
  String? local;
  String? curso;
  String? mesInicio;
  String? mesFim;
  String? anoInicio;
  String? anoFim;
  bool? andamento;
  String? andamentoStr;
  bool? incompleto;

  FormacaoModel({
    this.id,
    this.grauID,
    this.grau,
    this.local,
    this.curso,
    this.mesInicio,
    this.mesFim,
    this.anoInicio,
    this.anoFim,
    this.andamento,
    this.andamentoStr,
    this.incompleto,
  });

  factory FormacaoModel.fromJson(Map<String, dynamic> json) => FormacaoModel(
        id: json['id'] as int?,
        grauID: json['grauID'] as int?,
        grau: json['grau'] as String?,
        local: json['local'] as String?,
        curso: json['curso'] as String?,
        mesInicio: json['mesInicio'] as String?,
        mesFim: json['mesFim'] as String?,
        anoInicio: json['anoInicio'] as String?,
        anoFim: json['anoFim'] as String?,
        andamento: json['andamento'] as bool?,
        andamentoStr: json['andamentoStr'] as String?,
        incompleto: json['incompleto'] as bool?,
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'grauID': grauID,
        'grau': grau,
        'local': local,
        'curso': curso,
        'mesInicio': mesInicio,
        'mesFim': mesFim,
        'anoInicio': anoInicio,
        'anoFim': anoFim,
        'andamento': andamento,
        'andamentoStr': andamentoStr,
        'incompleto': incompleto,
      };

  FormacaoModel copy() {
    return FormacaoModel(
      id: id,
      grauID: grauID,
      grau: grau,
      local: local,
      curso: curso,
      mesInicio: mesInicio,
      mesFim: mesFim,
      anoInicio: anoInicio,
      anoFim: anoFim,
      andamento: andamento,
      andamentoStr: andamentoStr,
      incompleto: incompleto,
    );
  }

  bool get isEnsinoFundamental =>
      grauID == null || grauID == 13 || grauID == 16;

  bool get isEnsinoMedio => grauID == null || grauID == 1 || grauID == 17;

  void setEnsinoMedio() {
    curso = 'Ensino Médio';
    local = null;
    mesInicio = null;
    anoInicio = null;
    mesFim = null;
    anoFim = null;
    incompleto = false;
  }

  void setEnsinoFundamental() {
    local = 'Ensino Fundamental';
    curso = 'Ensino Fundamental';
    mesInicio = '01';
    anoInicio = '1931';
    mesFim = '01';
    anoFim = '1932';
  }

  void setEnsinoSuperior() {
    local = null;
    curso = null;
    mesInicio = null;
    anoInicio = null;
    mesFim = null;
    anoFim = null;
  }
}
