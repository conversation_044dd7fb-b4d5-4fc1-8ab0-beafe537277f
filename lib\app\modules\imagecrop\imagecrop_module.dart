import 'package:flutter_modular/flutter_modular.dart';

import 'imagecrop_controller.dart';
import 'imagecrop_page.dart';

class ImagecropModule extends Module {
  static const route = '/imagecrop';

  @override
  void binds(i) {
    i.add<PERSON>azy<PERSON>ingleton(ImagecropController.new);
  }

  @override
  void routes(r) {
    r.child(
      Modular.initialRoute,
      child: (context) => ImagecropPage(image: r.args.data),
    );
  }
}
