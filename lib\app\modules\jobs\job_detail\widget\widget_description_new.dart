import 'package:flutter/material.dart';

import '../../../../shared/widgets/app_html.dart';
import '../job_detail_controller.dart';

class WidgetDescripitionNew extends StatelessWidget {
  const WidgetDescripitionNew({
    super.key,
    required this.controllerJob,
  });

  final JobDetailController controllerJob;
  @override
  Widget build(BuildContext context) {
    final descricao = controllerJob.getJobDescription();

    if (descricao.isEmpty) {
      return const SizedBox.shrink();
    }
    String texto = descricao;

    RegExp exp = RegExp(r'<[^>]*>$');

    while (exp.hasMatch(texto)) {
      texto = texto.replaceFirst(exp, '');
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Descrição e Responsabilidades',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontFamily: 'Inter',
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 10),
        const Text(
          'Missão do Cargo:',
          style: TextStyle(
            color: Colors.black,
            fontSize: 14,
            fontFamily: 'Inter',
            fontWeight: FontWeight.w700,
          ),
        ),
        const SizedBox(height: 10),
        AppHtml(data: texto),
      ],
    );
  }
}
