class JobInsightModel {
  final int? vagaID;
  final String? origem;
  final String? url;
  final String? dispositivo;

  JobInsightModel({
    this.vagaID,
    this.origem = "App",
    this.url = "www.empregare.com",
    this.dispositivo = "Mobile",
  });

  Map<String, dynamic> toJson() {
    return {
      'vagaID': vagaID,
      'origem': origem,
      'url': url,
      'dispositivo': dispositivo,
    };
  }
}
