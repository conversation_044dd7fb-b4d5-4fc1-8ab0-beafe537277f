import 'package:flutter/material.dart';

class CheckBoxWidget extends StatelessWidget {
  final void Function() onTap;
  final void Function(bool?)? onChanged;
  final bool value;

  const CheckBoxWidget({
    super.key,
    required this.onTap,
    this.onChanged,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Row(
        children: [
          Checkbox(
            value: value,
            onChanged: onChanged,
          ),
          const Expanded(
            child: Text(
              'Concordo em disponibilizar meus dados às empresas para possibilitar a implementação de iniciativas focadas no fomento à diversidade.',
            ),
          ),
        ],
      ),
    );
  }
}
