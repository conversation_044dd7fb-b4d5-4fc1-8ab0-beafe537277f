class JobParameterModel {
  String? query;
  String? localidade;
  int? ordenacao;
  int? dataPublicacao;
  List<String>? modalidade;
  List<String>? empresa;
  List<String>? nivel;
  bool? pcd;
  bool? selecaoCega;
  List<int>? tipoRecrutamento;
  List<String>? pais;
  List<String>? estado;
  List<String>? regime;
  List<String>? subArea;
  List<String>? empresaUnidade;
  int pagina;
  int? itensPagina;
  int? divisao;
  String? empresaID;
  String? hotSiteUrl;
  List<String>? camposDinamicos;
  List<String>? camposDinamicosIDs;

  List<String>? vaga;
  HotSiteSegmentacaoModel? hotSiteSegmentacao;
  num? lat;
  num? lon;
  double? km;
  String? q;
  String? c;
  List<String>? cidade;
  List<String>? uf;
  String? cargo;
  bool? carregarLista;
  bool? carregarFiltro;

  int pag;
  int? itensPag;

  JobParameterModel({
    this.query,
    this.localidade,
    this.ordenacao,
    this.dataPublicacao,
    this.modalidade,
    this.empresa,
    this.nivel,
    this.pcd,
    this.selecaoCega,
    this.tipoRecrutamento,
    this.pais,
    this.estado,
    this.regime,
    this.subArea,
    this.empresaUnidade,
    this.pagina = 1,
    this.itensPagina,
    this.divisao,
    this.empresaID,
    this.hotSiteUrl,
    this.camposDinamicos,
    this.camposDinamicosIDs,
    this.lat,
    this.lon,
    this.km,
    this.q,
    this.c,
    this.cidade,
    this.uf,
    this.cargo,
    this.carregarLista,
    this.carregarFiltro = true,
    this.pag = 1,
    this.itensPag,
  });

  void nextPage() => pag++;

  Map<String, dynamic> toJson() {
    {
      final val = <String, dynamic>{};

      void writeNotNull(String key, dynamic value) {
        if (value != null) {
          val[key] = value;
        }

        if (value is List && value.isEmpty) {
          val.remove(key);
        }
      }

      writeNotNull('vaga', vaga);
      writeNotNull('hotSiteSegmentacao', hotSiteSegmentacao?.toJson());
      writeNotNull('ordenacao', ordenacao);
      writeNotNull('query', query);
      writeNotNull('lat', lat);
      writeNotNull('lon', lon);
      writeNotNull('km', km);
      writeNotNull('q', q);
      writeNotNull('c', c);
      writeNotNull('pais', pais);
      writeNotNull('cidade', cidade);
      writeNotNull('selecaoCega', selecaoCega);
      writeNotNull('uf', uf);
      writeNotNull('cargo', cargo);
      writeNotNull('nivel', nivel);
      writeNotNull('regime', regime);
      writeNotNull('tipoRecrutamento', tipoRecrutamento);
      writeNotNull('pcd', pcd);
      writeNotNull('divisao', divisao);
      writeNotNull('carregarLista', carregarLista);
      writeNotNull('carregarFiltro', carregarFiltro);
      writeNotNull('empresa', empresa);
      writeNotNull('empresaID', empresaID);
      writeNotNull('hotSiteUrl', hotSiteUrl);
      val['pag'] = pag;
      writeNotNull('itensPag', itensPag);
      return val;
    }
  }

  JobParameterModel copyWith({
    String? query,
    String? localidade,
    int? ordenacao,
    int? dataPublicacao,
    List<String>? modalidade,
    List<String>? empresa,
    List<String>? nivel,
    bool? pcd,
    bool? selecaoCega,
    List<int>? tipoRecrutamento,
    List<String>? pais,
    List<String>? estado,
    List<String>? regime,
    List<String>? subArea,
    List<String>? empresaUnidade,
    int? pagina,
    int? itensPagina,
    int? divisao,
    String? empresaID,
    String? hotSiteUrl,
    List<String>? camposDinamicos,
    List<String>? camposDinamicosIDs,
    List<String>? vaga,
    HotSiteSegmentacaoModel? hotSiteSegmentacao,
    num? lat,
    num? lon,
    double? km,
    String? q,
    String? c,
    List<String>? cidade,
    List<String>? uf,
    String? cargo,
    bool? carregarLista,
    bool? carregarFiltro,
    int? pag,
    int? itensPag,
  }) {
    return JobParameterModel(
      query: query ?? this.query,
      localidade: localidade ?? this.localidade,
      ordenacao: ordenacao ?? this.ordenacao,
      dataPublicacao: dataPublicacao ?? this.dataPublicacao,
      modalidade: modalidade ?? this.modalidade,
      empresa: empresa ?? this.empresa,
      nivel: nivel ?? this.nivel,
      pcd: pcd ?? this.pcd,
      selecaoCega: selecaoCega ?? this.selecaoCega,
      tipoRecrutamento: tipoRecrutamento ?? this.tipoRecrutamento,
      pais: pais ?? this.pais,
      estado: estado ?? this.estado,
      regime: regime ?? this.regime,
      subArea: subArea ?? this.subArea,
      empresaUnidade: empresaUnidade ?? this.empresaUnidade,
      pagina: pagina ?? this.pagina,
      itensPagina: itensPagina ?? this.itensPagina,
      divisao: divisao ?? this.divisao,
      empresaID: empresaID ?? this.empresaID,
      hotSiteUrl: hotSiteUrl ?? this.hotSiteUrl,
      camposDinamicos: camposDinamicos ?? this.camposDinamicos,
      camposDinamicosIDs: camposDinamicosIDs ?? this.camposDinamicosIDs,
      lat: lat ?? this.lat,
      lon: lon ?? this.lon,
      km: km ?? this.km,
      q: q ?? this.q,
      c: c ?? this.c,
      cidade: cidade ?? this.cidade,
      uf: uf ?? this.uf,
      cargo: cargo ?? this.cargo,
      carregarLista: carregarLista ?? this.carregarLista,
      carregarFiltro: carregarFiltro ?? this.carregarFiltro,
      pag: pag ?? this.pag,
      itensPag: itensPag ?? this.itensPag,
    );
  }
}

class HotSiteSegmentacaoModel {
  final bool? recrutamentoInterno;
  final bool? divulgacaoPrivada;
  final bool? query;

  HotSiteSegmentacaoModel({
    this.recrutamentoInterno,
    this.divulgacaoPrivada,
    this.query,
  });

  Map<String, dynamic> toJson() {
    return {
      'recrutamentoInterno': recrutamentoInterno,
      'divulgacaoPrivada': divulgacaoPrivada,
      'query': query,
    };
  }
}
