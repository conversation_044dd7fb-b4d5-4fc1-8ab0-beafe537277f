class ChatResponseModel {
  final bool sucesso;
  final String mensagem;
  final List<ChatModel>? chats;

  ChatResponseModel({
    this.sucesso = false,
    this.mensagem = "",
    this.chats,
  });

  factory ChatResponseModel.fromJson(Map<String, dynamic> json) {
    return ChatResponseModel(
      sucesso: json['sucesso'] ?? false,
      mensagem: json['mensagem'] ?? "",
      chats: (json['chats'] as List<dynamic>?)
          ?.map((e) => ChatModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class ChatModel {
  final String? id;
  final String? vagaID;
  final String? pessoaID;
  final String? vaga;
  final String? mensagem;
  final String? data;
  final String? nome;
  final String? foto;
  final bool? naoLida;
  final bool? encerrado;

  ChatModel({
    this.id,
    this.vagaID,
    this.pessoaID,
    this.vaga,
    this.mensagem,
    this.data,
    this.nome,
    this.foto,
    this.naoLida,
    this.encerrado,
  });

  factory ChatModel.fromJson(Map<String, dynamic> json) {
    return ChatModel(
      id: json['id'] as String?,
      vagaID: json['vagaID'] as String?,
      pessoaID: json['pessoaID'] as String?,
      vaga: json['vaga'] as String?,
      mensagem: json['mensagem'] as String?,
      data: json['data'] as String?,
      nome: json['nome'] as String?,
      foto: json['foto'] as String?,
      naoLida: json['naoLida'] as bool?,
      encerrado: json['encerrado'] as bool?,
    );
  }

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'id': id,
      'vagaID': vagaID,
      'pessoaID': pessoaID,
      'vaga': vaga,
      'mensagem': mensagem,
      'data': data,
      'nome': nome,
      'foto': foto,
      'naoLida': naoLida,
      'encerrado': encerrado,
    };
  }
}
