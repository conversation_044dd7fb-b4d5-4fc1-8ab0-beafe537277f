import 'package:bottom_sheet/bottom_sheet.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:share_plus/share_plus.dart';

import '../../shared/core/app_config.dart';
import '../../shared/widgets/app_default_button.dart';
import '../../shared/widgets/app_tab_item.dart';
import '../../shared/widgets/app_usuario_card/app_usuario_card_controller.dart';
import '../jobs/job_detail/job_detail_controller.dart';
import '../jobs/job_detail/widget/app_body_builder_job_detaisl.dart';
import '../jobs/job_detail/widget/app_header_builder_job_details.dart';
import 'home_controller.dart';
import 'home_module.dart';
import 'tab_subscriptions/tab_subscriptions_controller.dart';
import 'utils/subscription_button.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  _HomePageState createState() => _HomePageState();
}

class _HomePageState extends State<HomePage>
    with SingleTickerProviderStateMixin {
  final controller = Modular.get<HomeController>();
  final appUsuarioController = Modular.get<AppUsuarioCardController>();
  final controllerJob = Modular.get<JobDetailController>();
  final tabSubscriptionController = Modular.get<TabSubscriptionsController>();

  @override
  void initState() {
    super.initState();
    Modular.to.navigate(HomeModule.tabNotifications, arguments: goToJobs);
  }

  goToJobs() {
    controller.setTabSelected(1);
    Modular.to.navigate(HomeModule.tabJobs);
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (bool? value, n) async {
        if (controller.tabSelected == 0) {
          value = true;
          return;
        }
        controller.setTabSelected(0);
        Modular.to.navigate(HomeModule.tabNotifications);
        value = false;
        return;
      },
      child: ColoredBox(
        color: Colors.white,
        child: SafeArea(
          top: false,
          child: Scaffold(
            body: const AnnotatedRegion<SystemUiOverlayStyle>(
              value: SystemUiOverlayStyle.light,
              child: RouterOutlet(),
            ),
            bottomNavigationBar: Observer(
              builder: (_) {
                if (controllerJob.isSubscriptionButton ||
                    tabSubscriptionController.subscription != null) {
                  return Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(bottom: 8),
                          child: Text(controllerJob.inscricaoMensagem ?? ""),
                        ),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Expanded(
                              flex: 3,
                              child: AppDefaultButton(
                                withBorder: true,
                                onPressed: controllerJob.isSubscriptionButton ==
                                        false
                                    ? null
                                    : () async {
                                        await controller.setCloseScreen(true);

                                        var response =
                                            await controllerJob.cancelar();

                                        // ignore: use_build_context_synchronously
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          SnackBar(
                                            backgroundColor: response.sucesso
                                                ? Colors.green
                                                : Colors.redAccent,
                                            content: Text(response.mensagem),
                                          ),
                                        );

                                        if (controller.closeScreen == true &&
                                            response.sucesso) {
                                          controllerJob.getJobDetails;
                                        }
                                      },
                                title: const Text("Cancelar inscrição"),
                              ),
                            ),
                            if (tabSubscriptionController.hasVagaId())
                              Expanded(
                                flex: 2,
                                child: Padding(
                                  padding: const EdgeInsets.only(left: 5),
                                  child: AppDefaultButton(
                                    onPressed: () async {
                                      await showStickyFlexibleBottomSheet(
                                        minHeight: .82,
                                        initHeight: .82,
                                        maxHeight: .92,
                                        maxHeaderHeight: 118,
                                        minHeaderHeight: 100,
                                        isSafeArea: true,
                                        bottomSheetBorderRadius:
                                            const BorderRadius.only(
                                          topLeft: Radius.circular(25),
                                          topRight: Radius.circular(25),
                                        ),
                                        anchors: [0.8],
                                        decoration: const BoxDecoration(
                                          color: Colors.white,
                                        ),
                                        context: context,
                                        isDismissible: true,
                                        duration:
                                            const Duration(milliseconds: 500),
                                        headerBuilder: (context, offset) {
                                          return Observer(
                                            builder: (_) =>
                                                AppHeaderBuildeJobDetails(
                                              image: tabSubscriptionController
                                                      .job?.logo?.thumb ??
                                                  '',
                                              title: tabSubscriptionController
                                                      .job?.titulo ??
                                                  '',
                                              company: tabSubscriptionController
                                                      .job?.empresa ??
                                                  '',
                                              date: tabSubscriptionController
                                                      .job?.data ??
                                                  '',
                                              offset: offset,
                                            ),
                                          );
                                        },
                                        bodyBuilder:
                                            (context, bottomSheetOffset) {
                                          return SliverChildListDelegate(
                                            [
                                              Padding(
                                                padding: const EdgeInsets.only(
                                                  left: 24,
                                                  right: 24,
                                                ),
                                                child: AppBodyBuilderJobDetails(
                                                  jobId:
                                                      tabSubscriptionController
                                                          .getVagaId(),
                                                ),
                                              )
                                            ],
                                          );
                                        },
                                      );
                                    },
                                    title: const Text("Visualizar vaga"),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ],
                    ),
                  );
                }

                if (controllerJob.isPageJob) {
                  String formatHtml(String text) {
                    final replacements = {
                      '<ul>':
                          '\n', // Adiciona quebra de linha para início de listas
                      '</ul>': '', // Remove o fechamento da lista
                      '<li>': '\n- ', // Substitui <li> por marcadores de lista
                      '</li>': '', // Remove </li>
                      '<p>': '\n', // Adiciona quebra de linha para parágrafos
                      '</p>': '', // Remove fechamento de parágrafo
                      '<br>': '\n', // Substitui <br> por quebra de linha
                    };

                    replacements.forEach((tag, replacement) {
                      text = text.replaceAll(tag, replacement);
                    });

                    text = text.replaceAll(RegExp(r'<[^>]+>'), '');

                    text = text.replaceAll(RegExp(r'\n{2,}'), '\n\n');

                    return text.trim();
                  }

                  String plainText =
                      formatHtml(controllerJob.jobDetails?.descricao ?? '');

                  return Container(
                    height: 73,
                    width: MediaQuery.of(context).size.width,
                    color: const Color(0xfff5f5f5),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        SubscriptionButton(controllerJob: controllerJob),
                        Padding(
                          padding: const EdgeInsets.only(left: 25.0),
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(6.0),
                              ),
                              shadowColor: Colors.transparent,
                              elevation: 0,
                              backgroundColor: const Color(0xff9e9eef),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 20,
                                vertical: 17,
                              ),
                            ),
                            child: Image.asset(
                              'lib/assets/images/share.png',
                              scale: 2,
                            ),
                            onPressed: () async {
                              await Share.share(
                                "${controllerJob.jobDetails?.titulo ?? ''}\n"
                                "$plainText\n"
                                "${AppConfig.siteUrl + (controllerJob.jobDetails?.url ?? '')}",
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return FittedBox(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withValues(alpha: 0.1),
                          spreadRadius: 5,
                          blurRadius: 10,
                          offset: const Offset(
                            0,
                            -15,
                          ),
                        ),
                      ],
                    ),
                    height: 105,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        TabItem(
                          isProfile: false,
                          isSelected: controller.tabSelected == 0,
                          imageNoActive:
                              'lib/assets/icons/notification-disable.png',
                          imageActive:
                              'lib/assets/icons/notification-active.png',
                          title: 'Notificações',
                          index: 0,
                          routeName: HomeModule.tabNotifications,
                          onPressed: _onPressedTabItem,
                          appUsuarioController: appUsuarioController,
                        ),
                        TabItem(
                          isProfile: false,
                          isSelected: controller.tabSelected == 1,
                          imageNoActive:
                              'lib/assets/icons/briefcase-disable.png',
                          imageActive: 'lib/assets/icons/briefcase-active.png',
                          title: 'Vagas',
                          index: 1,
                          routeName: HomeModule.tabJobs,
                          onPressed: _onPressedTabItem,
                          appUsuarioController: appUsuarioController,
                        ),
                        TabItem(
                          isProfile: false,
                          isSelected: controller.tabSelected == 2,
                          imageNoActive: 'lib/assets/icons/message-disable.png',
                          imageActive: 'lib/assets/icons/message-active.png',
                          title: 'Mensagens',
                          index: 2,
                          routeName: HomeModule.tabMessages,
                          onPressed: _onPressedTabItem,
                          appUsuarioController: appUsuarioController,
                        ),
                        TabItem(
                          isProfile: false,
                          isSelected: controller.tabSelected == 3,
                          imageNoActive:
                              'lib/assets/icons/archive-book-disable.png',
                          imageActive:
                              'lib/assets/icons/archive-book-active.png',
                          title: 'Candidaturas',
                          index: 3,
                          routeName: HomeModule.tabSubscriptions,
                          onPressed: _onPressedTabItem,
                          appUsuarioController: appUsuarioController,
                        ),
                        TabItem(
                          isProfile: true,
                          isSelected: controller.tabSelected == 4,
                          title: 'Perfil',
                          index: 4,
                          imageActive: appUsuarioController.foto,
                          imageNoActive:
                              'lib/assets/icons/icon-menu-disable.png',
                          onPressed: _onPressedTabItem,
                          routeName: HomeModule.tabProfile,
                          appUsuarioController: appUsuarioController,
                          paddingHeight: 2,
                        )
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  void _onPressedTabItem(String? routeName, int? index) {
    if (routeName != null && index != null) {
      Modular.to.navigate(routeName);
      controller.setTabSelected(index);
      setState(() => {});
    }
  }
}
