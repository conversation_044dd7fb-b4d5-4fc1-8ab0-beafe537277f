import 'package:flutter/material.dart';

class HeaderRecoveryPassword extends StatelessWidget {
  const HeaderRecoveryPassword({
    super.key,
    this.title,
    this.subTitle,
    this.bottomBack,
    this.bottom,
    this.iconInnerTitle,
    this.onTap,
  });

  final String? title;
  final String? subTitle;
  final bool? bottomBack;
  final double? bottom;
  final Widget? iconInnerTitle;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(
            top: 30.0,
            left: 14.0,
          ),
          child: Visibility(
            visible: bottomBack ?? false,
            replacement: const SizedBox(
              height: 50,
              width: 50,
            ),
            child: InkWell(
              onTap: () {
                onTap ?? Navigator.of(context).pop();
              },
              borderRadius: BorderRadius.circular(20),
              child: SizedBox(
                height: 50,
                width: 50,
                child: Material(
                  shape: const CircleBorder(),
                  color: Colors.transparent,
                  child: InkWell(
                    customBorder: const CircleBorder(),
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    child: const Icon(
                      Icons.arrow_back_ios_new,
                      color: Colors.black,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
        Padding(
          padding: EdgeInsets.only(
            top: 32.0,
            left: 28.0,
            right: 121.0,
            bottom: bottom ?? 77.0,
          ),
          child: Image.asset(
            'lib/assets/images/logo-empregare-splash.png',
            fit: BoxFit.cover,
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(
            left: 24.0,
            right: 20.0,
            bottom: 32.0,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  iconInnerTitle ?? const SizedBox.shrink(),
                  Text(
                    title ?? '',
                    style: const TextStyle(
                      fontSize: 24,
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w600,
                      color: Color(0xff161519),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                subTitle ?? '',
                style: const TextStyle(
                  fontSize: 13.2,
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w400,
                  color: Color(0xff161519),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
