import 'package:flutter/material.dart';

import '../../../shared/core/app_config.dart';

class CurriculumDefaultField extends StatelessWidget {
  final String? description;
  final String? textBottom;
  final Function? bottomAdd;
  final bool? noContent;
  final Function? noContentButtom;
  final String? noContentText;

  const CurriculumDefaultField({
    this.bottomAdd,
    this.textBottom,
    this.description,
    this.noContent = false,
    this.noContentButtom,
    this.noContentText,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: Colors.white,
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
            child: Text(
              description ?? '',
              style: const TextStyle(
                color: Color(0xFF666666),
              ),
              textAlign: TextAlign.justify,
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              left: 24,
              right: 24,
              bottom: 24,
            ),
            child: Column(
              children: [
                InkWell(
                  onTap: bottomAdd as void Function()?,
                  child: DecoratedBox(
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: AppConfig.colorPrimary,
                      ),
                      borderRadius: const BorderRadius.all(
                        Radius.circular(6),
                      ),
                      color: AppConfig.colorPrimary,
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(12),
                      child: Center(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(
                              Icons.add,
                              color: Colors.white,
                              size: 18,
                            ),
                            const SizedBox(width: 5),
                            Text(
                              textBottom ?? '',
                              style: const TextStyle(
                                color: AppConfig.white,
                                fontFamily: 'Inter',
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                if (noContent != false)
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 10.0),
                    child: InkWell(
                      onTap: noContentButtom as void Function()?,
                      child: DecoratedBox(
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: AppConfig.colorPrimary,
                          ),
                          borderRadius: const BorderRadius.all(
                            Radius.circular(6),
                          ),
                          color: AppConfig.white,
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(12),
                          child: Center(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const SizedBox(width: 5),
                                Text(
                                  noContentText ?? '',
                                  style: const TextStyle(
                                    color: Colors.black,
                                    fontFamily: 'Inter',
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
