import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../shared/core/app_config.dart';
import '../../shared/widgets/app_default_button.dart';
import '../../shared/widgets/app_text_form_field.dart';
import 'forgot_password_controller.dart';
import 'utils/cpf_or_email_formatter.dart';
import 'utils/header_recovery_password.dart';
import 'utils/widget_custom_toaster.dart';
import 'utils/widget_select_method_of_recovery.dart';

class ForgotPasswordPage extends StatefulWidget {
  const ForgotPasswordPage({
    super.key,
  });

  @override
  _ForgotPasswordPageState createState() => _ForgotPasswordPageState();
}

class _ForgotPasswordPageState extends State<ForgotPasswordPage> {
  final controller = Modular.get<ForgotPasswordController>();

  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();

    controller.reset();
  }

  @override
  void dispose() {
    super.dispose();

    _formKey.currentState?.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const HeaderRecoveryPassword(
                bottomBack: true,
                subTitle: 'Informe seus dados e siga as orientações da tela',
                title: 'Esqueci minha senha',
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('E-mail ou CPF'),
                    const SizedBox(height: 8),
                    AppTextFormField(
                      hintText: 'Informe seu e-mail ou CPF',
                      hintStyleColor: const Color(0xff60606b),
                      onChanged: controller.setCpf,
                      keyboardType: TextInputType.emailAddress,
                      textInputAction: TextInputAction.done,
                      enabled: true,
                      isPassword: false,
                      radius: 6,
                      autofocus: true,
                      optional: false,
                      inputFormatters: [CpfOrEmailFormatter()],
                    )
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24.0,
                  vertical: 24,
                ),
                child: AppDefaultButton(
                  color: AppConfig.colorPrimary,
                  title: const Text(
                    'Recuperar senha',
                    style: TextStyle(
                      color: AppConfig.white,
                    ),
                  ),
                  onPressed: validateAndProcessLogin,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> validateAndProcessLogin() async {
    if (_formKey.currentState?.validate() != true) return;

    var response = await controller.postVerificarSenha();

    if (!response.sucesso) {
      WidgetCustomToaster(
        context: context,
        backgroundColor: const Color(0xFFF44336),
        message: response.mensagem,
        borderRadius: 6,
        duration: const Duration(seconds: 6),
      ).show();

      return;
    }

    Modular.to.push(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) {
          return const WidgetSelectMethodOfRecovery();
        },
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.easeInOut;

          var tween = Tween(begin: begin, end: end).chain(
            CurveTween(curve: curve),
          );

          return FadeTransition(
            opacity: animation,
            child: SlideTransition(
              position: animation.drive(tween),
              child: child,
            ),
          );
        },
      ),
    );
  }
}
