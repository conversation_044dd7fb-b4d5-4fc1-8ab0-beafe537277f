import '../../controllers/computing_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../../shared/core/app_translation.i18n.dart';
import '../../../../shared/helpers/snack_bar_helper.dart';
import '../../../../shared/models/responses/listar_informatica_response_model.dart';
import '../../../../shared/widgets/app_badge.dart';
import '../../curriculum_editing/curriculum_editing_module.dart';
import '../../widgets/curriculum_default_field.dart';
import '../secao/secao_widget.dart';

class InformaticaWidget extends StatefulWidget {
  const InformaticaWidget({super.key});

  @override
  _InformaticaWidgetState createState() => _InformaticaWidgetState();
}

class _InformaticaWidgetState extends State<InformaticaWidget> {
  final ComputingController controller = Modular.get();

  // @override
  // void initState() {
  //   super.initState();
  //   controller.load(); // Carrega dados de informática ao abrir a seção
  // }
  Widget getBadgesByNivel(int nivel, String label) {
    if (controller.informatica?.any((i) => i.nivel == nivel) == true) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 12,
            ),
          ),
          SizedBox(
            height: 35,
            child: ListView(
              scrollDirection: Axis.horizontal,
              children: controller.informatica!
                  .where((i) => i.nivel == nivel)
                  .map((i) {
                return AppBadge(title: i.nome);
              }).toList(),
            ),
          ),
          const Divider(),
        ],
      );
    }
    return Container();
  }

  void onAddOrEdit() async {
    controller.informaticaBasico = '';
    controller.informaticaIntermediario = '';
    controller.informaticaAvancado = '';
    controller.informaticaToSave = controller.informatica?.toList() ?? [];
    var result = await Modular.to.pushNamed(CurriculumEditingModule.route,
        arguments: CurriculumEditingModule.informatica);
    if (result == true) {
      SnackbarHelper.showSnackbarSucesso(
        context,
        'Habilidades em informática salvas com sucesso',
      );
      controller.loadInformatica();
    }
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ComputingController>(
      init: controller,
      builder: (_) => SecaoWidget(
        header: 'Informática'.i18n,
        showEdit: true,
        replaceEditWithAdd:
            (controller.informatica?.isNotEmpty ?? false) ? true : false,
        hasContent: controller.informatica?.isNotEmpty ?? false,
        onEdit: onAddOrEdit,
        content: ColoredBox(
          color: Colors.white,
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 24.0,
              vertical: 15.0,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                getBadgesByNivel(InformaticaModel.avancado, 'Avaçado'.i18n),
                getBadgesByNivel(
                    InformaticaModel.intermediario, 'Intermediário'.i18n),
                getBadgesByNivel(InformaticaModel.basico, 'Básico'.i18n),
              ],
            ),
          ),
        ),
        noContent: CurriculumDefaultField(
          bottomAdd: onAddOrEdit,
          textBottom: 'Adicionar informática',
          description:
              'Inclua suas habilidades em informática nos seus respectivos níveis.',
        ),
      ),
    );
  }
}
