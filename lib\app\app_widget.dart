import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:i18n_extension/i18n_extension.dart';

import 'shared/core/app_config.dart';

class AppWidget extends StatelessWidget {
  const AppWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return I18n(
      child: MaterialApp.router(
        title: 'Empregare',
        theme: ThemeData(
          useMaterial3: false,
          fontFamily: 'Inter',
          appBarTheme: const AppBarTheme(
            elevation: 0.5,
            backgroundColor: AppConfig.white,
            foregroundColor: Colors.black,
            iconTheme: IconThemeData(color: AppConfig.colorPrimary),
            actionsIconTheme: IconThemeData(color: AppConfig.colorPrimary),
          ),
          primaryColor: AppConfig.colorPrimary,
          colorScheme: ColorScheme.fromSwatch().copyWith(
            primary: AppConfig.colorPrimary,
          ),
          elevatedButtonTheme: ElevatedButtonThemeData(
            style: ElevatedButton.styleFrom(
              foregroundColor: Colors.black,
            ),
          ),
        ),
        themeMode: ThemeMode.light,
        debugShowCheckedModeBanner: false,
        locale: const Locale('pt', 'BR'),
        supportedLocales: const [
          Locale('pt', 'BR'),
          Locale('pt', 'PT'),
          Locale('en', 'US'),
          Locale('es', 'ES'),
        ],
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        routerConfig: Modular.routerConfig,
      ),
    );
  }
}
