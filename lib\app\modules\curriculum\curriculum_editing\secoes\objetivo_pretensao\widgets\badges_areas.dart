import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../../../../../shared/widgets/app_badge.dart';
import '../../../../../curriculum/curriculum_controller.dart';

class BadgesAreas extends StatefulWidget {
  const BadgesAreas({
    super.key,
    required this.areasInteresseToSaveLength,
  });

  final int areasInteresseToSaveLength;

  @override
  State<BadgesAreas> createState() => _BadgesAreasState();
}

class _BadgesAreasState extends State<BadgesAreas> {
  final CurriculumController _curriculumController = Modular.get();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          'Áreas de interesse'.i18n,
          style: const TextStyle(
            fontSize: 14,
          ),
        ),
        Visibility(
          visible: widget.areasInteresseToSaveLength > 0,
          child: Container(
            height: widget.areasInteresseToSaveLength == 0 ? 35 : null,
            width: double.maxFinite,
            color: Colors.white,
            child: Wrap(
              children: _curriculumController.objective.areasInteresseToSave.map(
                (a) {
                  return SizedBox(
                    height: 40,
                    child: AppBadge(
                      title: a.nome,
                      onRemove: () {
                        _curriculumController.objective.removeAreasInteresseToSave(a);
                      },
                    ),
                  );
                },
              ).toList(),
            ),
          ),
        ),
        widget.areasInteresseToSaveLength >= 3
            ? const Divider(height: 1, color: Colors.black)
            : const SizedBox.shrink(),
      ],
    );
  }
}
