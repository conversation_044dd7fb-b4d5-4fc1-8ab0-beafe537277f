import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../../shared/core/app_config.dart';
import '../../jobs/job_detail/job_detail_controller.dart';

class SubscriptionButton extends StatelessWidget {
  const SubscriptionButton({super.key, required this.controllerJob});

  final JobDetailController controllerJob;

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;

    final double progressIndicatorSize = screenWidth * 0.06;

    return Observer(
      builder: (_) => ElevatedButton(
        style: ElevatedButton.styleFrom(
          fixedSize: const Size(250, 50),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6.0),
            side: const BorderSide(color: AppConfig.colorPrimary),
          ),
          shadowColor: Colors.transparent,
          elevation: 0,
          backgroundColor: !controllerJob.isSubscriptionButton
              ? AppConfig.colorPrimary
              : AppConfig.white,
          padding: const EdgeInsets.symmetric(
            horizontal: 50,
            vertical: 15,
          ),
        ),
        onPressed: controllerJob.subscription?.retorno == "CurriculoIncompleto"
            ? null
            : () async {
                if ((controllerJob.isSuccess ?? true) == false ||
                    controllerJob.loading) {
                  return;
                }

                final scaffoldMessenger = ScaffoldMessenger.of(context);
                bool success = false;
                String message = "";

                if (controllerJob.isSubscriptionButton == false) {
                  var response = await controllerJob.confirmSubscription();

                  success = response.sucesso;
                  message = response.mensagem;

                  if (message.isEmpty) {
                    message = response.retornoMensagem ?? "";
                  }
                } else {
                  var response = await controllerJob.cancelar();

                  success = response.sucesso;
                  message = response.mensagem;
                }

                // ignore: use_build_context_synchronously
                scaffoldMessenger.showSnackBar(
                  SnackBar(
                    backgroundColor: success ? Colors.green : Colors.redAccent,
                    content: Text(message),
                  ),
                );
              },
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.max,
          children: [
            Visibility(
              visible: controllerJob.loading,
              replacement: Text(
                !controllerJob.isSubscriptionButton
                    ? 'Candidatar-se'.i18n
                    : 'Cancelar inscrição'.i18n,
                style: TextStyle(
                  color: !controllerJob.isSubscriptionButton
                      ? AppConfig.white
                      : AppConfig.colorPrimary,
                ),
              ),
              child: SizedBox(
                width: progressIndicatorSize,
                height: progressIndicatorSize,
                child: const CircularProgressIndicator(
                  color: AppConfig.colorPrimary,
                  backgroundColor: AppConfig.grey,
                  strokeWidth: 5,
                  strokeAlign: 1,
                  strokeCap: StrokeCap.round,
                ),
              ),
            ),
            const SizedBox(width: 10),
            Visibility(
              visible:
                  controllerJob.isSubscriptionButton || controllerJob.loading,
              replacement: Image.asset(
                'lib/assets/images/candidatar.png',
                scale: 2,
              ),
              child: const SizedBox.shrink(),
            ),
          ],
        ),
      ),
    );
  }
}
