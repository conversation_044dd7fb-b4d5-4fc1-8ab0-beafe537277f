import 'package:flutter/material.dart';

import '../app_container.dart';
import '../core/app_translation.i18n.dart';
import '../models/responses/job_details_response_model.dart';
import 'app_info.dart';

class AppJobHeader extends StatelessWidget {
  final JobDetailsResponseModel? jobDetails;

  const AppJobHeader({super.key, this.jobDetails});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: <Widget>[
        Column(
          children: <Widget>[
            const SizedBox(height: 70),
            AppContainer(
              width: double.infinity,
              padding: const EdgeInsets.only(
                left: 15,
                right: 15,
                top: 40,
                bottom: 20,
              ),
              child: Column(
                children: <Widget>[
                  const SizedBox(height: 15),
                  Text(
                    jobDetails?.titulo ?? '',
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 6),
                  Text(
                    jobDetails?.empresa ?? '',
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 15),
                  Row(
                    children: <Widget>[
                      if (jobDetails?.salario != null &&
                          jobDetails!.salario!.isNotEmpty)
                        Expanded(
                          flex: 2,
                          child: AppInfo(
                            icon: const Icon(
                              Icons.attach_money,
                              color: Colors.grey,
                            ),
                            title: Text(
                              'SALÁRIO'.i18n,
                              style: const TextStyle(
                                fontSize: 11,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            subtitle: Text(
                              jobDetails!.salario!,
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                      Expanded(
                        flex: 1,
                        child: AppInfo(
                          icon: const Icon(Icons.work, color: Colors.grey),
                          title: Text(
                            'MODALIDADE'.i18n,
                            style: const TextStyle(
                              fontSize: 11,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          subtitle: Text(
                            jobDetails?.modalidadeTitulo ?? '',
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                ],
              ),
            )
          ],
        ),
        Positioned(
          left: 0,
          right: 0,
          top: 15,
          child: Center(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(5),
                boxShadow: const [
                  BoxShadow(
                    color: Colors.grey,
                    offset: Offset(0.0, 0.0),
                    blurRadius: 4.0,
                  ),
                ],
              ),
              padding: const EdgeInsets.all(7),
              child: Container(
                height: 82,
                width: 82,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(5),
                  image: DecorationImage(
                    fit: BoxFit.fitWidth,
                    image: NetworkImage(jobDetails?.logo ?? ''),
                  ),
                ),
              ),
            ),
          ),
        )
      ],
    );
  }
}
