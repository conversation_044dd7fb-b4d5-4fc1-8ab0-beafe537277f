import 'package:flutter_modular/flutter_modular.dart';

import 'schedule_controller.dart';
import 'schedule_page.dart';

class ScheduleModule extends Module {
  static const route = '/schedule';

  @override
  void binds(i) {
    i.addLazySingleton(ScheduleController.new);
  }

  @override
  void routes(r) {
    r.child(
      Modular.initialRoute,
      child: (context) => SchedulePage(token: r.args.data),
    );
  }
}
