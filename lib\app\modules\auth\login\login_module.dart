import 'package:flutter_modular/flutter_modular.dart';
import 'package:get/get.dart';

import '../../../shared/repositories/forgot_password_repository.dart';
import '../../forgot_password/forgot_password_controller.dart';
import '../signup/signup_controller.dart';
import 'login_controller.dart';
import 'login_page.dart';

class LoginModule extends Module {
  static const routeName = '/login';

  @override
  void binds(i) {
    Get.lazyPut(() => LoginController());


    i.addLazySingleton(ForgotPasswordController.new);
    i.addLazySingleton(ForgotPasswordRepository.new);
    i.addLazySingleton(SignupController.new);
  }

  @override
  void routes(r) {
    r.child('/', child: (context) => const LoginPage());
  }
}
