import 'package:flutter/material.dart';

import '../../../../shared/core/app_config.dart';
import '../../../../shared/core/app_translation.i18n.dart';
import '../../../../shared/widgets/app_html.dart';
import '../job_detail_controller.dart';

class Requirements extends StatelessWidget {
  const Requirements({
    super.key,
    required this.controller,
  });

  final JobDetailController controller;

  @override
  Widget build(BuildContext context) {
    RegExp exp = RegExp(r'<[^>]*>$');
    String texto = controller.jobDetails?.requisito!.replaceAll(exp, '') ?? '';

    exp = RegExp(r'<[^>]*>$');
    while (exp.hasMatch(texto)) {
      texto = texto.replaceFirst(exp, '');
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          'Requisitos'.i18n,
          style: const TextStyle(
            fontSize: 14,
            color: AppConfig.colorPrimary,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 20),
        AppHtml(data: texto),
      ],
    );
  }
}
