import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../../../../shared/core/app_config.dart';
import '../../../../../shared/models/responses/listar_complementar_response_model.dart';
import '../../../../../shared/widgets/app_dropdown_search.dart';
import '../../../controllers/complementary_controller.dart';
import '../../curriculum_editing_controller.dart';
import '../../widgets/secao.dart';

class InformacoesComplementares extends StatefulWidget {
  const InformacoesComplementares({super.key});

  @override
  State<InformacoesComplementares> createState() =>
      _InformacoesComplementaresState();
}

class _InformacoesComplementaresState extends State<InformacoesComplementares> {
  final ComplementaryController _curriculumController = Modular.get();
  final controller = Modular.get<CurriculumEditingController>();

  @override
  Widget build(BuildContext context) {
    return Secao(
      fields: Observer(builder: (_) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(
                  top: _curriculumController.complementarToSave?.filhos != null
                      ? 8
                      : 0),
              child: DropdownSearch(
                context,
                hintText: 'POSSUI FILHOS?'.i18n,
                title: _curriculumController.complementarToSave?.filhos != null
                    ? 'POSSUI FILHOS?'.i18n
                    : '',
                items: _curriculumController.filhos
                    .map((e) => DropdownSearchItem(
                          value: e.id,
                          searchKey: e.nome!,
                          text: e.nome,
                          child: Text(e.nome!),
                        ))
                    .toList(),
                onSelected: (dynamic item) {
                  if (item != null) {
                    _curriculumController.complementarToSave!.filhos = item;
                  }
                },
                value: _curriculumController.complementarToSave?.filhos,
              ),
            ),
            const SizedBox(height: 15),
            Padding(
              padding: const EdgeInsets.only(bottom: 5),
              child: Text(
                'CARTEIRA DE HABILITAÇÃO'.i18n,
                style: const TextStyle(fontSize: 14),
              ),
            ),
            Wrap(
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Checkbox(
                      activeColor: AppConfig.colorPrimary,
                      value: _curriculumController
                              .complementarToSave?.curriculoAdicional
                              ?.any((v) => v == ComplementarModel.habA) ??
                          false,
                      onChanged: (value) {
                        setState(() {
                          if (value!) {
                            _curriculumController
                                .complementarToSave?.curriculoAdicional
                                ?.add(ComplementarModel.habA);
                          } else {
                            _curriculumController
                                .complementarToSave?.curriculoAdicional
                                ?.remove(ComplementarModel.habA);
                          }
                        });
                      },
                    ),
                    InkWell(
                      child: const Text(
                        'A',
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 14,
                        ),
                      ),
                      onTap: () {
                        if (_curriculumController
                                .complementarToSave?.curriculoAdicional
                                ?.any((v) => v == ComplementarModel.habA) ??
                            false) {
                          _curriculumController
                              .complementarToSave?.curriculoAdicional
                              ?.remove(ComplementarModel.habA);
                        } else {
                          _curriculumController
                              .complementarToSave?.curriculoAdicional
                              ?.add(ComplementarModel.habA);
                        }
                        setState(() {});
                      },
                    ),
                  ],
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Checkbox(
                      activeColor: AppConfig.colorPrimary,
                      value: _curriculumController
                              .complementarToSave?.curriculoAdicional
                              ?.any((v) => v == ComplementarModel.habB) ??
                          false,
                      onChanged: (value) {
                        setState(() {
                          if (value!) {
                            _curriculumController
                                .complementarToSave?.curriculoAdicional
                                ?.add(ComplementarModel.habB);
                          } else {
                            _curriculumController
                                .complementarToSave?.curriculoAdicional
                                ?.remove(ComplementarModel.habB);
                          }
                        });
                      },
                    ),
                    InkWell(
                      child: const Text(
                        'B',
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 14,
                        ),
                      ),
                      onTap: () {
                        if (_curriculumController
                                .complementarToSave?.curriculoAdicional
                                ?.any((v) => v == ComplementarModel.habB) ??
                            false) {
                          _curriculumController
                              .complementarToSave?.curriculoAdicional
                              ?.remove(ComplementarModel.habB);
                        } else {
                          _curriculumController
                              .complementarToSave?.curriculoAdicional
                              ?.add(ComplementarModel.habB);
                        }
                        setState(() {});
                      },
                    ),
                  ],
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Checkbox(
                      activeColor: AppConfig.colorPrimary,
                      value: _curriculumController
                              .complementarToSave?.curriculoAdicional
                              ?.any((v) => v == ComplementarModel.habC) ??
                          false,
                      onChanged: (value) {
                        setState(() {
                          if (value!) {
                            _curriculumController
                                .complementarToSave?.curriculoAdicional
                                ?.add(ComplementarModel.habC);
                          } else {
                            _curriculumController
                                .complementarToSave?.curriculoAdicional
                                ?.remove(ComplementarModel.habC);
                          }
                        });
                      },
                    ),
                    InkWell(
                      child: const Text(
                        'C',
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 14,
                        ),
                      ),
                      onTap: () {
                        if (_curriculumController
                                .complementarToSave?.curriculoAdicional
                                ?.any((v) => v == ComplementarModel.habC) ??
                            false) {
                          _curriculumController
                              .complementarToSave?.curriculoAdicional
                              ?.remove(ComplementarModel.habC);
                        } else {
                          _curriculumController
                              .complementarToSave?.curriculoAdicional
                              ?.add(ComplementarModel.habC);
                        }
                        setState(() {});
                      },
                    ),
                  ],
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Checkbox(
                      activeColor: AppConfig.colorPrimary,
                      value: _curriculumController
                              .complementarToSave?.curriculoAdicional
                              ?.any((v) => v == ComplementarModel.habD) ??
                          false,
                      onChanged: (value) {
                        setState(() {
                          if (value!) {
                            _curriculumController
                                .complementarToSave?.curriculoAdicional
                                ?.add(ComplementarModel.habD);
                          } else {
                            _curriculumController
                                .complementarToSave?.curriculoAdicional
                                ?.remove(ComplementarModel.habD);
                          }
                        });
                      },
                    ),
                    InkWell(
                      child: const Text(
                        'D',
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 14,
                        ),
                      ),
                      onTap: () {
                        if (_curriculumController
                                .complementarToSave?.curriculoAdicional
                                ?.any((v) => v == ComplementarModel.habD) ??
                            false) {
                          _curriculumController
                              .complementarToSave?.curriculoAdicional
                              ?.remove(ComplementarModel.habD);
                        } else {
                          _curriculumController
                              .complementarToSave?.curriculoAdicional
                              ?.add(ComplementarModel.habD);
                        }
                        setState(() {});
                      },
                    ),
                  ],
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Checkbox(
                      activeColor: AppConfig.colorPrimary,
                      value: _curriculumController
                              .complementarToSave?.curriculoAdicional
                              ?.any((v) => v == ComplementarModel.habE) ??
                          false,
                      onChanged: (value) {
                        setState(() {
                          if (value!) {
                            _curriculumController
                                .complementarToSave?.curriculoAdicional
                                ?.add(ComplementarModel.habE);
                          } else {
                            _curriculumController
                                .complementarToSave?.curriculoAdicional
                                ?.remove(ComplementarModel.habE);
                          }
                        });
                      },
                    ),
                    InkWell(
                      child: const Text(
                        'E',
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 14,
                        ),
                      ),
                      onTap: () {
                        if (_curriculumController
                                .complementarToSave?.curriculoAdicional
                                ?.any((v) => v == ComplementarModel.habE) ??
                            false) {
                          _curriculumController
                              .complementarToSave?.curriculoAdicional
                              ?.remove(ComplementarModel.habE);
                        } else {
                          _curriculumController
                              .complementarToSave?.curriculoAdicional
                              ?.add(ComplementarModel.habE);
                        }
                        setState(() {});
                      },
                    ),
                  ],
                ),
              ],
            ),
          ],
        );
      }),
    );
  }
}
