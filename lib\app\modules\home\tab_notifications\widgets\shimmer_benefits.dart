import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class ShimmerBenefits extends StatelessWidget {
  const ShimmerBenefits({
    super.key,
    this.height,
    this.width,
  });

  final double? height;
  final double? width;

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          Container(
            height: height ?? 150,
            width: width ?? 150,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6),
              color: Colors.white,
            ),
          ),
          const SizedBox(
            width: 10,
          ),
          Container(
            height: height ?? 150,
            width: width ?? 150,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6),
              color: Colors.white,
            ),
          ),
          const SizedBox(
            width: 10,
          ),
          Container(
            height: height ?? 150,
            width: width ?? 150,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6),
              color: Colors.white,
            ),
          ),
          const SizedBox(
            width: 10,
          ),
          Container(
            height: height ?? 150,
            width: width ?? 150,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6),
              color: Colors.white,
            ),
          ),
          const SizedBox(
            width: 10,
          ),
          Container(
            height: height ?? 150,
            width: width ?? 150,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6),
              color: Colors.white,
            ),
          ),
          const SizedBox(
            width: 10,
          ),
          Container(
            height: height ?? 150,
            width: width ?? 150,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6),
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}
