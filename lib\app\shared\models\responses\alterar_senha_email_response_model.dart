class AlterarSenhaEmailResponseModel {
  final bool sucesso;
  final String? email;
  final String mensagem;

  AlterarSenhaEmailResponseModel({
    this.sucesso = false,
    this.mensagem = "",
    this.email,
  });

  factory AlterarSenhaEmailResponseModel.fromJson(Map<String, dynamic> json) {
    return AlterarSenhaEmailResponseModel(
      sucesso: json['sucesso'] ?? false,
      mensagem: json['mensagem'] ?? "",
      email: json['email'] as String?,
    );
  }
}
