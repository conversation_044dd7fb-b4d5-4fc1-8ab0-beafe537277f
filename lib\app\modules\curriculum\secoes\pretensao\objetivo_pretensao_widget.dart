import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:get/get.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../../../shared/helpers/snack_bar_helper.dart';
import '../../../../shared/widgets/app_badge.dart';
import '../../curriculum_controller.dart';
import '../../curriculum_editing/curriculum_editing_module.dart';
import '../../widgets/curriculum_default_field.dart';
import '../secao/secao_widget.dart';

class ObjetivoPretensaoWidget extends StatefulWidget {
  const ObjetivoPretensaoWidget({super.key});

  @override
  _ObjetivoPretensaoWidgetState createState() =>
      _ObjetivoPretensaoWidgetState();
}

class _ObjetivoPretensaoWidgetState extends State<ObjetivoPretensaoWidget> {
  final CurriculumController controller = Modular.get();

  void onAddOrEdit() async {
    var result = await Modular.to.pushNamed(
      CurriculumEditingModule.route,
      arguments: CurriculumEditingModule.objetivoPretensao,
    );
    if (result == true) {
      SnackbarHelper.showSnackbarSucesso(
        context,
        'Objetivo e Pretensão Salarial salvos com sucesso',
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CurriculumController>(
      init: controller,
      builder: (_) => SecaoWidget(
        header: 'Objetivo e pretensão'.i18n,
        hasContent: controller.pessoa?.pretensaoSalarialID != 0 ||
            (controller.objective.objetivos?.isNotEmpty ?? false),
        onEdit: onAddOrEdit,
        replaceEditWithAdd: true,
        content: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            ColoredBox(
              color: Colors.white,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  ColoredBox(
                    color: Colors.white,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 10,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Visibility(
                            visible:
                                controller.pessoa?.pretensaoSalarialID != null,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Pretensão salarial'.i18n,
                                  style: const TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Inter',
                                  ),
                                ),
                                const SizedBox(
                                  height: 5,
                                ),
                                Text(
                                  controller
                                          .pessoa?.pretensaoSalarialFormatado ??
                                      '',
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontSize: 12,
                                    fontWeight: FontWeight.w400,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                              ],
                            ),
                          ),
                          Visibility(
                            visible:
                                (controller.objective.objetivos?.length ?? 0) >
                                    0,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Áreas de interesse'.i18n,
                                  style: const TextStyle(
                                    fontFamily: 'Inter',
                                    fontWeight: FontWeight.w500,
                                    fontSize: 14,
                                  ),
                                ),
                                const SizedBox(height: 5),
                                SizedBox(
                                  height: 35,
                                  child: ListView(
                                    clipBehavior: Clip.none,
                                    scrollDirection: Axis.horizontal,
                                    children:
                                        (controller.objective.objetivos ?? [])
                                            .map(
                                      (b) {
                                        return AppBadge(title: b.nome);
                                      },
                                    ).toList(),
                                  ),
                                )
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                ],
              ),
            ),
          ],
        ),
        noContent: CurriculumDefaultField(
          bottomAdd: onAddOrEdit,
          textBottom: 'Adicionar objetivo',
          description:
              'Inclua as áreas relacionadas ao seu objetivo profissional e sua pretensão salarial.',
        ),
      ),
    );
  }
}
