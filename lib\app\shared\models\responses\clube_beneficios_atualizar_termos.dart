class ClubeBeneficiosAtualizarTermosModel {
  final bool sucesso;
  final String? mensagem;

  ClubeBeneficiosAtualizarTermosModel({
    required this.sucesso,
    required this.mensagem,
  });

  factory ClubeBeneficiosAtualizarTermosModel.fromJson(
      Map<String, dynamic> json) {
    return ClubeBeneficiosAtualizarTermosModel(
      sucesso: json['sucesso'],
      mensagem: json['mensagem'],
    );
  }
}
