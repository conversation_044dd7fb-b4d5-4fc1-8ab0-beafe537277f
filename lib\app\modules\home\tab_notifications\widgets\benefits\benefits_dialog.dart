import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../../shared/models/responses/clube_beneficios_detalhes_model.dart';
import '../../../../../shared/models/responses/clube_beneficios_promocoes.dart';
import '../../../../../shared/widgets/app_image_network_widget.dart';
import 'benefits_category_card.dart';
import 'benefits_details.dart';

class BenefitsDialog extends StatefulWidget {
  const BenefitsDialog({
    super.key,
    this.model,
    this.modelPromo,
  });

  final ClubeBeneficiosDetalhesModel? model;
  final ClubeBeneficiosPromocoesModel? modelPromo;

  @override
  _BenefitsDialogState createState() => _BenefitsDialogState();
}

class _BenefitsDialogState extends State<BenefitsDialog> {
  String selectedTab = 'Detalhes';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        leadingWidth: 0,
        leading: const SizedBox.shrink(),
        elevation: 1,
        shadowColor: Colors.black45,
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  Navigator.of(context).pop();
                },
                customBorder: const CircleBorder(),
                splashColor: Colors.grey.withValues(alpha: 0.6),
                child: Container(
                  padding: const EdgeInsets.all(8.0),
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.close,
                    color: Colors.black,
                    size: 36,
                  ),
                ),
              ),
            ),
          ),
        ],
        title: Text(
          widget.model?.dados?.nome ?? '',
          style: const TextStyle(
            color: Colors.black,
            fontFamily: 'Inter',
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      body: ListView(
        children: [
          Column(
            children: [
              widget.model?.dados?.logo != null
                  ? AppImageNetworkWidget(
                      widget.model?.dados?.logo ?? '',
                    )
                  : Image.asset('lib/assets/images/placeholder.png'),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Text(
                  widget.model?.dados?.beneficio ?? 'Benefício',
                  style: const TextStyle(
                    fontSize: 20,
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 10,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.offline_bolt),
                    const SizedBox(width: 5),
                    Expanded(
                      child: Text(
                        widget.model?.dados?.beneficio ??
                            'Desconto exclusivo disponível!',
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 12,
                ),
                child: Wrap(
                  spacing: 10,
                  children: (widget.model?.dados?.palavrasChave?.split(', ') ??
                          [])
                      .map((palavra) => BenefitsCategoryCard(title: palavra))
                      .toList(),
                ),
              ),
              const Divider(
                color: Color(0xFFe0e0e0),
                thickness: 1,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 22),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        InkWell(
                          onTap: () {
                            setState(() {
                              selectedTab = 'Detalhes';
                            });
                          },
                          child: Text(
                            'Detalhes',
                            style: TextStyle(
                              fontSize: 20,
                              fontFamily: 'Inter',
                              fontWeight: FontWeight.w500,
                              color: selectedTab == 'Detalhes'
                                  ? Colors.blue
                                  : Colors.black,
                            ),
                          ),
                        ),
                        const SizedBox(width: 24),
                        InkWell(
                          onTap: () {
                            setState(() {
                              selectedTab = '% Promoções';
                            });
                          },
                          child: Row(
                            children: [
                              Text(
                                '% Promoções',
                                style: TextStyle(
                                  fontSize: 20,
                                  fontFamily: 'Inter',
                                  fontWeight: FontWeight.w500,
                                  color: selectedTab == '% Promoções'
                                      ? Colors.blue
                                      : Colors.black,
                                ),
                              ),
                              Container(
                                height: 25,
                                width: 25,
                                padding: const EdgeInsets.all(2),
                                margin: const EdgeInsets.all(5),
                                decoration: BoxDecoration(
                                  color: const Color(0xFF3AA141),
                                  borderRadius: BorderRadius.circular(50),
                                ),
                                child: Center(
                                  child: Text(
                                    '${widget.modelPromo?.dados?.length}',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 10,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const Divider(color: Color(0xFFe0e0e0), thickness: 1.5),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: ConstrainedBox(
                  constraints: const BoxConstraints(
                    minHeight: 0,
                  ),
                  child: selectedTab == 'Detalhes'
                      ? Html(
                          data: widget.model?.dados?.descricao ?? "",
                          onLinkTap: (url, _, __) {
                            if (url != null) {
                              launchUrl(Uri.parse(url));
                            }
                          },
                        )
                      : BenefitsDetails(widget: widget),
                ),
              ),
              const Divider(color: Color(0xFFe0e0e0), thickness: 1.5),
            ],
          ),
        ],
      ),
    );
  }
}
