import 'package:dio/dio.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../core/app_rest.dart';
import '../models/cadastrar_model.dart';
import '../models/responses/cadastrar_response_model.dart';

class RegisterRepository {
  final AppRest _rest = Modular.get();

  Future<CadastrarResponseModel> postCadastrar(CadastrarModel model) async {
    try {
      final response = await _rest.post(
        '/candidato/cadastrar-v2',
        data: model.toJson(),
      );

      return CadastrarResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return CadastrarResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return CadastrarResponseModel.fromJson(err.response!.data);
    }
  }
}
