import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../../shared/app_container.dart';
import '../../../shared/core/app_config.dart';
import '../../../shared/widgets/app_default_button.dart';
import '../questionnaire_controller.dart';
import 'widget_video_player.dart';

class WidgetPerguntaVideo extends StatelessWidget {
  const WidgetPerguntaVideo({
    super.key,
    required this.context,
    required this.controller,
  });

  final BuildContext context;
  final QuestionnaireController controller;

  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;
    var width = size.width * (Platform.isIOS ? 0.8 : 0.9);
    var height =
        width * ((size.height - (Platform.isIOS ? 140 : 80)) / size.width);

    return Observer(
      builder: (_) {
        if (controller.video == null) {
          return AppContainer(
            width: double.infinity,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Icon(
                  Icons.videocam,
                  size: 120,
                  color: Colors.grey[600],
                ),
                const Text(
                  'Você deverá gravar um vídeo de até:',
                  style: TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 5),
                Text(
                  '${controller.pergunta!.tempo} segundos',
                  style: const TextStyle(
                    fontSize: 16,
                    color: AppConfig.colorPrimary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 20),
                AppDefaultButton(
                  onPressed: controller.gravarVideo,
                  title: Text(
                    'REALIZAR GRAVAÇÃO'.i18n,
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                      fontSize: 14,
                    ),
                  ),
                ),
                const SizedBox(height: 20),
              ],
            ),
          );
        }
        return AppContainer(
          width: double.maxFinite,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const Text(
                'Vídeo gravado',
                style: TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 10),
              AppDefaultButton(
                color: AppConfig.green,
                onPressed: controller.enviarVideo,
                title: Text(
                  'ENVIAR VÍDEO'.i18n,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                ),
              ),
              const SizedBox(height: 10),
              AppDefaultButton(
                color: AppConfig.colorPrimary,
                onPressed: controller.gravarVideo,
                title: Text(
                  'GRAVAR NOVAMENTE'.i18n,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                ),
              ),
              const SizedBox(height: 10),
              Observer(
                builder: (_) {
                  return controller.video != null
                      ? WidgetVideoPlayer(
                          videoFile: controller.video,
                          width: width,
                          height: height,
                          newKey: UniqueKey())
                      : Container(
                          width: size.width * 0.5,
                          height: size.height * 0.5,
                          color: Colors.black,
                        );
                },
              ),
              const SizedBox(
                height: 15,
              ),
            ],
          ),
        );
      },
    );
  }
}
