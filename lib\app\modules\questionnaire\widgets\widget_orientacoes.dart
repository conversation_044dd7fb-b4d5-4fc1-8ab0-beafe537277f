import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';

import '../../../shared/core/app_config.dart';
import '../../../shared/models/responses/questionario_response_model.dart';

class WidgetOrientacoes extends StatelessWidget {
  const WidgetOrientacoes({
    super.key,
    required this.questionario,
  });

  final QuestionarioModel? questionario;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.only(top: 20),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(15),
            color: Colors.white,
            child: const Text(
              'Leia algumas orientações importantes antes de começar',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: AppConfig.colorPrimary,
                fontSize: 20,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          ...List.generate(
            questionario!.orientacoes!.length,
            (i) {
              var orientacao = questionario!.orientacoes![i];

              return Container(
                padding: const EdgeInsets.all(15),
                color: orientacao.id == 'conexao'
                    ? const Color(0xFFF9E4C9)
                    : Colors.white,
                child: Column(
                  children: [
                    Html(
                      data: orientacao.titulo,
                      style: {
                        'html': Style.fromTextStyle(const TextStyle(
                                fontFamily: 'Roboto',
                                fontWeight: FontWeight.bold,
                                fontSize: 16))
                            .merge(Style(textAlign: TextAlign.center))
                      },
                    ),
                    const SizedBox(
                      height: 3,
                    ),
                    Html(
                      data: orientacao.descricao ?? '',
                    )
                  ],
                ),
              );
            },
          )
        ],
      ),
    );
  }
}
