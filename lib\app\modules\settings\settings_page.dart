import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../shared/core/app_config.dart';
import '../../shared/helpers/snack_bar_helper.dart';
import '../../shared/widgets/app_default_button.dart';
import '../../shared/widgets/app_usuario_card/app_usuario_card_controller.dart';
import '../auth/change_password/change_password_module.dart';
import '../home/<USER>';
import 'settings_controller.dart';

class SettingsPage extends StatelessWidget {
  SettingsPage({super.key});

  final controller = Modular.get<SettingsController>();
  final appUsuarioController = Modular.get<AppUsuarioCardController>();
  final homeController = Modular.get<HomeController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          "Configurações",
          style: TextStyle(color: Colors.black),
        ),
        elevation: 0.5,
        backgroundColor: AppConfig.white,
      ),
      body: ListView(
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 10.0),
            child: ListTile(
              onTap: () {
                Modular.to.pushNamed(ChangePasswordModule.route);
              },
              title: const Text(
                "Alterar senha",
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w500,
                ),
              ),
              subtitle: const Text(
                "Escolha uma senha exclusiva para proteger sua conta",
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 14,
                ),
              ),
            ),
          ),
          const Divider(
            height: 0,
          ),
          ListTile(
            title: const Text(
              "Encerrar a conta",
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w500,
                color: Colors.red,
              ),
            ),
            subtitle: const Text(
              "Encerre sua conta se desejar",
              style: TextStyle(
                color: Colors.red,
                fontSize: 14,
              ),
            ),
            onTap: () {
              showDialog(
                context: context,
                builder: (context) {
                  return SimpleDialog(
                    contentPadding: const EdgeInsets.all(15),
                    title: const Text(
                      'Você realmente quer desativar sua conta?',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 20,
                      ),
                    ),
                    children: <Widget>[
                      Column(
                        children: [
                          Row(
                            children: <Widget>[
                              Expanded(
                                child: AppDefaultButton(
                                  color: AppConfig.red,
                                  onPressed: controller.desativarConta,
                                  title: const Text(
                                    'SIM',
                                    style: TextStyle(color: Colors.white),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 5),
                              Expanded(
                                child: AppDefaultButton(
                                  color: Colors.green,
                                  onPressed: () => Modular.to.pop(),
                                  title: const Text(
                                    'NÃO',
                                    style: TextStyle(
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 40),
                          const Text(
                            'Caso você queira excluir definitivamente seus dados, entre em contato conosco clicando no botão abaixo.',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 13,
                            ),
                          ),
                          const SizedBox(height: 10),
                          ElevatedButton(
                            style: ButtonStyle(
                              backgroundColor: WidgetStatePropertyAll(
                                AppConfig.colorPrimary.withValues(
                                  alpha: 0.7,
                                ),
                              ),
                            ),
                            onPressed: () => _launchURL(
                              'https://www.empregare.com/pt-br/suporte/novo',
                            ),
                            child: const Text(
                              'Excluir Definitivamente',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.white,
                              ),
                            ),
                          )
                        ],
                      ),
                    ],
                  );
                },
              );
            },
          ),
          ListTile(
            title: const Text(
              "Apagar a conta",
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w500,
                color: Colors.red,
              ),
            ),
            subtitle: const Text(
              "Todos os seus dados serão excluídos",
              style: TextStyle(
                color: Colors.red,
                fontSize: 14,
              ),
            ),
            onTap: () {
              showDialog(
                context: context,
                builder: (context) {
                  return SimpleDialog(
                    contentPadding: const EdgeInsets.all(15),
                    title: const Text(
                      'Você realmente quer desativar sua conta?',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 20,
                      ),
                    ),
                    children: <Widget>[
                      Row(
                        children: <Widget>[
                          Expanded(
                            child: AppDefaultButton(
                              color: AppConfig.red,
                              onPressed: () async {
                                final response = await controller.delete();
                                if (response == null) return;
                                Navigator.of(context).pop();
                                SnackbarHelper.error(
                                    context, response.mensagem);
                              },
                              title: const Text(
                                'SIM',
                                style: TextStyle(color: Colors.white),
                              ),
                            ),
                          ),
                          const SizedBox(width: 5),
                          Expanded(
                            child: AppDefaultButton(
                              color: Colors.green,
                              onPressed: () => Modular.to.pop(),
                              title: const Text(
                                'NÃO',
                                style: TextStyle(
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  );
                },
              );
            },
          ),
          const Divider(),
        ],
      ),
    );
  }
}

void _launchURL(String url) async {
  if (await canLaunchUrl(Uri.parse(url))) {
    await launchUrl(Uri.parse(url));
  } else {
    throw 'Não foi possível abrir a URL: $url';
  }
}
