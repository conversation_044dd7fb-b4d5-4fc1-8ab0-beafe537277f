import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:get/get.dart';

import '../../../shared/libs/get_version_lib.dart';
import '../../../shared/mixins/loader_mixin.dart';
import '../../../shared/models/login_model.dart';
import '../../../shared/models/responses/pessoa_response_model.dart';
import '../../../shared/models/session_model.dart';
import '../../../shared/repositories/login_repository.dart';
import '../../../shared/services/session_service.dart';

class LoginController extends GetxController with LoaderMixin {
  final SessionService _session = Modular.get();
  final LoginRepository _repository = Modular.get();

  final formKey = GlobalKey<FormState>();

  String? message;
  void setMessage(String value) {
    message = value.isEmpty ? null : value;
    update();
  }

  List<PessoaResponseModel> people = [];
  void setPeople(List<PessoaResponseModel> value) {
    people = value;
    update();
  }

  String _user = '';
  void user(String value) => _user = value.trim();

  String _password = '';
  void password(String value) => _password = value.trim();

  Future<LoginModel> _getLoginModel({String? personId}) async {
    final getVersionLib = GetVersionLib();
    await getVersionLib.initialize();
    return LoginModel(
      usuario: _user,
      senha: _password,
      pessoaID: personId,
      urlBase: 'app',
      ip: getVersionLib.ip,
      navegador: getVersionLib.toString(),
    );
  }

  Future<bool> login([String? personId]) async {
    try {
      changeLoading(true);

      final response = await _repository.postLogin(await _getLoginModel(personId: personId));

      if (response.token?.isNotEmpty ?? false) {
        await _session.set(SessionModel(token: response.token));
        return true;
      } else if (response.pessoas?.isNotEmpty ?? false) {
        setPeople(response.pessoas!);
      } else if (response.mensagem.isNotEmpty) {
        setMessage(response.mensagem);
      } else {
        setMessage("Ocorreu um erro desconhecido!");
      }

      return false;
    } catch (e) {
      setMessage("Ocorreu um erro desconhecido!");
      return false;
    } finally {
      changeLoading(false);
    }
  }
}
