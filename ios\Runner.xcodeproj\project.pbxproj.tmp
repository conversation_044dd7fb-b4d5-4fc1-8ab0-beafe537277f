<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>archiveVersion</key>
	<string>1</string>
	<key>classes</key>
	<dict/>
	<key>objectVersion</key>
	<string>51</string>
	<key>objects</key>
	<dict>
		<key>001FA50F6108F8D55703911A</key>
		<dict>
			<key>children</key>
			<array>
				<string>24CF0CE817B2AE4CDB3E5A5A</string>
			</array>
			<key>isa</key>
			<string>PBXGroup</string>
			<key>name</key>
			<string>Frameworks</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>0099643AD90D4228B2C108D4</key>
		<dict>
			<key>fileRef</key>
			<string>24CF0CE817B2AE4CDB3E5A5A</string>
			<key>isa</key>
			<string>PBXBuildFile</string>
		</dict>
		<key>0A5E0FF176AC333CA1282518</key>
		<dict>
			<key>includeInIndex</key>
			<string>1</string>
			<key>isa</key>
			<string>PBXFileReference</string>
			<key>lastKnownFileType</key>
			<string>text.xcconfig</string>
			<key>name</key>
			<string>Pods-Runner.release.xcconfig</string>
			<key>path</key>
			<string>Target Support Files/Pods-Runner/Pods-Runner.release.xcconfig</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>1498D2321E8E86230040F4C2</key>
		<dict>
			<key>isa</key>
			<string>PBXFileReference</string>
			<key>lastKnownFileType</key>
			<string>sourcecode.c.h</string>
			<key>path</key>
			<string>GeneratedPluginRegistrant.h</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>1498D2331E8E89220040F4C2</key>
		<dict>
			<key>fileEncoding</key>
			<string>4</string>
			<key>isa</key>
			<string>PBXFileReference</string>
			<key>lastKnownFileType</key>
			<string>sourcecode.c.objc</string>
			<key>path</key>
			<string>GeneratedPluginRegistrant.m</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>1498D2341E8E89220040F4C2</key>
		<dict>
			<key>fileRef</key>
			<string>1498D2331E8E89220040F4C2</string>
			<key>isa</key>
			<string>PBXBuildFile</string>
		</dict>
		<key>249021D3217E4FDB00AE95B9</key>
		<dict>
			<key>buildSettings</key>
			<dict>
				<key>ALWAYS_SEARCH_USER_PATHS</key>
				<string>NO</string>
				<key>CLANG_ANALYZER_NONNULL</key>
				<string>YES</string>
				<key>CLANG_CXX_LANGUAGE_STANDARD</key>
				<string>gnu++0x</string>
				<key>CLANG_CXX_LIBRARY</key>
				<string>libc++</string>
				<key>CLANG_ENABLE_MODULES</key>
				<string>YES</string>
				<key>CLANG_ENABLE_OBJC_ARC</key>
				<string>YES</string>
				<key>CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING</key>
				<string>YES</string>
				<key>CLANG_WARN_BOOL_CONVERSION</key>
				<string>YES</string>
				<key>CLANG_WARN_COMMA</key>
				<string>YES</string>
				<key>CLANG_WARN_CONSTANT_CONVERSION</key>
				<string>YES</string>
				<key>CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS</key>
				<string>YES</string>
				<key>CLANG_WARN_DIRECT_OBJC_ISA_USAGE</key>
				<string>YES_ERROR</string>
				<key>CLANG_WARN_EMPTY_BODY</key>
				<string>YES</string>
				<key>CLANG_WARN_ENUM_CONVERSION</key>
				<string>YES</string>
				<key>CLANG_WARN_INFINITE_RECURSION</key>
				<string>YES</string>
				<key>CLANG_WARN_INT_CONVERSION</key>
				<string>YES</string>
				<key>CLANG_WARN_NON_LITERAL_NULL_CONVERSION</key>
				<string>YES</string>
				<key>CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF</key>
				<string>YES</string>
				<key>CLANG_WARN_OBJC_LITERAL_CONVERSION</key>
				<string>YES</string>
				<key>CLANG_WARN_OBJC_ROOT_CLASS</key>
				<string>YES_ERROR</string>
				<key>CLANG_WARN_RANGE_LOOP_ANALYSIS</key>
				<string>YES</string>
				<key>CLANG_WARN_STRICT_PROTOTYPES</key>
				<string>YES</string>
				<key>CLANG_WARN_SUSPICIOUS_MOVE</key>
				<string>YES</string>
				<key>CLANG_WARN_UNREACHABLE_CODE</key>
				<string>YES</string>
				<key>CLANG_WARN__DUPLICATE_METHOD_MATCH</key>
				<string>YES</string>
				<key>CODE_SIGN_IDENTITY[sdk=iphoneos*]</key>
				<string>iPhone Developer</string>
				<key>COPY_PHASE_STRIP</key>
				<string>NO</string>
				<key>DEBUG_INFORMATION_FORMAT</key>
				<string>dwarf-with-dsym</string>
				<key>ENABLE_NS_ASSERTIONS</key>
				<string>NO</string>
				<key>ENABLE_STRICT_OBJC_MSGSEND</key>
				<string>YES</string>
				<key>GCC_C_LANGUAGE_STANDARD</key>
				<string>gnu99</string>
				<key>GCC_NO_COMMON_BLOCKS</key>
				<string>YES</string>
				<key>GCC_WARN_64_TO_32_BIT_CONVERSION</key>
				<string>YES</string>
				<key>GCC_WARN_ABOUT_RETURN_TYPE</key>
				<string>YES_ERROR</string>
				<key>GCC_WARN_UNDECLARED_SELECTOR</key>
				<string>YES</string>
				<key>GCC_WARN_UNINITIALIZED_AUTOS</key>
				<string>YES_AGGRESSIVE</string>
				<key>GCC_WARN_UNUSED_FUNCTION</key>
				<string>YES</string>
				<key>GCC_WARN_UNUSED_VARIABLE</key>
				<string>YES</string>
				<key>IPHONEOS_DEPLOYMENT_TARGET</key>
				<string>9.0</string>
				<key>MTL_ENABLE_DEBUG_INFO</key>
				<string>NO</string>
				<key>SDKROOT</key>
				<string>iphoneos</string>
				<key>SUPPORTED_PLATFORMS</key>
				<string>iphoneos</string>
				<key>TARGETED_DEVICE_FAMILY</key>
				<string>1,2</string>
				<key>VALIDATE_PRODUCT</key>
				<string>YES</string>
			</dict>
			<key>isa</key>
			<string>XCBuildConfiguration</string>
			<key>name</key>
			<string>Profile</string>
		</dict>
		<key>249021D4217E4FDB00AE95B9</key>
		<dict>
			<key>baseConfigurationReference</key>
			<string>7AFA3C8E1D35360C0083082E</string>
			<key>buildSettings</key>
			<dict>
				<key>ASSETCATALOG_COMPILER_APPICON_NAME</key>
				<string>AppIcon</string>
				<key>CLANG_ENABLE_MODULES</key>
				<string>YES</string>
				<key>CODE_SIGN_ENTITLEMENTS</key>
				<string>Runner/RunnerProfile.entitlements</string>
				<key>CODE_SIGN_IDENTITY</key>
				<string>Apple Development</string>
				<key>CODE_SIGN_STYLE</key>
				<string>Automatic</string>
				<key>CURRENT_PROJECT_VERSION</key>
				<string>17</string>
				<key>DEVELOPMENT_TEAM</key>
				<string></string>
				<key>ENABLE_BITCODE</key>
				<string>NO</string>
				<key>INFOPLIST_FILE</key>
				<string>Runner/Info.plist</string>
				<key>IPHONEOS_DEPLOYMENT_TARGET</key>
				<string>10.0</string>
				<key>LD_RUNPATH_SEARCH_PATHS</key>
				<array>
					<string>$(inherited)</string>
					<string>@executable_path/Frameworks</string>
				</array>
				<key>MARKETING_VERSION</key>
				<string>2.0.0</string>
				<key>PRODUCT_BUNDLE_IDENTIFIER</key>
				<string>com.empregare.app</string>
				<key>PRODUCT_NAME</key>
				<string>$(TARGET_NAME)</string>
				<key>PROVISIONING_PROFILE_SPECIFIER</key>
				<string></string>
				<key>SWIFT_OBJC_BRIDGING_HEADER</key>
				<string>Runner/Runner-Bridging-Header.h</string>
				<key>SWIFT_VERSION</key>
				<string>5.0</string>
				<key>VERSIONING_SYSTEM</key>
				<string>apple-generic</string>
			</dict>
			<key>isa</key>
			<string>XCBuildConfiguration</string>
			<key>name</key>
			<string>Profile</string>
		</dict>
		<key>24CF0CE817B2AE4CDB3E5A5A</key>
		<dict>
			<key>explicitFileType</key>
			<string>wrapper.framework</string>
			<key>includeInIndex</key>
			<string>0</string>
			<key>isa</key>
			<string>PBXFileReference</string>
			<key>path</key>
			<string>Pods_Runner.framework</string>
			<key>sourceTree</key>
			<string>BUILT_PRODUCTS_DIR</string>
		</dict>
		<key>28D2DD93B8CED5F86A1868BC</key>
		<dict>
			<key>buildActionMask</key>
			<string>2147483647</string>
			<key>files</key>
			<array/>
			<key>inputFileListPaths</key>
			<array>
				<string>${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks-${CONFIGURATION}-input-files.xcfilelist</string>
			</array>
			<key>isa</key>
			<string>PBXShellScriptBuildPhase</string>
			<key>name</key>
			<string>[CP] Embed Pods Frameworks</string>
			<key>outputFileListPaths</key>
			<array>
				<string>${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks-${CONFIGURATION}-output-files.xcfilelist</string>
			</array>
			<key>runOnlyForDeploymentPostprocessing</key>
			<string>0</string>
			<key>shellPath</key>
			<string>/bin/sh</string>
			<key>shellScript</key>
			<string>"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks.sh"
</string>
			<key>showEnvVarsInLog</key>
			<string>0</string>
		</dict>
		<key>3B06AD1E1E4923F5004D2608</key>
		<dict>
			<key>buildActionMask</key>
			<string>2147483647</string>
			<key>files</key>
			<array/>
			<key>inputPaths</key>
			<array/>
			<key>isa</key>
			<string>PBXShellScriptBuildPhase</string>
			<key>name</key>
			<string>Thin Binary</string>
			<key>outputPaths</key>
			<array/>
			<key>runOnlyForDeploymentPostprocessing</key>
			<string>0</string>
			<key>shellPath</key>
			<string>/bin/sh</string>
			<key>shellScript</key>
			<string>/bin/sh "$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh" embed_and_thin</string>
		</dict>
		<key>3B3967151E833CAA004F5970</key>
		<dict>
			<key>fileEncoding</key>
			<string>4</string>
			<key>isa</key>
			<string>PBXFileReference</string>
			<key>lastKnownFileType</key>
			<string>text.plist.xml</string>
			<key>name</key>
			<string>AppFrameworkInfo.plist</string>
			<key>path</key>
			<string>Flutter/AppFrameworkInfo.plist</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>3B3967161E833CAA004F5970</key>
		<dict>
			<key>fileRef</key>
			<string>3B3967151E833CAA004F5970</string>
			<key>isa</key>
			<string>PBXBuildFile</string>
		</dict>
		<key>3C4C97A6B7074CC02FFD6313</key>
		<dict>
			<key>buildActionMask</key>
			<string>2147483647</string>
			<key>files</key>
			<array/>
			<key>inputFileListPaths</key>
			<array/>
			<key>inputPaths</key>
			<array>
				<string>${PODS_PODFILE_DIR_PATH}/Podfile.lock</string>
				<string>${PODS_ROOT}/Manifest.lock</string>
			</array>
			<key>isa</key>
			<string>PBXShellScriptBuildPhase</string>
			<key>name</key>
			<string>[CP] Check Pods Manifest.lock</string>
			<key>outputFileListPaths</key>
			<array/>
			<key>outputPaths</key>
			<array>
				<string>$(DERIVED_FILE_DIR)/Pods-Runner-checkManifestLockResult.txt</string>
			</array>
			<key>runOnlyForDeploymentPostprocessing</key>
			<string>0</string>
			<key>shellPath</key>
			<string>/bin/sh</string>
			<key>shellScript</key>
			<string>diff "${PODS_PODFILE_DIR_PATH}/Podfile.lock" "${PODS_ROOT}/Manifest.lock" &gt; /dev/null
if [ $? != 0 ] ; then
    # print error to STDERR
    echo "error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation." &gt;&amp;2
    exit 1
fi
# This output is used by Xcode 'outputs' to avoid re-running this script phase.
echo "SUCCESS" &gt; "${SCRIPT_OUTPUT_FILE_0}"
</string>
			<key>showEnvVarsInLog</key>
			<string>0</string>
		</dict>
		<key>6552CF315A682D3AF7357F14</key>
		<dict>
			<key>children</key>
			<array>
				<string>8EE3F014C2AB755E987288AC</string>
				<string>0A5E0FF176AC333CA1282518</string>
				<string>EB4CADF7021B680243F63761</string>
			</array>
			<key>isa</key>
			<string>PBXGroup</string>
			<key>path</key>
			<string>Pods</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>74858FAD1ED2DC5600515810</key>
		<dict>
			<key>isa</key>
			<string>PBXFileReference</string>
			<key>lastKnownFileType</key>
			<string>sourcecode.c.h</string>
			<key>path</key>
			<string>Runner-Bridging-Header.h</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>74858FAE1ED2DC5600515810</key>
		<dict>
			<key>fileEncoding</key>
			<string>4</string>
			<key>isa</key>
			<string>PBXFileReference</string>
			<key>lastKnownFileType</key>
			<string>sourcecode.swift</string>
			<key>path</key>
			<string>AppDelegate.swift</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>74858FAF1ED2DC5600515810</key>
		<dict>
			<key>fileRef</key>
			<string>74858FAE1ED2DC5600515810</string>
			<key>isa</key>
			<string>PBXBuildFile</string>
		</dict>
		<key>7AFA3C8E1D35360C0083082E</key>
		<dict>
			<key>isa</key>
			<string>PBXFileReference</string>
			<key>lastKnownFileType</key>
			<string>text.xcconfig</string>
			<key>name</key>
			<string>Release.xcconfig</string>
			<key>path</key>
			<string>Flutter/Release.xcconfig</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>8EE3F014C2AB755E987288AC</key>
		<dict>
			<key>includeInIndex</key>
			<string>1</string>
			<key>isa</key>
			<string>PBXFileReference</string>
			<key>lastKnownFileType</key>
			<string>text.xcconfig</string>
			<key>name</key>
			<string>Pods-Runner.debug.xcconfig</string>
			<key>path</key>
			<string>Target Support Files/Pods-Runner/Pods-Runner.debug.xcconfig</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>9705A1C41CF9048500538489</key>
		<dict>
			<key>buildActionMask</key>
			<string>2147483647</string>
			<key>dstPath</key>
			<string></string>
			<key>dstSubfolderSpec</key>
			<string>10</string>
			<key>files</key>
			<array/>
			<key>isa</key>
			<string>PBXCopyFilesBuildPhase</string>
			<key>name</key>
			<string>Embed Frameworks</string>
			<key>runOnlyForDeploymentPostprocessing</key>
			<string>0</string>
		</dict>
		<key>9740EEB11CF90186004384FC</key>
		<dict>
			<key>children</key>
			<array>
				<string>3B3967151E833CAA004F5970</string>
				<string>9740EEB21CF90195004384FC</string>
				<string>7AFA3C8E1D35360C0083082E</string>
				<string>9740EEB31CF90195004384FC</string>
			</array>
			<key>isa</key>
			<string>PBXGroup</string>
			<key>name</key>
			<string>Flutter</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>9740EEB21CF90195004384FC</key>
		<dict>
			<key>fileEncoding</key>
			<string>4</string>
			<key>isa</key>
			<string>PBXFileReference</string>
			<key>lastKnownFileType</key>
			<string>text.xcconfig</string>
			<key>name</key>
			<string>Debug.xcconfig</string>
			<key>path</key>
			<string>Flutter/Debug.xcconfig</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>9740EEB31CF90195004384FC</key>
		<dict>
			<key>fileEncoding</key>
			<string>4</string>
			<key>isa</key>
			<string>PBXFileReference</string>
			<key>lastKnownFileType</key>
			<string>text.xcconfig</string>
			<key>name</key>
			<string>Generated.xcconfig</string>
			<key>path</key>
			<string>Flutter/Generated.xcconfig</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>9740EEB61CF901F6004384FC</key>
		<dict>
			<key>buildActionMask</key>
			<string>2147483647</string>
			<key>files</key>
			<array/>
			<key>inputPaths</key>
			<array/>
			<key>isa</key>
			<string>PBXShellScriptBuildPhase</string>
			<key>name</key>
			<string>Run Script</string>
			<key>outputPaths</key>
			<array/>
			<key>runOnlyForDeploymentPostprocessing</key>
			<string>0</string>
			<key>shellPath</key>
			<string>/bin/sh</string>
			<key>shellScript</key>
			<string>/bin/sh "$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh" build</string>
		</dict>
		<key>97C146E51CF9000F007C117D</key>
		<dict>
			<key>children</key>
			<array>
				<string>9740EEB11CF90186004384FC</string>
				<string>97C146F01CF9000F007C117D</string>
				<string>97C146EF1CF9000F007C117D</string>
				<string>6552CF315A682D3AF7357F14</string>
				<string>001FA50F6108F8D55703911A</string>
			</array>
			<key>isa</key>
			<string>PBXGroup</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>97C146E61CF9000F007C117D</key>
		<dict>
			<key>attributes</key>
			<dict>
				<key>LastUpgradeCheck</key>
				<string>1020</string>
				<key>ORGANIZATIONNAME</key>
				<string></string>
				<key>TargetAttributes</key>
				<dict>
					<key>97C146ED1CF9000F007C117D</key>
					<dict>
						<key>CreatedOnToolsVersion</key>
						<string>7.3.1</string>
						<key>LastSwiftMigration</key>
						<string>1100</string>
					</dict>
				</dict>
			</dict>
			<key>buildConfigurationList</key>
			<string>97C146E91CF9000F007C117D</string>
			<key>compatibilityVersion</key>
			<string>Xcode 9.3</string>
			<key>developmentRegion</key>
			<string>en</string>
			<key>hasScannedForEncodings</key>
			<string>0</string>
			<key>isa</key>
			<string>PBXProject</string>
			<key>knownRegions</key>
			<array>
				<string>en</string>
				<string>Base</string>
			</array>
			<key>mainGroup</key>
			<string>97C146E51CF9000F007C117D</string>
			<key>productRefGroup</key>
			<string>97C146EF1CF9000F007C117D</string>
			<key>projectDirPath</key>
			<string></string>
			<key>projectRoot</key>
			<string></string>
			<key>targets</key>
			<array>
				<string>97C146ED1CF9000F007C117D</string>
			</array>
		</dict>
		<key>97C146E91CF9000F007C117D</key>
		<dict>
			<key>buildConfigurations</key>
			<array>
				<string>97C147031CF9000F007C117D</string>
				<string>97C147041CF9000F007C117D</string>
				<string>249021D3217E4FDB00AE95B9</string>
			</array>
			<key>defaultConfigurationIsVisible</key>
			<string>0</string>
			<key>defaultConfigurationName</key>
			<string>Release</string>
			<key>isa</key>
			<string>XCConfigurationList</string>
		</dict>
		<key>97C146EA1CF9000F007C117D</key>
		<dict>
			<key>buildActionMask</key>
			<string>2147483647</string>
			<key>files</key>
			<array>
				<string>74858FAF1ED2DC5600515810</string>
				<string>1498D2341E8E89220040F4C2</string>
			</array>
			<key>isa</key>
			<string>PBXSourcesBuildPhase</string>
			<key>runOnlyForDeploymentPostprocessing</key>
			<string>0</string>
		</dict>
		<key>97C146EB1CF9000F007C117D</key>
		<dict>
			<key>buildActionMask</key>
			<string>2147483647</string>
			<key>files</key>
			<array>
				<string>0099643AD90D4228B2C108D4</string>
			</array>
			<key>isa</key>
			<string>PBXFrameworksBuildPhase</string>
			<key>runOnlyForDeploymentPostprocessing</key>
			<string>0</string>
		</dict>
		<key>97C146EC1CF9000F007C117D</key>
		<dict>
			<key>buildActionMask</key>
			<string>2147483647</string>
			<key>files</key>
			<array>
				<string>97C147011CF9000F007C117D</string>
				<string>3B3967161E833CAA004F5970</string>
				<string>EAF9556726E65FFF00854ABF</string>
				<string>97C146FE1CF9000F007C117D</string>
				<string>97C146FC1CF9000F007C117D</string>
			</array>
			<key>isa</key>
			<string>PBXResourcesBuildPhase</string>
			<key>runOnlyForDeploymentPostprocessing</key>
			<string>0</string>
		</dict>
		<key>97C146ED1CF9000F007C117D</key>
		<dict>
			<key>buildConfigurationList</key>
			<string>97C147051CF9000F007C117D</string>
			<key>buildPhases</key>
			<array>
				<string>3C4C97A6B7074CC02FFD6313</string>
				<string>9740EEB61CF901F6004384FC</string>
				<string>97C146EA1CF9000F007C117D</string>
				<string>97C146EB1CF9000F007C117D</string>
				<string>97C146EC1CF9000F007C117D</string>
				<string>9705A1C41CF9048500538489</string>
				<string>3B06AD1E1E4923F5004D2608</string>
				<string>28D2DD93B8CED5F86A1868BC</string>
			</array>
			<key>buildRules</key>
			<array/>
			<key>dependencies</key>
			<array/>
			<key>isa</key>
			<string>PBXNativeTarget</string>
			<key>name</key>
			<string>Runner</string>
			<key>productName</key>
			<string>Runner</string>
			<key>productReference</key>
			<string>97C146EE1CF9000F007C117D</string>
			<key>productType</key>
			<string>com.apple.product-type.application</string>
		</dict>
		<key>97C146EE1CF9000F007C117D</key>
		<dict>
			<key>explicitFileType</key>
			<string>wrapper.application</string>
			<key>includeInIndex</key>
			<string>0</string>
			<key>isa</key>
			<string>PBXFileReference</string>
			<key>path</key>
			<string>Runner.app</string>
			<key>sourceTree</key>
			<string>BUILT_PRODUCTS_DIR</string>
		</dict>
		<key>97C146EF1CF9000F007C117D</key>
		<dict>
			<key>children</key>
			<array>
				<string>97C146EE1CF9000F007C117D</string>
			</array>
			<key>isa</key>
			<string>PBXGroup</string>
			<key>name</key>
			<string>Products</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>97C146F01CF9000F007C117D</key>
		<dict>
			<key>children</key>
			<array>
				<string>EA8EBBC026E6856C00588BF3</string>
				<string>EAF9556626E65FFF00854ABF</string>
				<string>97C146FA1CF9000F007C117D</string>
				<string>97C146FD1CF9000F007C117D</string>
				<string>97C146FF1CF9000F007C117D</string>
				<string>97C147021CF9000F007C117D</string>
				<string>1498D2321E8E86230040F4C2</string>
				<string>1498D2331E8E89220040F4C2</string>
				<string>74858FAE1ED2DC5600515810</string>
				<string>74858FAD1ED2DC5600515810</string>
			</array>
			<key>isa</key>
			<string>PBXGroup</string>
			<key>path</key>
			<string>Runner</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>97C146FA1CF9000F007C117D</key>
		<dict>
			<key>children</key>
			<array>
				<string>97C146FB1CF9000F007C117D</string>
			</array>
			<key>isa</key>
			<string>PBXVariantGroup</string>
			<key>name</key>
			<string>Main.storyboard</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>97C146FB1CF9000F007C117D</key>
		<dict>
			<key>isa</key>
			<string>PBXFileReference</string>
			<key>lastKnownFileType</key>
			<string>file.storyboard</string>
			<key>name</key>
			<string>Base</string>
			<key>path</key>
			<string>Base.lproj/Main.storyboard</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>97C146FC1CF9000F007C117D</key>
		<dict>
			<key>fileRef</key>
			<string>97C146FA1CF9000F007C117D</string>
			<key>isa</key>
			<string>PBXBuildFile</string>
		</dict>
		<key>97C146FD1CF9000F007C117D</key>
		<dict>
			<key>isa</key>
			<string>PBXFileReference</string>
			<key>lastKnownFileType</key>
			<string>folder.assetcatalog</string>
			<key>path</key>
			<string>Assets.xcassets</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>97C146FE1CF9000F007C117D</key>
		<dict>
			<key>fileRef</key>
			<string>97C146FD1CF9000F007C117D</string>
			<key>isa</key>
			<string>PBXBuildFile</string>
		</dict>
		<key>97C146FF1CF9000F007C117D</key>
		<dict>
			<key>children</key>
			<array>
				<string>97C147001CF9000F007C117D</string>
			</array>
			<key>isa</key>
			<string>PBXVariantGroup</string>
			<key>name</key>
			<string>LaunchScreen.storyboard</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>97C147001CF9000F007C117D</key>
		<dict>
			<key>isa</key>
			<string>PBXFileReference</string>
			<key>lastKnownFileType</key>
			<string>file.storyboard</string>
			<key>name</key>
			<string>Base</string>
			<key>path</key>
			<string>Base.lproj/LaunchScreen.storyboard</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>97C147011CF9000F007C117D</key>
		<dict>
			<key>fileRef</key>
			<string>97C146FF1CF9000F007C117D</string>
			<key>isa</key>
			<string>PBXBuildFile</string>
		</dict>
		<key>97C147021CF9000F007C117D</key>
		<dict>
			<key>isa</key>
			<string>PBXFileReference</string>
			<key>lastKnownFileType</key>
			<string>text.plist.xml</string>
			<key>path</key>
			<string>Info.plist</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>97C147031CF9000F007C117D</key>
		<dict>
			<key>buildSettings</key>
			<dict>
				<key>ALWAYS_SEARCH_USER_PATHS</key>
				<string>NO</string>
				<key>CLANG_ANALYZER_NONNULL</key>
				<string>YES</string>
				<key>CLANG_CXX_LANGUAGE_STANDARD</key>
				<string>gnu++0x</string>
				<key>CLANG_CXX_LIBRARY</key>
				<string>libc++</string>
				<key>CLANG_ENABLE_MODULES</key>
				<string>YES</string>
				<key>CLANG_ENABLE_OBJC_ARC</key>
				<string>YES</string>
				<key>CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING</key>
				<string>YES</string>
				<key>CLANG_WARN_BOOL_CONVERSION</key>
				<string>YES</string>
				<key>CLANG_WARN_COMMA</key>
				<string>YES</string>
				<key>CLANG_WARN_CONSTANT_CONVERSION</key>
				<string>YES</string>
				<key>CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS</key>
				<string>YES</string>
				<key>CLANG_WARN_DIRECT_OBJC_ISA_USAGE</key>
				<string>YES_ERROR</string>
				<key>CLANG_WARN_EMPTY_BODY</key>
				<string>YES</string>
				<key>CLANG_WARN_ENUM_CONVERSION</key>
				<string>YES</string>
				<key>CLANG_WARN_INFINITE_RECURSION</key>
				<string>YES</string>
				<key>CLANG_WARN_INT_CONVERSION</key>
				<string>YES</string>
				<key>CLANG_WARN_NON_LITERAL_NULL_CONVERSION</key>
				<string>YES</string>
				<key>CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF</key>
				<string>YES</string>
				<key>CLANG_WARN_OBJC_LITERAL_CONVERSION</key>
				<string>YES</string>
				<key>CLANG_WARN_OBJC_ROOT_CLASS</key>
				<string>YES_ERROR</string>
				<key>CLANG_WARN_RANGE_LOOP_ANALYSIS</key>
				<string>YES</string>
				<key>CLANG_WARN_STRICT_PROTOTYPES</key>
				<string>YES</string>
				<key>CLANG_WARN_SUSPICIOUS_MOVE</key>
				<string>YES</string>
				<key>CLANG_WARN_UNREACHABLE_CODE</key>
				<string>YES</string>
				<key>CLANG_WARN__DUPLICATE_METHOD_MATCH</key>
				<string>YES</string>
				<key>CODE_SIGN_IDENTITY[sdk=iphoneos*]</key>
				<string>iPhone Developer</string>
				<key>COPY_PHASE_STRIP</key>
				<string>NO</string>
				<key>DEBUG_INFORMATION_FORMAT</key>
				<string>dwarf</string>
				<key>ENABLE_STRICT_OBJC_MSGSEND</key>
				<string>YES</string>
				<key>ENABLE_TESTABILITY</key>
				<string>YES</string>
				<key>GCC_C_LANGUAGE_STANDARD</key>
				<string>gnu99</string>
				<key>GCC_DYNAMIC_NO_PIC</key>
				<string>NO</string>
				<key>GCC_NO_COMMON_BLOCKS</key>
				<string>YES</string>
				<key>GCC_OPTIMIZATION_LEVEL</key>
				<string>0</string>
				<key>GCC_PREPROCESSOR_DEFINITIONS</key>
				<array>
					<string>DEBUG=1</string>
					<string>$(inherited)</string>
				</array>
				<key>GCC_WARN_64_TO_32_BIT_CONVERSION</key>
				<string>YES</string>
				<key>GCC_WARN_ABOUT_RETURN_TYPE</key>
				<string>YES_ERROR</string>
				<key>GCC_WARN_UNDECLARED_SELECTOR</key>
				<string>YES</string>
				<key>GCC_WARN_UNINITIALIZED_AUTOS</key>
				<string>YES_AGGRESSIVE</string>
				<key>GCC_WARN_UNUSED_FUNCTION</key>
				<string>YES</string>
				<key>GCC_WARN_UNUSED_VARIABLE</key>
				<string>YES</string>
				<key>IPHONEOS_DEPLOYMENT_TARGET</key>
				<string>9.0</string>
				<key>MTL_ENABLE_DEBUG_INFO</key>
				<string>YES</string>
				<key>ONLY_ACTIVE_ARCH</key>
				<string>YES</string>
				<key>SDKROOT</key>
				<string>iphoneos</string>
				<key>TARGETED_DEVICE_FAMILY</key>
				<string>1,2</string>
			</dict>
			<key>isa</key>
			<string>XCBuildConfiguration</string>
			<key>name</key>
			<string>Debug</string>
		</dict>
		<key>97C147041CF9000F007C117D</key>
		<dict>
			<key>buildSettings</key>
			<dict>
				<key>ALWAYS_SEARCH_USER_PATHS</key>
				<string>NO</string>
				<key>CLANG_ANALYZER_NONNULL</key>
				<string>YES</string>
				<key>CLANG_CXX_LANGUAGE_STANDARD</key>
				<string>gnu++0x</string>
				<key>CLANG_CXX_LIBRARY</key>
				<string>libc++</string>
				<key>CLANG_ENABLE_MODULES</key>
				<string>YES</string>
				<key>CLANG_ENABLE_OBJC_ARC</key>
				<string>YES</string>
				<key>CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING</key>
				<string>YES</string>
				<key>CLANG_WARN_BOOL_CONVERSION</key>
				<string>YES</string>
				<key>CLANG_WARN_COMMA</key>
				<string>YES</string>
				<key>CLANG_WARN_CONSTANT_CONVERSION</key>
				<string>YES</string>
				<key>CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS</key>
				<string>YES</string>
				<key>CLANG_WARN_DIRECT_OBJC_ISA_USAGE</key>
				<string>YES_ERROR</string>
				<key>CLANG_WARN_EMPTY_BODY</key>
				<string>YES</string>
				<key>CLANG_WARN_ENUM_CONVERSION</key>
				<string>YES</string>
				<key>CLANG_WARN_INFINITE_RECURSION</key>
				<string>YES</string>
				<key>CLANG_WARN_INT_CONVERSION</key>
				<string>YES</string>
				<key>CLANG_WARN_NON_LITERAL_NULL_CONVERSION</key>
				<string>YES</string>
				<key>CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF</key>
				<string>YES</string>
				<key>CLANG_WARN_OBJC_LITERAL_CONVERSION</key>
				<string>YES</string>
				<key>CLANG_WARN_OBJC_ROOT_CLASS</key>
				<string>YES_ERROR</string>
				<key>CLANG_WARN_RANGE_LOOP_ANALYSIS</key>
				<string>YES</string>
				<key>CLANG_WARN_STRICT_PROTOTYPES</key>
				<string>YES</string>
				<key>CLANG_WARN_SUSPICIOUS_MOVE</key>
				<string>YES</string>
				<key>CLANG_WARN_UNREACHABLE_CODE</key>
				<string>YES</string>
				<key>CLANG_WARN__DUPLICATE_METHOD_MATCH</key>
				<string>YES</string>
				<key>CODE_SIGN_IDENTITY[sdk=iphoneos*]</key>
				<string>iPhone Developer</string>
				<key>COPY_PHASE_STRIP</key>
				<string>NO</string>
				<key>DEBUG_INFORMATION_FORMAT</key>
				<string>dwarf-with-dsym</string>
				<key>ENABLE_NS_ASSERTIONS</key>
				<string>NO</string>
				<key>ENABLE_STRICT_OBJC_MSGSEND</key>
				<string>YES</string>
				<key>GCC_C_LANGUAGE_STANDARD</key>
				<string>gnu99</string>
				<key>GCC_NO_COMMON_BLOCKS</key>
				<string>YES</string>
				<key>GCC_WARN_64_TO_32_BIT_CONVERSION</key>
				<string>YES</string>
				<key>GCC_WARN_ABOUT_RETURN_TYPE</key>
				<string>YES_ERROR</string>
				<key>GCC_WARN_UNDECLARED_SELECTOR</key>
				<string>YES</string>
				<key>GCC_WARN_UNINITIALIZED_AUTOS</key>
				<string>YES_AGGRESSIVE</string>
				<key>GCC_WARN_UNUSED_FUNCTION</key>
				<string>YES</string>
				<key>GCC_WARN_UNUSED_VARIABLE</key>
				<string>YES</string>
				<key>IPHONEOS_DEPLOYMENT_TARGET</key>
				<string>9.0</string>
				<key>MTL_ENABLE_DEBUG_INFO</key>
				<string>NO</string>
				<key>SDKROOT</key>
				<string>iphoneos</string>
				<key>SUPPORTED_PLATFORMS</key>
				<string>iphoneos</string>
				<key>SWIFT_COMPILATION_MODE</key>
				<string>wholemodule</string>
				<key>SWIFT_OPTIMIZATION_LEVEL</key>
				<string>-O</string>
				<key>TARGETED_DEVICE_FAMILY</key>
				<string>1,2</string>
				<key>VALIDATE_PRODUCT</key>
				<string>YES</string>
			</dict>
			<key>isa</key>
			<string>XCBuildConfiguration</string>
			<key>name</key>
			<string>Release</string>
		</dict>
		<key>97C147051CF9000F007C117D</key>
		<dict>
			<key>buildConfigurations</key>
			<array>
				<string>97C147061CF9000F007C117D</string>
				<string>97C147071CF9000F007C117D</string>
				<string>249021D4217E4FDB00AE95B9</string>
			</array>
			<key>defaultConfigurationIsVisible</key>
			<string>0</string>
			<key>defaultConfigurationName</key>
			<string>Release</string>
			<key>isa</key>
			<string>XCConfigurationList</string>
		</dict>
		<key>97C147061CF9000F007C117D</key>
		<dict>
			<key>baseConfigurationReference</key>
			<string>9740EEB21CF90195004384FC</string>
			<key>buildSettings</key>
			<dict>
				<key>ASSETCATALOG_COMPILER_APPICON_NAME</key>
				<string>AppIcon</string>
				<key>CLANG_ENABLE_MODULES</key>
				<string>YES</string>
				<key>CODE_SIGN_IDENTITY</key>
				<string>Apple Development</string>
				<key>CODE_SIGN_STYLE</key>
				<string>Automatic</string>
				<key>CURRENT_PROJECT_VERSION</key>
				<string>17</string>
				<key>DEVELOPMENT_TEAM</key>
				<string>4QWLW9TKZP</string>
				<key>ENABLE_BITCODE</key>
				<string>NO</string>
				<key>INFOPLIST_FILE</key>
				<string>Runner/Info.plist</string>
				<key>IPHONEOS_DEPLOYMENT_TARGET</key>
				<string>10.0</string>
				<key>LD_RUNPATH_SEARCH_PATHS</key>
				<array>
					<string>$(inherited)</string>
					<string>@executable_path/Frameworks</string>
				</array>
				<key>MARKETING_VERSION</key>
				<string>2.0.0</string>
				<key>PRODUCT_BUNDLE_IDENTIFIER</key>
				<string>com.empregare.app</string>
				<key>PRODUCT_NAME</key>
				<string>$(TARGET_NAME)</string>
				<key>PROVISIONING_PROFILE_SPECIFIER</key>
				<string></string>
				<key>SWIFT_OBJC_BRIDGING_HEADER</key>
				<string>Runner/Runner-Bridging-Header.h</string>
				<key>SWIFT_OPTIMIZATION_LEVEL</key>
				<string>-Onone</string>
				<key>SWIFT_VERSION</key>
				<string>5.0</string>
				<key>VERSIONING_SYSTEM</key>
				<string>apple-generic</string>
			</dict>
			<key>isa</key>
			<string>XCBuildConfiguration</string>
			<key>name</key>
			<string>Debug</string>
		</dict>
		<key>97C147071CF9000F007C117D</key>
		<dict>
			<key>baseConfigurationReference</key>
			<string>7AFA3C8E1D35360C0083082E</string>
			<key>buildSettings</key>
			<dict>
				<key>ASSETCATALOG_COMPILER_APPICON_NAME</key>
				<string>AppIcon</string>
				<key>CLANG_ENABLE_MODULES</key>
				<string>YES</string>
				<key>CODE_SIGN_IDENTITY</key>
				<string>Apple Development</string>
				<key>CODE_SIGN_STYLE</key>
				<string>Automatic</string>
				<key>CURRENT_PROJECT_VERSION</key>
				<string>17</string>
				<key>DEVELOPMENT_TEAM</key>
				<string>4QWLW9TKZP</string>
				<key>ENABLE_BITCODE</key>
				<string>NO</string>
				<key>INFOPLIST_FILE</key>
				<string>Runner/Info.plist</string>
				<key>IPHONEOS_DEPLOYMENT_TARGET</key>
				<string>10.0</string>
				<key>LD_RUNPATH_SEARCH_PATHS</key>
				<array>
					<string>$(inherited)</string>
					<string>@executable_path/Frameworks</string>
				</array>
				<key>MARKETING_VERSION</key>
				<string>2.0.0</string>
				<key>PRODUCT_BUNDLE_IDENTIFIER</key>
				<string>com.empregare.app</string>
				<key>PRODUCT_NAME</key>
				<string>$(TARGET_NAME)</string>
				<key>PROVISIONING_PROFILE_SPECIFIER</key>
				<string></string>
				<key>SWIFT_OBJC_BRIDGING_HEADER</key>
				<string>Runner/Runner-Bridging-Header.h</string>
				<key>SWIFT_VERSION</key>
				<string>5.0</string>
				<key>VERSIONING_SYSTEM</key>
				<string>apple-generic</string>
			</dict>
			<key>isa</key>
			<string>XCBuildConfiguration</string>
			<key>name</key>
			<string>Release</string>
		</dict>
		<key>EA8EBBC026E6856C00588BF3</key>
		<dict>
			<key>isa</key>
			<string>PBXFileReference</string>
			<key>lastKnownFileType</key>
			<string>text.plist.entitlements</string>
			<key>path</key>
			<string>RunnerProfile.entitlements</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>EAF9556626E65FFF00854ABF</key>
		<dict>
			<key>fileEncoding</key>
			<string>4</string>
			<key>isa</key>
			<string>PBXFileReference</string>
			<key>lastKnownFileType</key>
			<string>text.plist.xml</string>
			<key>path</key>
			<string>GoogleService-Info.plist</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>EAF9556726E65FFF00854ABF</key>
		<dict>
			<key>fileRef</key>
			<string>EAF9556626E65FFF00854ABF</string>
			<key>isa</key>
			<string>PBXBuildFile</string>
		</dict>
		<key>EB4CADF7021B680243F63761</key>
		<dict>
			<key>includeInIndex</key>
			<string>1</string>
			<key>isa</key>
			<string>PBXFileReference</string>
			<key>lastKnownFileType</key>
			<string>text.xcconfig</string>
			<key>name</key>
			<string>Pods-Runner.profile.xcconfig</string>
			<key>path</key>
			<string>Target Support Files/Pods-Runner/Pods-Runner.profile.xcconfig</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
	</dict>
	<key>rootObject</key>
	<string>97C146E61CF9000F007C117D</string>
</dict>
</plist>
