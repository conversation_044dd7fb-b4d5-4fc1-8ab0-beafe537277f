import 'dart:async';

import 'package:flutter/material.dart';

import '../../../shared/app_container.dart';
import '../../../shared/core/app_translation.i18n.dart';
import '../../../shared/core/app_utils.dart';

class WidgetPerguntaTempo extends StatefulWidget {
  final int? tempo;
  final Function? onFinished;

  const WidgetPerguntaTempo({super.key, this.tempo, this.onFinished});

  @override
  _WidgetPerguntaTempoState createState() => _WidgetPerguntaTempoState();
}

class _WidgetPerguntaTempoState extends State<WidgetPerguntaTempo> {
  Timer? _timer;
  int _tempo = 0;
  late String _tempoRestante;

  @override
  void initState() {
    super.initState();
    start();
  }

  @override
  void dispose() {
    super.dispose();
    _timer?.cancel();
  }

  void start() {
    _tempo = widget.tempo ?? 0;
    _tempoRestante = formatSeconds(_tempo);

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) async {
      if (_tempo > 0) {
        setState(() {
          _tempo--;
          _tempoRestante = formatSeconds(_tempo);
        });
      } else {
        _timer!.cancel();
        widget.onFinished!();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return AppContainer(
      width: double.infinity,
      color: const Color(0xFFFFF5E8),
      child: Text(
        'TEMPO RESTANTE: %s'.i18n.fill([_tempoRestante]),
        textAlign: TextAlign.center,
        style: const TextStyle(
          color: Color(0xFFFF762E),
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}
