import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:get/get.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:share_plus/share_plus.dart';

import '../../../shared/app_container.dart';
import '../../../shared/core/app_config.dart';
import '../../../shared/core/app_translation.i18n.dart';
import '../../../shared/utils/have_value.dart';
import '../../../shared/widgets/app_bar_default.dart';
import '../../../shared/widgets/app_job_header.dart';
import '../../../shared/widgets/banner_ad_widget.dart';
import '../../confirm_subscription/confirm_subscription_page.dart';
import 'job_detail_controller.dart';
import 'widget/job_detail_shimmer_page.dart';
import 'widget/widget_benefits.dart';
import 'widget/widget_code_box.dart';
import 'widget/widget_description.dart';
import 'widget/widget_related_jobs.dart';
import 'widget/widget_requirements.dart';

class JobDetailPage extends StatefulWidget {
  final int? id;

  const JobDetailPage({super.key, this.id});

  @override
  JobDetailPageState createState() => JobDetailPageState();
}

class JobDetailPageState extends State<JobDetailPage> {
  final _scaffoldKey = GlobalKey<ScaffoldState>();

  final controller = Modular.get<JobDetailController>();

  @override
  Widget build(BuildContext context) {
    controller.getJobDetails(widget.id);

    return Container(
      color: Colors.white,
      child: SafeArea(
        top: false,
        child: Scaffold(
          key: _scaffoldKey,
          backgroundColor: AppConfig.grey,
          appBar: AppBarDefault(
            icon: Icons.close,
            titleText: 'Detalhes da Vaga'.i18n,
            trailing: GetBuilder<JobDetailController>(
                init: controller,
                builder: (_) {
                  if (!controller.showShareButton) return const SizedBox();
                  return InkWell(
                    child: const Icon(Icons.share, color: AppConfig.white),
                    onTap: () async {
                      await Share.share(
                        "${controller.jobDetails?.titulo ?? ''}\n"
                        "${AppConfig.siteUrl}${controller.jobDetails?.url ?? ''}",
                        subject: controller.jobDetails?.titulo ?? '',
                      );
                    },
                  );
                }),
          ),
          body: Column(
            children: <Widget>[
              Expanded(
                child: SingleChildScrollView(
                  child: GetBuilder<JobDetailController>(
                    init: controller,
                    builder: (_) {
                      if (controller.loading) {
                        return const JobDetailShimmerPage();
                      }

                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: <Widget>[
                          AppJobHeader(jobDetails: controller.jobDetails),
                          const SizedBox(height: 5),
                          if (haveValue(controller.inscricaoMensagem))
                            AppContainer(
                              color: const Color(0xFFFFF5E8),
                              height: 70,
                              width: MediaQuery.of(context).size.width,
                              child: Text(
                                controller.inscricaoMensagem ?? '',
                                textAlign: TextAlign.center,
                                style: const TextStyle(
                                  color: Color(0xFFFF762E),
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          const SizedBox(height: 5),
                          BannerAdWidget(
                            adUnitId: AppConfig.getAdmobBannerListagemVagasId(),
                            size: AdSize.fullBanner,
                          ),
                          const SizedBox(height: 5),
                          AppContainer(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 15,
                              vertical: 20,
                            ),
                            child: Description(controller: controller),
                          ),
                          const SizedBox(height: 10),
                          AppContainer(
                            width: double.infinity,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 15,
                              vertical: 20,
                            ),
                            child: Requirements(controller: controller),
                          ),
                          const SizedBox(height: 10),
                          AppContainer(
                            width: double.infinity,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 15,
                              vertical: 20,
                            ),
                            child: Benefits(controller: controller),
                          ),
                          const SizedBox(height: 10),
                          AppContainer(
                            width: double.infinity,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 15,
                              vertical: 20,
                            ),
                            child: WidgetCodeBox(
                              id: controller.jobDetails?.id,
                              dataCadastro: controller.jobDetails?.dataCadastro,
                              dataAtualizacao:
                                  controller.jobDetails?.dataAtualizacao ?? '',
                            ),
                          ),
                          const SizedBox(height: 10),
                          if (controller
                                  .jobDetails?.vagasRelacionadas?.isNotEmpty ??
                              false)
                            AppContainer(
                              width: double.infinity,
                              child: Column(
                                children: [
                                  const Padding(
                                    padding: EdgeInsets.symmetric(
                                      vertical: 15.0,
                                    ),
                                    child: Text(
                                      'Vagas Relacionadas',
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                  const Divider(),
                                  WidgetRelatedJobs(
                                    onUpdate: () {
                                      setState(() {});
                                    },
                                  ),
                                ],
                              ),
                            ),
                          const SizedBox(height: 10),
                          BannerAdWidget(
                            adUnitId: AppConfig.getAdmobBannerDetalhesVagasId(),
                            size: AdSize.fullBanner,
                          ),
                          const SizedBox(height: 10),
                        ],
                      );
                    },
                  ),
                ),
              ),
              GetBuilder<JobDetailController>(
                init: controller,
                builder: (_) {
                  if (!controller.showSubscriptionButton) {
                    return const SizedBox();
                  }
                  return Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withValues(alpha: 0.1),
                          offset: const Offset(0.0, -7.0),
                          blurRadius: 10.0,
                          spreadRadius: 0.0,
                        )
                      ],
                    ),
                    padding:
                        const EdgeInsets.symmetric(vertical: 7, horizontal: 15),
                    child: SizedBox(
                      height: 45,
                      width: double.infinity,
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppConfig.colorPrimary,
                        ),
                        onPressed: () async {
                          var response = await showDialog(
                            context: context,
                            builder: (_) {
                              return Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 30, vertical: 40),
                                child: ConfirmSubscriptionPage(
                                  jobDetails: controller.jobDetails,
                                ),
                              );
                            },
                          );

                          if (response == true) {
                            ScaffoldMessenger.of(context)
                                .showSnackBar(const SnackBar(
                              backgroundColor: AppConfig.green,
                              content: Text(
                                'Sua inscrição foi confirmada para esta vaga.',
                                style: TextStyle(color: Colors.white),
                              ),
                            ));
                            controller.verificarInscricao();
                          }
                        },
                        child: Text(
                          'CANDIDATAR-SE'.i18n,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
