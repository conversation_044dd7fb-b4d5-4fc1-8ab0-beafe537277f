class UploadBlockResponseModel {
  final bool? error;
  final bool? isLastBlock;
  final String? message;
  final String? arquivo;
  final String? size;

  UploadBlockResponseModel({
    this.error,
    this.isLastBlock,
    this.message,
    this.arquivo,
    this.size,
  });

  factory UploadBlockResponseModel.fromJson(Map<String, dynamic> json) {
    return UploadBlockResponseModel(
      error: json['error'] as bool?,
      isLastBlock: json['isLastBlock'] as bool?,
      message: json['message'] as String?,
      arquivo: json['arquivo'] as String?,
      size: json['size'] as String?,
    );
  }
}
