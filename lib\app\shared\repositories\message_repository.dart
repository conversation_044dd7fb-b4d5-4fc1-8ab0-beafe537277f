import 'package:dio/dio.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../core/app_rest.dart';
import '../models/responses/job_sugestoes_response_model.dart';
import '../models/responses/listar_chat_response_model.dart';
import '../models/responses/listar_mensagens_response_model.dart';

class MessageRepository {
  final AppRest _rest = Modular.get();

  Future<JobSugestoesResponseModel> getNaoLidos() async {
    try {
      final response = await _rest.get('/candidato/chat/nao-lidos');
      return JobSugestoesResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return JobSugestoesResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return JobSugestoesResponseModel.fromJson(err.response!.data);
    }
  }

  Future<ChatResponseModel> getMessages() async {
    try {
      final response = await _rest.get(
        '/candidato/chat/listar',
        queryParameters: {'destinatarioID': null},
      );
      return ChatResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return ChatResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return ChatResponseModel.fromJson(err.response!.data);
    }
  }

  Future<JobSugestoesResponseModel> getLido({int? chatID}) async {
    try {
      final response = await _rest.get('/candidato/chat/lido/$chatID');
      return JobSugestoesResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return JobSugestoesResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return JobSugestoesResponseModel.fromJson(err.response!.data);
    }
  }

  Future<MensagemResponseModel> getListarMensagens({
    required String chatID,
  }) async {
    try {
      final response = await _rest.get(
        '/candidato/chat/listar-mensagens',
        queryParameters: {'chatID': chatID},
      );
      return MensagemResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return MensagemResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return MensagemResponseModel.fromJson(err.response!.data);
    }
  }
}
