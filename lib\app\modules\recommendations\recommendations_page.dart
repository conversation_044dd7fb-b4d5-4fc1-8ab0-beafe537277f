import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:share_plus/share_plus.dart';

import '../../shared/core/app_config.dart';
import '../../shared/core/app_translation.i18n.dart';
import '../../shared/core/app_utils.dart';
import '../home/<USER>/widgets/recommendation_job_card.dart';
import 'recommendations_controller.dart';

class RecommendationsPage extends StatefulWidget {
  const RecommendationsPage({super.key});

  @override
  State<RecommendationsPage> createState() => _RecommendationsPageState();
}

class _RecommendationsPageState extends State<RecommendationsPage> {
  final controller = Modular.get<RecommendationsController>();
  final ScrollController _scrollController = ScrollController();
  int? _selectedJobIndex;

  @override
  void initState() {
    super.initState();
    controller.loadRecomendacoes(reload: true);
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent * 0.8 &&
        controller.canLoadMore) {
      controller.loadRecomendacoes();
    }
  }

  Widget _buildBottomActionBar() {
    if (_selectedJobIndex == null) return const SizedBox.shrink();

    final selectedJob = controller.recomendacoes[_selectedJobIndex!];

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            offset: const Offset(0.0, -7.0),
            blurRadius: 10.0,
            spreadRadius: 0.0,
          )
        ],
      ),
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: SizedBox(
              height: 45,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConfig.colorPrimary,
                  foregroundColor: Colors.white,
                ),
                onPressed: () {
                  _showJobDetails(selectedJob);
                },
                child: Text(
                  'CANDIDATAR-SE'.i18n,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          SizedBox(
            height: 45,
            width: 45,
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFF5F5F5),
                foregroundColor: Colors.grey[700],
                padding: EdgeInsets.zero,
              ),
              onPressed: () {
                _shareJob(selectedJob);
              },
              child: const Icon(Icons.share, size: 20),
            ),
          ),
        ],
      ),
    );
  }

  void _shareJob(job) {
    String jobText = '''
🔥 Confira esta vaga incrível!

${job.title}
💼 ${job.company?.name ?? 'Empresa'}
📍 ${job.location ?? 'Localização não informada'}
${job.salaryRange != null ? '💰 ${job.salaryRange}' : ''}

${job.description != null ? _removeHtmlTags(job.description!) : ''}

Candidate-se agora pelo app Empregare! 📲
    '''
        .trim();

    Share.share(jobText);
  }

  void _showJobDetails(job) {
    GestureDetector(
      onTap: () {},
      child: RecommendationJobCard(job: job),
    );
  }

  String _removeHtmlTags(String htmlText) {
    final RegExp htmlTagRegExp = RegExp(r'<[^>]*>');
    return htmlText.replaceAll(htmlTagRegExp, '').trim();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConfig.white,
      appBar: AppBar(
        title: Text('Recomendações'.i18n),
      ),
      // AppBarDefault(
      //   titleText: 'Recomendações de Vagas'.i18n,

      // ),
      body: Observer(
        builder: (_) {
          if (controller.loading && controller.recomendacoes.isEmpty) {
            return const _LoadingWidget();
          }

          if (controller.hasError && controller.recomendacoes.isEmpty) {
            return _ErrorWidget(
              message: controller.errorMessage ?? 'Erro desconhecido',
              onRetry: () => controller.refresh(),
            );
          }

          if (controller.recomendacoes.isEmpty) {
            return const _EmptyWidget();
          }

          return RefreshIndicator(
            onRefresh: controller.refresh,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    '${controller.recomendacoes.length} recomendações encontradas',
                    style: const TextStyle(
                      color: Colors.grey,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Expanded(
                  child: ListView.separated(
                    controller: _scrollController,
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: controller.recomendacoes.length + (controller.loadingMore ? 1 : 0),
                    separatorBuilder: (_, __) => const SizedBox(height: 16),
                    itemBuilder: (context, index) {
                      if (index < controller.recomendacoes.length) {
                        final job = controller.recomendacoes[index];
                        return GestureDetector(
                          onTap: () {
                            setState(() {
                              _selectedJobIndex = index;
                            });
                          },
                          child: Container(
                            width: double.infinity,
                            decoration: BoxDecoration(
                              border: _selectedJobIndex == index
                                  ? Border.all(color: AppConfig.colorPrimary, width: 2)
                                  : null,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: RecommendationJobCard(
                              job: job,
                            ),
                          ),
                        );
                      } else {
                        return const Padding(
                          padding: EdgeInsets.symmetric(vertical: 16),
                          child: Center(
                            child: CircularProgressIndicator(),
                          ),
                        );
                      }
                    },
                  ),
                ),
              ],
            ),
          );
        },
      ),
      bottomNavigationBar: _selectedJobIndex != null ? _buildBottomActionBar() : null,
    );
  }
}

class _LoadingWidget extends StatelessWidget {
  const _LoadingWidget();

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: 6,
      separatorBuilder: (_, __) => const SizedBox(height: 16),
      itemBuilder: (_, __) => const ShimmerJobPageItem(),
    );
  }
}

class _ErrorWidget extends StatelessWidget {
  final String message;
  final VoidCallback onRetry;

  const _ErrorWidget({
    required this.message,
    required this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              message,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: onRetry,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppConfig.colorPrimary,
                foregroundColor: Colors.white,
              ),
              child: const Text('Tentar novamente'),
            ),
          ],
        ),
      ),
    );
  }
}

class _EmptyWidget extends StatelessWidget {
  const _EmptyWidget();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.work_outline,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'Nenhuma recomendação encontrada',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Complete seu perfil para receber recomendações personalizadas',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
