import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../shared/core/app_config.dart';
import '../../shared/core/app_translation.i18n.dart';
import '../../shared/core/app_utils.dart';
import '../../shared/models/job_parameter_model.dart';
import '../../shared/widgets/app_usuario_card/app_usuario_card.dart';
import '../curriculum/curriculum_module.dart';
import '../jobs/job_detail/widget/app_job_card.dart';
import '../notification/notification_module.dart';
import 'tab_panel_controller.dart';

class TabPanelPage extends StatefulWidget {
  final TabController tabController;

  const TabPanelPage({super.key, required this.tabController});

  @override
  _TabPanelPageState createState() => _TabPanelPageState();
}

class _TabPanelPageState extends State<TabPanelPage> {
  final controller = Modular.get<TabPanelController>();
  JobParameterModel filtros = JobParameterModel();

  @override
  void initState() {
    super.initState();
    controller.load();
    controller.loadJobs(filtros);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Image.asset('lib/assets/images/logo.png', scale: 5),
        backgroundColor: AppConfig.white,
        actions: <Widget>[
          InkWell(
            onTap: () => Modular.to.pushNamed(NotificationModule.route),
            child: Stack(
              children: <Widget>[
                IconButton(
                  icon: const Icon(Icons.notifications_none),
                  onPressed: () {
                    Modular.to.pushNamed(NotificationModule.route);
                  },
                ),
                Observer(
                  builder: (context) {
                    if (controller.countNotificacao > 0) {
                      return Positioned(
                        right: 10,
                        top: 10,
                        child: Container(
                          height: 18,
                          width: 18,
                          padding: const EdgeInsets.all(2),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(50),
                          ),
                          child: Center(
                            child: Text(
                              '${controller.countNotificacao}',
                              textAlign: TextAlign.center,
                              style: const TextStyle(
                                fontSize: 10,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      );
                    } else {
                      return const SizedBox.shrink();
                    }
                  },
                )
              ],
            ),
          ),
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => widget.tabController.animateTo(1),
          )
        ],
      ),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: <Widget>[
          Container(
            color: AppConfig.grey,
            child: Observer(
              builder: (context) {
                return AppUsuarioCard(
                  key: GlobalKey(),
                  onEdit: () {
                    Modular.to.pushNamed(CurriculumModule.route);
                  },
                );
              },
            ),
          ),
          Container(
            color: AppConfig.grey,
            padding: const EdgeInsets.all(15),
            alignment: Alignment.bottomLeft,
            child: Text(
              'Sugestões de Vagas'.i18n,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const SizedBox(height: 10),
          Expanded(
            child: Observer(
              builder: (_) {
                return ListView.separated(
                  itemBuilder: (_, i) {
                    if (i < controller.jobs.length) {
                      return AppJobCard(job: controller.jobs[i]);
                    } else if (i > 1 &&
                        filtros.pag < controller.totalPaginas!) {
                      if (!controller.loading) {
                        filtros.nextPage();
                        WidgetsBinding.instance.addPostFrameCallback(
                          (_) => controller.loadJobs(
                            filtros,
                          ),
                        );
                      }
                      return const ShimmerJobPageItem();
                    }
                    return const SizedBox.shrink();
                  },
                  separatorBuilder: (_, i) => const Divider(height: 2),
                  itemCount: controller.jobs.length + 1,
                );
              },
            ),
          )
        ],
      ),
    );
  }
}
