import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:get/get.dart';

import '../../../../shared/core/app_translation.i18n.dart';
import '../../../../shared/helpers/snack_bar_helper.dart';
import '../../../../shared/models/responses/listar_idiomas_response_model.dart';
import '../../curriculum_controller.dart';
import '../../curriculum_editing/curriculum_editing_module.dart';
import '../../widgets/add_button.dart';
import '../../widgets/curriculum_default_field.dart';
import '../../widgets/dialog_confirm_delete.dart';
import '../secao/secao_widget.dart';

class IdiomasWidget extends StatefulWidget {
  const IdiomasWidget({super.key});

  @override
  _IdiomasWidgetState createState() => _IdiomasWidgetState();
}

class _IdiomasWidgetState extends State<IdiomasWidget> {
  final CurriculumController controller = Modular.get();

  void onAddOrEdit(IdiomaModel toSave) async {
    controller.languages.idiomaToSave = toSave;
    var result = await Modular.to.pushNamed(CurriculumEditingModule.route,
        arguments: CurriculumEditingModule.idiomas);

    if (result == true) {
      SnackbarHelper.showSnackbarSucesso(
        context,
        'Idioma salvo com sucesso',
      );
      // Não precisa chamar loadIdiomas() aqui, já é feito automaticamente no saveIdioma()
    }
  }

  void onRemove(IdiomaModel toRemove) async {
    bool? confirmDelete = await showDialog(
      context: context,
      builder: (_) => const ConfirmDeleteDialog(secao: 'Idioma'),
    );

    if (confirmDelete == true) {
      var result = await controller.languages.deleteIdioma(toRemove.id);
      if (result == true) {
        SnackbarHelper.showSnackbarSucesso(
          context,
          'Idioma removido com sucesso',
        );
        // Não precisa chamar loadIdiomas() aqui, já é feito automaticamente no deleteIdioma()
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CurriculumController>(
      init: controller,
      builder: (_) => SecaoWidget(
        header: 'Idiomas'.i18n,
        showEdit: false,
        hasContent: controller.languages.idiomas?.isNotEmpty ?? false,
        content: ColoredBox(
          color: Colors.white,
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 24,
              vertical: 15,
            ),
            child: Column(
              children: [
                ...(controller.languages.idiomas ?? []).map(
                  (i) => Column(
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Row(
                            children: <Widget>[
                              Expanded(
                                child: Text(
                                  i.nome!,
                                  style: const TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                              Material(
                                child: InkWell(
                                  onTap: () => onAddOrEdit(i.copy()),
                                  child: Row(
                                    children: [
                                      Image.asset(
                                        'lib/assets/icons/pencil-square.png',
                                        scale: 1.75,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              const SizedBox(width: 25),
                              Material(
                                child: InkWell(
                                  onTap: () => onRemove(i),
                                  child: const Icon(
                                    Icons.delete,
                                    color: Colors.red,
                                    size: 24,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          Text(
                            '${i.nivelString}',
                            style: const TextStyle(
                              fontSize: 12,
                            ),
                          ),
                          Text(
                            i.descricao ?? '',
                            style: const TextStyle(
                              color: Colors.grey,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                      const Divider()
                    ],
                  ),
                ),
                AddButton(
                  onTap: () => onAddOrEdit(IdiomaModel()),
                  btnTitle: 'Adicionar idioma',
                )
              ],
            ),
          ),
        ),
        noContent: CurriculumDefaultField(
          bottomAdd: () => onAddOrEdit(IdiomaModel()),
          textBottom: 'Adicionar idioma',
          description: 'Inclua seu nível de fluência e formação.',
        ),
      ),
    );
  }
}
