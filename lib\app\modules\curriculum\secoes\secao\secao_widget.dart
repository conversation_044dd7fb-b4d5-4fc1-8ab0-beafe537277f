import 'package:flutter/material.dart';

class SecaoWidget extends StatefulWidget {
  final String header;
  final Widget? content;
  final Widget? noContent;
  final bool hasContent;
  final bool? showEdit;
  final Function? onEdit;
  final bool replaceEditWithAdd;

  const SecaoWidget({
    super.key,
    required this.header,
    this.content,
    this.noContent,
    this.hasContent = false,
    this.showEdit,
    this.onEdit,
    this.replaceEditWithAdd = false,
  });

  @override
  _SecaoWidgetState createState() => _SecaoWidgetState();
}

class _SecaoWidgetState extends State<SecaoWidget> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ColoredBox(
          color: const Color(0xFFf5f8fa),
          child: Padding(
            padding: const EdgeInsets.only(
              top: 16.0,
              bottom: 10.0,
              left: 24,
              right: 24,
            ),
            child: Row(
              children: [
                Text(
                  widget.header,
                  style: const TextStyle(
                    fontFamily: 'Inter',
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Spacer(),
                if (widget.replaceEditWithAdd)
                  InkWell(
                    onTap: widget.onEdit as void Function()?,
                    child: Row(
                      children: [
                        Image.asset(
                          'lib/assets/icons/pencil-square.png',
                          scale: 1.75,
                        ),
                        const SizedBox(width: 8),
                        const Text(
                          'Editar',
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ),
        widget.hasContent ? widget.content! : widget.noContent ?? Container(),
      ],
    );
  }
}
