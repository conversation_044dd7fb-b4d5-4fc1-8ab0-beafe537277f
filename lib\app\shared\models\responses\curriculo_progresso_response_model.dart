class CurriculoProgressoResponseModel {
  final bool sucesso;
  final String mensagem;
  final CurriculoProgressoModel? dados;

  CurriculoProgressoResponseModel({
    this.sucesso = false,
    this.dados,
    this.mensagem = "",
  });

  factory CurriculoProgressoResponseModel.fromJson(Map<String, dynamic> json) {
    return CurriculoProgressoResponseModel(
      sucesso: json['sucesso'] ?? false,
      mensagem: json['mensagem'] ?? "",
      dados: json['dados'] == null
          ? null
          : CurriculoProgressoModel.fromJson(
              json['dados'] as Map<String, dynamic>,
            ),
    );
  }
}

class CurriculoProgressoModel {
  final bool? cep;
  final bool? logradouro;
  final bool? cidade;
  final bool? foto;
  final bool? redeSocial;
  final bool? objetivo;
  final bool? miniCurriculo;
  final bool? formacao;
  final bool? experiencia;
  final bool? curso;
  final bool? idioma;
  final bool? informatica;
  final bool? semExperiencia;
  final bool? semInformatica;
  final String? dataCadastro;
  final String? dataAtualizacao;
  final bool? barraPorcentagem;
  final double? porcentagem;

  CurriculoProgressoModel({
    this.cep,
    this.logradouro,
    this.cidade,
    this.foto,
    this.redeSocial,
    this.objetivo,
    this.miniCurriculo,
    this.formacao,
    this.experiencia,
    this.curso,
    this.idioma,
    this.informatica,
    this.semExperiencia,
    this.semInformatica,
    this.dataCadastro,
    this.dataAtualizacao,
    this.barraPorcentagem,
    this.porcentagem,
  });

  factory CurriculoProgressoModel.fromJson(Map<String, dynamic> json) {
    return CurriculoProgressoModel(
      cep: json['cep'] as bool?,
      logradouro: json['logradouro'] as bool?,
      cidade: json['cidade'] as bool?,
      foto: json['foto'] as bool?,
      redeSocial: json['redeSocial'] as bool?,
      objetivo: json['objetivo'] as bool?,
      miniCurriculo: json['miniCurriculo'] as bool?,
      formacao: json['formacao'] as bool?,
      experiencia: json['experiencia'] as bool?,
      curso: json['curso'] as bool?,
      idioma: json['idioma'] as bool?,
      informatica: json['informatica'] as bool?,
      semExperiencia: json['semExperiencia'] as bool?,
      semInformatica: json['semInformatica'] as bool?,
      dataCadastro: json['dataCadastro'] as String?,
      dataAtualizacao: json['dataAtualizacao'] as String?,
      barraPorcentagem: json['barraPorcentagem'] as bool?,
      porcentagem: (json['porcentagem'] as num?)?.toDouble(),
    );
  }
}
