import 'package:flutter/foundation.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:mobx/mobx.dart';

import '../../../shared/models/job_insight_model.dart';
import '../../../shared/models/responses/cancelar_inscricao_response_model.dart';
import '../../../shared/models/responses/job_details_response_model.dart';
import '../../../shared/models/responses/verificar_inscricao_response_model.dart';
import '../../../shared/repositories/inscription_repository.dart';
import '../../../shared/repositories/job_repository.dart';
import '../../auth/login/login_module.dart';

part 'job_detail_controller.g.dart';

class JobDetailController = _JobDetailControllerBase with _$JobDetailController;

abstract class _JobDetailControllerBase with Store {
  final JobRepository _jobRepository = Modular.get();
  final InscriptionRepository _repository = Modular.get();

  @observable
  bool loading = false;

  @action
  void setLoading(bool value) => loading = value;
  JobDetailsResponseModel? get jobDetails => jobs[jobs.keys.lastOrNull];

  String getJobDescription() {
    return jobDetails?.descricao ?? '';
  }

  @observable
  ObservableMap<int, JobDetailsResponseModel> jobs = ObservableMap.of({});

  VerificarInscricaoResponseModel? get subscription {
    return subscriptionsJobs[subscriptionsJobs.keys.lastOrNull];
  }

  @observable
  ObservableMap<int, VerificarInscricaoResponseModel> subscriptionsJobs =
      ObservableMap.of({});

  @action
  void removeJob(int? jobId) {
    jobId ??= jobs.keys.lastOrNull;
    if (jobId == null) return;

    jobs.remove(jobId);
    subscriptionsJobs.remove(jobId);
  }

  bool get showSubscriptionButton {
    switch (subscription?.retorno) {
      case 'VagaEncerrada':
      case 'CurriculoNaoPermitido':
      case 'CurriculoNaoExiste':
      case 'LimiteUltrapassado':
      case 'SomenteColaboradores':
      case 'SomenteCandidatos':
      case 'DeficienciaObrigatoria':
        return false;
      case 'NaoLogado':
      case 'CurriculoIncompleto':
        return false;
      case 'Inscrito':
        return false;
      case 'Liberado':
      case 'Cancelado':
        return true;
      default:
        return true;
    }
  }

  bool get isSubscriptionButton => subscription?.retorno == "Inscrito";

  bool? get isSuccess => subscription?.sucesso;

  bool get showShareButton => subscription?.retorno != 'VagaEncerrada';

  String? get inscricaoMensagem {
    switch (subscription?.retorno) {
      case 'VagaEncerrada':
        return "A vaga foi encerrada!";
      case 'CurriculoNaoPermitido':
        return "Currículo não permitido!";
      case 'CurriculoNaoExiste':
        return "Currículo não existe!";
      case 'LimiteUltrapassado':
        return "Limite ultrapassado!";
      case 'SomenteColaboradores':
        return "Somente Colaboradores";
      case 'SomenteCandidatos':
        return "Somente Candidatos";
      case 'DeficienciaObrigatoria':
        return subscription?.retornoMensagem;
      case 'NaoLogado':
        Modular.to.pushNamedAndRemoveUntil(
          LoginModule.routeName,
          (_) => false,
        );
        break;
      case 'CurriculoIncompleto':
        return 'Currículo Incompleto. Bloqueamos sua inscrição na vaga porque seu currículo está incompleto. Efetue as mudanças solicitadas para se inscrever nas vagas de nosso site.';

      case 'Inscrito':
        return 'Sua inscrição está confirmada nessa vaga';
      case 'Liberado':
        return "Liberado!";
      case 'Cancelado':
        return 'Cancelado!';
    }

    return null;
  }

  @observable
  bool _mostrarMaisCidades = false;
  bool get mostrarMaisCidades => _mostrarMaisCidades;

  bool get isPageJob => jobs.isNotEmpty;

  @observable
  double _headerOffset = 0;
  double get headerOffset => _headerOffset;

  @action
  changeOffset(double value) => _headerOffset = value;

  int? get candidaturaId {
    return subscriptionsJobs[subscriptionsJobs.keys.lastOrNull]?.id;
  }

  @action
  setMostrarMaisCidades(bool value) => _mostrarMaisCidades = value;

  @action
  Future getJobDetails(int? id) async {
    if (id == null) return;
    try {
      loading = true;
      var response = await _jobRepository.getJobDetails(id);

      jobs.addAll({id: response});

      try {
        await _jobRepository.postJobInsight(
          insight: JobInsightModel(vagaID: response.id),
        );
      } catch (e) {
        debugPrint('Erro ao enviar insight: $e');
      }

      await verificarInscricao();
    } finally {
      loading = false;
    }
  }

  @action
  Future verificarInscricao([int? jobId]) async {
    int? id = jobId ?? jobDetails?.id;
    final responseModel = await _jobRepository.getVerificarInscricao(id);
    if (id != null) {
      subscriptionsJobs.addAll({id: responseModel});
    }
  }

  @observable
  ObserverList<int> _cidadesInteresse = ObserverList<int>();

  List<int> get cidadesInteresse => _cidadesInteresse.toList();

  @action
  void addCidadeInteresse(int cidadeId) => _cidadesInteresse.add(cidadeId);

  @action
  void removeCidadeInteresse(int cidadeId) =>
      _cidadesInteresse.remove(cidadeId);

  @action
  Future<VerificarInscricaoResponseModel> confirmSubscription() async {
    setLoading(true);
    final response = await _jobRepository.postJobConfirmarInscricao(
      jobDetails?.id,
      cidadesInteresse,
    );
    await verificarInscricao();
    setLoading(false);

    return response;
  }

  @action
  Future<CancelarInscricaoResponseModel> cancelar() async {
    setLoading(true);

    final response = await _repository.getCancelarInscricao(candidaturaId);
    await verificarInscricao();

    setLoading(false);

    return response;
  }
}
