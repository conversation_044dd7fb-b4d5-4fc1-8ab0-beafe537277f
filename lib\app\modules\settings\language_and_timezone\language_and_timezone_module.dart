import 'package:flutter_modular/flutter_modular.dart';

import 'language_and_timezone_controller.dart';
import 'language_and_timezone_page.dart';

class LanguageAndTimezoneModule extends Module {
  static const route = '/language-and-timezone';

  @override
  void binds(i) {
    i.add<PERSON><PERSON>y<PERSON><PERSON>(LanguageAndTimezoneController.new);
  }

  @override
  void routes(r) {
    r.child(
      '/',
      child: (context) => const LanguageAndTimezonePage(),
      transition: TransitionType.noTransition,
    );
  }
}
