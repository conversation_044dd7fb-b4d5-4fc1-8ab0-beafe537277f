import 'package:flutter/material.dart';

import '../../../shared/core/app_config.dart';

class AddButton extends StatelessWidget {
  final Function onTap;
  final String? btnTitle;

  const AddButton({
    super.key,
    required this.onTap,
    this.btnTitle,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap as void Function()?,
      child: DecoratedBox(
        decoration: BoxDecoration(
          border: Border.all(
            color: AppConfig.colorPrimary,
          ),
          borderRadius: const BorderRadius.all(
            Radius.circular(6),
          ),
          color: AppConfig.colorPrimary,
        ),
        child: Padding(
          padding: const EdgeInsets.all(14),
          child: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.add,
                  color: Colors.white,
                  size: 18,
                ),
                const SizedBox(
                  width: 5,
                ),
                Text(
                  btnTitle ?? 'Adicionar',
                  style: const TextStyle(
                    color: AppConfig.white,
                    fontFamily: 'Inter',
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
