import 'package:empregare_app/app/shared/mixins/loader_mixin.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:get/get.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../../shared/models/porte_empresa_model.dart';
import '../../../shared/models/responses/listar_experiencias_response_model.dart';
import '../../../shared/models/responses/simple_response_model.dart';
import '../../../shared/models/salvar_experiencia_model.dart';
import '../../../shared/repositories/curriculum_repository.dart';
import '../curriculum_controller.dart';

class ExperienceController extends GetxController with LoaderMixin {
  final CurriculumRepository _repository = Modular.get();

  SalvarExperienciaModel? experienciaToSave;

  bool loading = false;

  Future load() async {
    try {
      changeLoading(true);
      await Future.wait([
        loadExperiencias(),
      ]);
      experienciaToSave ??= SalvarExperienciaModel(
        atual: false,
        voluntario: false,
      );
    } finally {
      changeLoading(false);
    }
  }

  final portesEmpresa = <PorteEmpresaModel>[
    PorteEmpresaModel(id: 1, descricao: 'Atividade Autônoma'.i18n),
    PorteEmpresaModel(id: 2, descricao: 'Microempresa'.i18n),
    PorteEmpresaModel(id: 3, descricao: 'Empresa de pequeno porte'.i18n),
    PorteEmpresaModel(id: 4, descricao: 'Empresa de médio porte'.i18n),
    PorteEmpresaModel(id: 5, descricao: 'Empresa de grande porte'.i18n),
  ];
  Future loadExperiencias() async {
    final response = await _repository.getExperiencias();
    experiencias = response.dados;
    update();
  }

  List<ExperienciaModel>? experiencias = <ExperienciaModel>[];

  Future<void> reloadFromCurriculumController() async {
    try {
      final curriculumController = Modular.get<CurriculumController>();
      await curriculumController.reloadSection('experiencias');
    } catch (e) {
      await loadExperiencias();
    }
  }

  Future<SimpleResponseModel> saveExperiencia({bool remover = false}) async {
    SimpleResponseModel response;
    try {
      changeLoading(true);
      response = (await _repository.saveExperiencia(
        remover ? null : experienciaToSave,
        remover: remover,
      ));
      if (response.sucesso) {
        await reloadFromCurriculumController();
      }
    } finally {
      loading = false;
      changeLoading(false);
    }
    return response;
  }

  Future<SimpleResponseModel> deleteExperiencia(int? id) async {
    SimpleResponseModel responseModel;
    changeLoading(true);
    try {
      responseModel = (await _repository.deleteExperiencia(id));
      if (responseModel.sucesso) {
        await reloadFromCurriculumController();
      }
    } finally {
      changeLoading(false);
    }
    return responseModel;
  }
}
