import 'package:flutter/material.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../../../shared/models/responses/job_sugestoes_response_model.dart';

class FacetBuilder extends StatefulWidget {
  final String titulo;
  final Widget? child;
  final int itemsPerList;
  final List<FacetModel> facet;

  const FacetBuilder({
    super.key,
    required this.titulo,
    this.child,
    this.itemsPerList = 3,
    required this.facet,
  });

  @override
  State<FacetBuilder> createState() => _FacetBuilderState();
}

class _FacetBuilderState extends State<FacetBuilder> {
  List<List<FacetModel>> get facetSeparated {
    List<List<FacetModel>> sublistas = [];
    int i = 0;

    while (i < showTotal) {
      int fim = i + widget.itemsPerList;
      if (fim > showTotal) {
        fim = showTotal;
      }
      sublistas.add(widget.facet.sublist(i, fim));
      i = fim;
    }

    return sublistas;
  }

  @override
  void initState() {
    super.initState();

    incrementShowTotal();
  }

  int showTotal = 0;
  void incrementShowTotal() {
    showTotal += 10;

    if (showTotal > widget.facet.length) {
      showTotal = widget.facet.length;
    } else if (widget.facet.length < 10) {
      showTotal = widget.facet.length;
    }

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
      child: widget.child ??
          Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Text(
                widget.titulo.i18n,
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Column(
                children: List.generate(
                  facetSeparated.length,
                  (i) {
                    return SizedBox(
                      width: MediaQuery.of(context).size.width,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: List.generate(
                          facetSeparated[i].length,
                          (sub) {
                            var item = facetSeparated[i][sub];

                            return GestureDetector(
                              child: Row(
                                children: [
                                  SizedBox(
                                    width: 35,
                                    height: 35,
                                    child: Checkbox(
                                      side: const BorderSide(),
                                      shape: BeveledRectangleBorder(
                                        borderRadius: BorderRadius.circular(1),
                                      ),
                                      value: item.isSelected,
                                      onChanged: (value) {
                                        setState(() {
                                          item.setSelected(value);
                                        });
                                      },
                                    ),
                                  ),
                                  SizedBox(
                                    width: MediaQuery.of(context).size.width /
                                            facetSeparated[i].length -
                                        MediaQuery.of(context).size.width *
                                            0.19,
                                    child: Text(
                                      item.nome,
                                      overflow: TextOverflow.ellipsis,
                                      maxLines: 2,
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ),
                    );
                  },
                ),
              ),
              if (widget.facet.length > showTotal)
                Row(
                  children: [
                    TextButton(
                      onPressed: incrementShowTotal,
                      child: const Text(
                        "Ver mais",
                        style: TextStyle(
                          fontFamily: 'Inter',
                        ),
                      ),
                    ),
                  ],
                ),
            ],
          ),
    );
  }
}
