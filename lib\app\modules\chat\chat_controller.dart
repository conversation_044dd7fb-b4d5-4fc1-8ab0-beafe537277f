import 'package:get/get.dart';

import '../../shared/mixins/loader_mixin.dart';
import '../../shared/models/responses/listar_mensagens_response_model.dart';

class ChatController extends GetxController with LoaderMixin {
  bool _isNotFound = false;
  bool get isNotFound => _isNotFound;

  void setIsNotFound(bool value) {
    _isNotFound = value;
    update();
  }

  List<MensagemModel> _mensagens = <MensagemModel>[];

  List<MensagemModel> get mensagens => _mensagens;

  void setMensagens(List<MensagemModel> value) {
    _mensagens = value;
    update();
  }

  // Método para compatibilidade com código existente
  void setLoading(bool value) => changeLoading(value);
}
