import 'package:mobx/mobx.dart';

import '../../shared/models/responses/listar_mensagens_response_model.dart';

part 'chat_controller.g.dart';

class ChatController = _ChatBase with _$ChatController;

abstract class _ChatBase with Store {
  @observable
  bool isNotFound = false;
  
  @action
  void setIsNotFound(bool value) => isNotFound = value;

  @observable
  bool loading = false;

  @action
  setLoading(bool value) => loading = value;

  @observable
  List<MensagemModel> _mensagens = <MensagemModel>[].asObservable();

  List<MensagemModel> get mensagens => _mensagens;
}
