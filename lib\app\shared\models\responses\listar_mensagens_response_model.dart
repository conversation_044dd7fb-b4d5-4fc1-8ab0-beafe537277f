class MensagemResponseModel {
  final bool sucesso;
  final String mensagem;
  final List<MensagemModel>? mensagens;

  MensagemResponseModel(
      {this.sucesso = false, this.mensagem = "", this.mensagens});

  factory MensagemResponseModel.fromJson(Map<String, dynamic> json) {
    return MensagemResponseModel(
      sucesso: json['sucesso'] ?? false,
      mensagem: json['mensagem'] ?? "",
      mensagens: (json['mensagens'] as List<dynamic>?)
          ?.map((e) => MensagemModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class MensagemModel {
  final String? chat;
  final int? id;
  final String? mensagem;
  final String? data;
  final bool? sistema;
  final String? foto;
  final bool? enviada;
  final String? nome;

  MensagemModel({
    this.chat,
    this.id,
    this.mensagem,
    this.data,
    this.sistema,
    this.foto,
    this.enviada,
    this.nome,
  });

  factory MensagemModel.fromJson(Map<String, dynamic> json) {
    return MensagemModel(
      chat: json['chat'] as String?,
      id: json['id'] as int?,
      mensagem: json['mensagem'] as String?,
      data: json['data'] as String?,
      sistema: json['sistema'] as bool?,
      foto: json['foto'] as String?,
      enviada: json['enviada'] as bool?,
      nome: json['nome'] as String?,
    );
  }
}
