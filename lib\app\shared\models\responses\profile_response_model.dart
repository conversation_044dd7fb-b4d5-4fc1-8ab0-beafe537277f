class ProfileResponseModel {
  final bool sucesso;
  final String mensagem;

  final String? foto;
  final String? nome;
  final int? progresso;

  ProfileResponseModel({
    this.sucesso = false,
    this.foto,
    this.nome,
    this.progresso,
    this.mensagem = "",
  });

  factory ProfileResponseModel.fromJson(Map<String, dynamic> json) {
    return ProfileResponseModel(
      sucesso: json['sucesso'] ?? false,
      mensagem: json['mensagem'] ?? "",
      foto: json['foto'] as String?,
      nome: json['nome'] as String?,
      progresso: json['progresso'] as int?,
    );
  }
}
