import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:timezone/timezone.dart' as tz;

import '../../../shared/core/app_config.dart';
import '../../../shared/widgets/app_default_button.dart';
import 'language_and_timezone_controller.dart';

class LanguageAndTimezonePage extends StatefulWidget {
  const LanguageAndTimezonePage({super.key});

  @override
  State<LanguageAndTimezonePage> createState() =>
      _LanguageAndTimezonePageState();
}

class _LanguageAndTimezonePageState extends State<LanguageAndTimezonePage> {
  @override
  void initState() {
    super.initState();

    tz.initializeTimeZones();
    Modular.get<LanguageAndTimezoneController>().fetch();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Idioma e fuso horário',
          style: TextStyle(color: Colors.black),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: ListView(
          physics: const BouncingScrollPhysics(),
          children: [
            const Text(
              'Idioma',
              style: TextStyle(
                fontSize: 14,
                fontFamily: 'Inter',
                fontWeight: FontWeight.w400,
                color: Color(0xff161519),
              ),
            ),
            const SizedBox(height: 10),
            DropdownButtonFormField(
              value: "Português",
              items: ["Português"].map((lang) {
                return DropdownMenuItem(
                  value: lang,
                  child: Text(lang),
                );
              }).toList(),
              onChanged: (value) {},
              decoration: InputDecoration(
                hintStyle: const TextStyle(
                  fontSize: 14,
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w400,
                  color: Colors.black,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide:
                      const BorderSide(color: Color(0xffeeeff3), width: 1.8),
                ),
                disabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide.none,
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(
                    color: AppConfig.colorPrimary,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 20),
            const Text(
              'Fuso horário',
              style: TextStyle(
                fontSize: 14,
                fontFamily: 'Inter',
                color: Color(0xff161519),
                fontWeight: FontWeight.w400,
              ),
            ),
            const SizedBox(height: 10),
            Observer(
              builder: (context) {
                var control = Modular.get<LanguageAndTimezoneController>();

                return DropdownButtonFormField(
                  value: control.selectedTimezone,
                  items: tz.timeZoneDatabase.locations.keys.map((loc) {
                    return DropdownMenuItem(value: loc, child: Text(loc));
                  }).toList(),
                  onChanged: control.changeSelectedTimezone,
                  decoration: InputDecoration(
                    hintStyle: const TextStyle(
                      fontSize: 14,
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w400,
                      color: Colors.black,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(
                        color: Color(0xffeeeff3),
                        width: 1.8,
                      ),
                    ),
                    disabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide.none,
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(
                        color: AppConfig.colorPrimary,
                      ),
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 20),
            AppDefaultButton(
              title: const Text("Salvar"),
              onPressed: () async {
                await Modular.get<LanguageAndTimezoneController>().save();

                // ignore: use_build_context_synchronously
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    backgroundColor: AppConfig.green,
                    content: Text("Alterações salvas"),
                  ),
                );

                Modular.to.pop();
              },
            ),
          ],
        ),
      ),
    );
  }
}
