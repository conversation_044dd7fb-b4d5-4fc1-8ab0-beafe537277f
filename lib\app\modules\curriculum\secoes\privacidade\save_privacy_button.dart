import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';

import '../../../../shared/core/app_config.dart';
import '../../../home/<USER>/tab_profile_controller.dart';
import '../../curriculum_controller.dart';

class SavePrivacyButton extends StatelessWidget {
  const SavePrivacyButton({
    super.key,
    required this.controller,
    required this.controllerTabProfile,
  });

  final CurriculumController? controller;
  final TabProfileController controllerTabProfile;

  @override
  Widget build(BuildContext context) {
    return Observer(
      builder: (_) => Padding(
        padding: const EdgeInsets.symmetric(
          vertical: 25,
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Flexible(
              flex: 2,
              child: InkWell(
                onTap: () => Navigator.of(context).pop(),
                child: Container(
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: AppConfig.colorPrimary,
                      width: 1.8,
                    ),
                    borderRadius: const BorderRadius.all(
                      Radius.circular(6),
                    ),
                    color: Colors.white,
                  ),
                  child: const Padding(
                    padding: EdgeInsets.only(
                      top: 16.0,
                      bottom: 16,
                    ),
                    child: Center(
                      child: Text(
                        'Cancelar',
                        style: TextStyle(
                          color: AppConfig.colorPrimary,
                          fontFamily: 'Inter',
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 10),
            Flexible(
              flex: 3,
              child: InkWell(
                onTap: () async {
                  if (controller != null) {
                    await controller?.salvarPrivacidade(
                      controller!.privacy,
                    );
                    // await Future.delayed(Duration(milliseconds: 500));
                    Navigator.of(context).pop();
                  } else {
                    controllerTabProfile.salvarPrivacidade(
                      controllerTabProfile.privacy,
                    );
                  }

                  await controllerTabProfile.getPrivacidade();
                },
                child: Container(
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: AppConfig.colorPrimary,
                    ),
                    borderRadius: const BorderRadius.all(
                      Radius.circular(6),
                    ),
                    color: AppConfig.colorPrimary,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.only(
                      top: 16.0,
                      bottom: 16,
                    ),
                    child: Center(
                      child: Visibility(
                        visible: (controller?.salvandoPrivacidade ??
                                controllerTabProfile.salvandoPrivacidade) ==
                            false,
                        replacement: const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            color: AppConfig.colorPrimary,
                            backgroundColor: AppConfig.grey,
                          ),
                        ),
                        child: const Text(
                          'Confirmar',
                          style: TextStyle(
                            color: AppConfig.white,
                            fontFamily: 'Inter',
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
