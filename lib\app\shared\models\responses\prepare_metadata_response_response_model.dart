class PrepareMetadataResponseModel {
  final bool sucesso;
  final String mensagem;
  final String? arquivo;

  PrepareMetadataResponseModel(
      {this.sucesso = false, this.mensagem = "", this.arquivo});

  factory PrepareMetadataResponseModel.fromJson(Map<String, dynamic> json) {
    return PrepareMetadataResponseModel(
      sucesso: json['sucesso'] ?? false,
      mensagem: json['mensagem'] ?? "",
      arquivo: json['arquivo'] as String?,
    );
  }
}
