import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../shared/app_container.dart';
import '../../shared/core/app_config.dart';
import '../../shared/core/app_translation.i18n.dart';
import '../../shared/models/responses/job_details_response_model.dart';
import '../../shared/widgets/app_bar_default.dart';
import '../../shared/widgets/app_default_button.dart';
import '../../shared/widgets/app_job_header.dart';
import '../jobs/job_detail/widget/job_detail_shimmer_page.dart';
import 'confirm_subscription_controller.dart';
import 'utils/cidades_widget.dart';

class ConfirmSubscriptionPage extends StatefulWidget {
  final JobDetailsResponseModel? jobDetails;

  const ConfirmSubscriptionPage({
    super.key,
    this.jobDetails,
  });

  @override
  _ConfirmSubscriptionPageState createState() =>
      _ConfirmSubscriptionPageState();
}

class _ConfirmSubscriptionPageState extends State<ConfirmSubscriptionPage> {
  final controller = Modular.get<ConfirmSubscriptionController>();

  @override
  void initState() {
    super.initState();
    controller.setJobDetails(widget.jobDetails);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarDefault(
        icon: Icons.close,
        titleText: 'Confirmar Inscrição'.i18n,
      ),
      body: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: Observer(
          builder: (BuildContext context) {
            if (controller.jobDetails == null) {
              return const JobDetailShimmerPage();
            }

            return Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                AppJobHeader(jobDetails: controller.jobDetails),
                const SizedBox(height: 10),
                AppContainer(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Text(
                        'Confirmar Inscrição'.i18n,
                        style: const TextStyle(
                          fontSize: 14,
                          color: AppConfig.colorPrimary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 20),
                      const Text(
                        'Ao se inscrever na vaga o seu currículo '
                        'será enviado automaticamente para a empresa '
                        'e você participará do processo seletivo.',
                        style: TextStyle(fontSize: 13),
                      ),
                      const SizedBox(height: 20),
                      const Text(
                        'Confirme sua cidade de interesse e '
                        'clique em confirmar inscrição.',
                        style: TextStyle(fontSize: 13),
                      ),
                      const SizedBox(height: 10),
                      CidadesWidget(controller: controller),
                      const SizedBox(height: 10),
                      AppDefaultButton(
                        color: AppConfig.green,
                        onPressed: _confirmSubscription,
                        title: Text(
                          'CONFIRMAR INSCRIÇÃO'.i18n,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Colors.white,
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                const SizedBox(height: 10),
              ],
            );
          },
        ),
      ),
    );
  }

  Future<void> _confirmSubscription() async {
    final response = await controller.confirmarInscricao();
    if (!mounted) return;

    if (response.sucesso) {
      Modular.to.pop(true);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          backgroundColor: Colors.redAccent,
          content: Text(response.retornoMensagem!),
        ),
      );
    }
  }
}
