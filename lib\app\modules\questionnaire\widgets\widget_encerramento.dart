import 'package:flutter/material.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../../shared/app_container.dart';
import '../../../shared/core/app_config.dart';
import '../questionnaire_controller.dart';

class WidgetEncerramento extends StatelessWidget {
  const WidgetEncerramento({
    super.key,
    required this.controller,
  });

  final QuestionnaireController controller;

  @override
  Widget build(BuildContext context) {
    return AppContainer(
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 30),
      width: double.infinity,
      child: Column(crossAxisAlignment: CrossAxisAlignment.center, children: [
        const Text(
          'Questionário Finalizado!',
          textAlign: TextAlign.center,
          style: TextStyle(
            color: AppConfig.green,
            fontSize: 22,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 5),
        const Text(
          'Você respondeu todas as perguntas do questionário.',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 30),
        InkWell(
          onTap: () async => await controller.finalizar(),
          child: Text(
            'FECHAR'.i18n,
            style: const TextStyle(
              fontSize: 18,
              color: AppConfig.colorPrimary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ]),
    );
  }
}
