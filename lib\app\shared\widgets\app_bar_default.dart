import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../modules/home/<USER>';

class AppBarDefault extends StatelessWidget implements PreferredSizeWidget {
  final String? titleText;
  final Widget? title;
  final Widget? subTitle;
  final Function? onBackPressed;
  final IconData icon;
  final Widget? trailing;

  @override
  final Size preferredSize;

  const AppBarDefault({
    super.key,
    this.titleText,
    this.onBackPressed,
    this.icon = Icons.arrow_back,
    this.trailing,
    this.subTitle,
    this.title,
  }) : preferredSize = const Size.fromHeight(60.0);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.white,
      title: title,
      elevation: 0.0,
      leading: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onBackPressed as void Function()? ??
              () {
                if (Modular.to.canPop()) {
                  Modular.to.pop();
                } else {
                  Modular.to.pushNamedAndRemoveUntil(
                    HomeModule.route,
                    (_) => false,
                  );
                }
              },
          customBorder: const CircleBorder(),
          splashColor: Colors.grey.withValues(alpha: 0.6),
          child: Container(
            padding: const EdgeInsets.all(8.0),
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: Colors.black,
              size: 28,
            ),
          ),
        ),
      ),
      actions: <Widget>[
        if (trailing != null) trailing!,
        const SizedBox(width: 15),
      ],
    );
  }
}
