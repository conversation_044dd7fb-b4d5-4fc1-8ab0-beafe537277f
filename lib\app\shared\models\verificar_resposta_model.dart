class VerificarRespostaModel {
  final String? token;
  final String? tipoPergunta;
  final String? resposta;

  VerificarRespostaModel({this.token, this.tipoPergunta, this.resposta});

  Map<String, dynamic> toJson() {
    final result = <String, dynamic>{};

    result.addAll({'token': token});
    result.addAll({'tipoPergunta': tipoPergunta});
    result.addAll({'resposta': resposta});

    return result;
  }
}
