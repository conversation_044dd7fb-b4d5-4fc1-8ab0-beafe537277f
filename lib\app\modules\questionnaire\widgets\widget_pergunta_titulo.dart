import 'package:flutter/material.dart';

import '../../../shared/widgets/app_html.dart';

class WidgetPerguntaTitulo extends StatefulWidget {
  final int? index;
  final String? descricao;

  const WidgetPerguntaTitulo({super.key, this.descricao, this.index});

  @override
  _WidgetPerguntaTituloState createState() => _WidgetPerguntaTituloState();
}

class _WidgetPerguntaTituloState extends State<WidgetPerguntaTitulo> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: Colors.white,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 8),
        child: SizedBox(
          width: MediaQuery.of(context).size.width,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 10.0),
                child: Text("PERGUNTA ${widget.index}:"),
              ),
              AppHtml(
                data: widget.descricao,
                fontSize: 16,
              )
            ],
          ),
        ),
      ),
    );
  }
}
