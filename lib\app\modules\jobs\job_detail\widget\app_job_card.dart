import 'package:bottom_sheet/bottom_sheet.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

import '../../../../shared/models/responses/inscricoes_response_model.dart';
import '../../../home/<USER>/tab_subscriptions_controller.dart';
import '../../../subscription/subscription_module.dart';
import '../job_detail_controller.dart';
import 'app_body_builder_job_detaisl.dart';
import 'app_header_builder_job_details.dart';

class AppJobCard extends StatelessWidget {
  final JobModel job;
  final bool isSubscription;

  AppJobCard({
    super.key,
    required this.job,
    this.isSubscription = false,
  });

  final controllerTab = Modular.get<TabSubscriptionsController>();
  final controllerJob = Modular.get<JobDetailController>();

  late final double off = controllerJob.headerOffset;
  @override
  Widget build(BuildContext context) {
    return ListTile(
      onTap: () async {
        if (isSubscription) {
          var isReload = await Modular.to.pushNamed(
            SubscriptionModule.route,
            arguments: job.candidaturaID,
          );
          if (isReload as bool? ?? false) {
            await controllerTab.load();
          }
        }

        if (!isSubscription) {
          await showStickyFlexibleBottomSheet(
            minHeight: .82,
            initHeight: .82,
            maxHeight: .92,
            maxHeaderHeight: 118,
            minHeaderHeight: 100,
            isSafeArea: true,
            bottomSheetBorderRadius: const BorderRadius.only(
              topLeft: Radius.circular(25),
              topRight: Radius.circular(25),
            ),
            anchors: [0.8],
            decoration: const BoxDecoration(
              color: Colors.white,
            ),
            context: context,
            isDismissible: true,
            duration: const Duration(milliseconds: 500),
            headerBuilder: (BuildContext context, double offset) {
              return AppHeaderBuildeJobDetails(
                image: job.logo?.thumb ?? "",
                title: job.titulo ?? "",
                company: job.empresa ?? "",
                date: job.data ?? "",
                offset: offset,
              );
            },
            bodyBuilder: (context, bottomSheetOffset) {
              return SliverChildListDelegate([
                Padding(
                  padding: const EdgeInsets.only(left: 24, right: 24),
                  child: AppBodyBuilderJobDetails(jobId: job.id!),
                )
              ]);
            },
          );

          controllerJob.removeJob(job.id!);
        }
      },
      leading: SizedBox(
        width: 60,
        child: CachedNetworkImage(
          imageUrl: job.logo?.thumb ?? '',
          imageBuilder: (context, imageProvider) => Container(
            decoration: BoxDecoration(
              image: DecorationImage(
                image: imageProvider,
                fit: BoxFit.fitWidth,
                alignment: Alignment.center,
              ),
            ),
          ),
          placeholder: (context, url) => SpinKitCircle(color: Colors.blue[300]),
          errorWidget: (context, url, error) => const Icon(
            Icons.broken_image,
            size: 60,
          ),
        ),
      ),
      isThreeLine: true,
      title: Row(
        children: [
          Expanded(
            child: Text(
              job.empresa ?? "",
              style: const TextStyle(
                fontSize: 14,
              ),
            ),
          ),
          Text(
            job.data ?? "",
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          ),
        ],
      ),
      subtitle: Text(
        job.titulo ?? "",
        style:
            const TextStyle(fontWeight: FontWeight.w500, color: Colors.black),
      ),
    );
  }

  double heightHeader() {
    if (job.titulo?.length != 3) {
      if (controllerJob.mostrarMaisCidades) {
        return (controllerJob.jobDetails!.cidades!.length + 1) * 14.0;
      } else {
        return 56.0;
      }
    }

    return controllerJob.jobDetails!.cidades!.length * 15.0;
  }
}
