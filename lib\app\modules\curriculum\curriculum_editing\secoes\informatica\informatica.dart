import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../../../../shared/core/app_config.dart';
import '../../../../../shared/models/responses/listar_informatica_response_model.dart';
import '../../../../../shared/widgets/app_badge.dart';
import '../../../../../shared/widgets/app_default_button.dart';
import '../../../../../shared/widgets/app_text_form_field.dart';
import '../../../controllers/computing_controller.dart';
import '../../curriculum_editing_controller.dart';
import '../../widgets/secao.dart';

class Informatica extends StatefulWidget {
  const Informatica({
    super.key,
    required this.avancadoFormKey,
    required this.intermediarioFormKey,
    required this.basicoFormKey,
  });

  final GlobalKey<FormState> avancadoFormKey;
  final GlobalKey<FormState> intermediarioFormKey;
  final GlobalKey<FormState> basicoFormKey;

  @override
  State<Informatica> createState() => _InformaticaState();
}

class _InformaticaState extends State<Informatica> {
  final ComputingController _curriculumController = Modular.get();
  final controller = Modular.get<CurriculumEditingController>();

  @override
  Widget build(BuildContext context) {
    return Secao(
      fields: Observer(builder: (_) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(bottom: 5, left: 3),
              child: Text(
                'AVANÇADO'.i18n,
                style: const TextStyle(
                  fontSize: 14,
                ),
              ),
            ),
            Row(
              children: [
                Expanded(
                  child: Form(
                    key: widget.avancadoFormKey,
                    child: AppTextFormField(
                      hintText: "Ex: Windows, Pacote Office, Photoshop, etc",
                      hintFontSize: 12,
                      controller: TextEditingController(
                        text: _curriculumController.informaticaAvancado,
                      ),
                      onChanged: (value) {
                        _curriculumController.informaticaAvancado = value;
                      },
                      validator: (value) {
                        if (value!.isEmpty) return 'Campo obrigatório'.i18n;

                        return null;
                      },
                    ),
                  ),
                ),
                const SizedBox(width: 10),
                AppDefaultButton(
                  color: AppConfig.colorPrimary,
                  title: Text(
                    'Adicionar'.i18n,
                    style: const TextStyle(
                      color: Colors.white,
                    ),
                  ),
                  width: 100,
                  height: 35,
                  onPressed: () {
                    if (widget.avancadoFormKey.currentState!.validate() &&
                        !_curriculumController.informaticaToSave.any((i) =>
                            i.nome ==
                                _curriculumController.informaticaAvancado &&
                            i.nivel == InformaticaModel.avancado)) {
                      setState(() {
                        _curriculumController.informaticaToSave.add(
                          InformaticaModel(
                            nivel: InformaticaModel.avancado,
                            nome: _curriculumController.informaticaAvancado,
                          ),
                        );
                      });
                      _curriculumController.informaticaAvancado = '';
                    }
                  },
                ),
              ],
            ),
            const SizedBox(height: 5),
            Wrap(
              children: _curriculumController.informaticaToSave
                  .where((i) => i.nivel == InformaticaModel.avancado)
                  .map(
                (i) {
                  return i.nome != null
                      ? AppBadge(
                          title: i.nome!,
                          onRemove: () {
                            setState(() {
                              _curriculumController.informaticaToSave
                                  .removeWhere((inf) =>
                                      inf.nome == i.nome &&
                                      inf.nivel == InformaticaModel.avancado);
                            });
                          },
                        )
                      : const SizedBox.shrink();
                },
              ).toList(),
            ),
            const SizedBox(height: 30),
            Padding(
              padding: const EdgeInsets.only(bottom: 5, left: 3),
              child: Text(
                'INTERMEDIÁRIO'.i18n,
                style: const TextStyle(fontSize: 14),
              ),
            ),
            Row(
              children: [
                Expanded(
                  child: Form(
                    key: widget.intermediarioFormKey,
                    child: AppTextFormField(
                      hintText: "Ex: Windows, Pacote Office, Photoshop, etc",
                      hintFontSize: 12,
                      controller: TextEditingController(
                        text: _curriculumController.informaticaIntermediario,
                      ),
                      onChanged: (value) {
                        _curriculumController.informaticaIntermediario = value;
                      },
                      validator: (value) {
                        if (value!.isEmpty) return 'Campo obrigatório'.i18n;

                        return null;
                      },
                    ),
                  ),
                ),
                const SizedBox(width: 10),
                AppDefaultButton(
                  color: AppConfig.colorPrimary,
                  title: Text(
                    'Adicionar'.i18n,
                    style: const TextStyle(color: Colors.white),
                  ),
                  width: 100,
                  height: 35,
                  onPressed: () {
                    if (widget.intermediarioFormKey.currentState!.validate() &&
                        !_curriculumController.informaticaToSave.any((i) =>
                            i.nome ==
                                _curriculumController
                                    .informaticaIntermediario &&
                            i.nivel == InformaticaModel.intermediario)) {
                      setState(() {
                        _curriculumController.informaticaToSave.add(
                          InformaticaModel(
                            nivel: InformaticaModel.intermediario,
                            nome:
                                _curriculumController.informaticaIntermediario,
                          ),
                        );
                      });
                      _curriculumController.informaticaIntermediario = '';
                    }
                  },
                ),
              ],
            ),
            const SizedBox(height: 5),
            Wrap(
              children: _curriculumController.informaticaToSave
                  .where((i) => i.nivel == InformaticaModel.intermediario)
                  .map(
                (i) {
                  return i.nome != null
                      ? AppBadge(
                          title: i.nome!,
                          onRemove: () {
                            setState(() {
                              _curriculumController.informaticaToSave
                                  .removeWhere((inf) =>
                                      inf.nome == i.nome &&
                                      inf.nivel ==
                                          InformaticaModel.intermediario);
                            });
                          },
                        )
                      : const SizedBox.shrink();
                },
              ).toList(),
            ),
            const SizedBox(height: 30),
            Padding(
              padding: const EdgeInsets.only(bottom: 5, left: 3),
              child: Text(
                'BÁSICO'.i18n,
                style: const TextStyle(
                  fontSize: 14,
                ),
              ),
            ),
            Row(
              children: [
                Expanded(
                  child: Form(
                    key: widget.basicoFormKey,
                    child: AppTextFormField(
                      hintText: "Ex: Windows, Pacote Office, Photoshop, etc",
                      hintFontSize: 12,
                      controller: TextEditingController(
                        text: _curriculumController.informaticaBasico,
                      ),
                      onChanged: (value) {
                        _curriculumController.informaticaBasico = value;
                      },
                      validator: (value) {
                        if (value!.isEmpty) return 'Campo obrigatório'.i18n;

                        return null;
                      },
                    ),
                  ),
                ),
                const SizedBox(width: 10),
                AppDefaultButton(
                  color: AppConfig.colorPrimary,
                  title: Text(
                    'Adicionar'.i18n,
                    style: const TextStyle(color: Colors.white),
                  ),
                  width: 100,
                  height: 35,
                  onPressed: () {
                    if (widget.basicoFormKey.currentState!.validate() &&
                        !_curriculumController.informaticaToSave.any((i) =>
                            i.nome == _curriculumController.informaticaBasico &&
                            i.nivel == InformaticaModel.basico)) {
                      setState(() {
                        _curriculumController.informaticaToSave.add(
                          InformaticaModel(
                            nivel: InformaticaModel.basico,
                            nome: _curriculumController.informaticaBasico,
                          ),
                        );
                      });
                      _curriculumController.informaticaBasico = '';
                    }
                  },
                ),
              ],
            ),
            const SizedBox(height: 5),
            Wrap(
              children: _curriculumController.informaticaToSave
                  .where((i) => i.nivel == InformaticaModel.basico)
                  .map(
                (i) {
                  return i.nome != null
                      ? AppBadge(
                          title: i.nome!,
                          onRemove: () {
                            setState(() {
                              _curriculumController.informaticaToSave
                                  .removeWhere((inf) =>
                                      inf.nome == i.nome &&
                                      inf.nivel == InformaticaModel.basico);
                            });
                          },
                        )
                      : const SizedBox.shrink();
                },
              ).toList(),
            ),
          ],
        );
      }),
    );
  }
}
