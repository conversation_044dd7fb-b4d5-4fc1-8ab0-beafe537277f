import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../../shared/core/app_config.dart';
import '../forgot_password_controller.dart';
import 'header_recovery_password.dart';
import 'widget_custom_toaster.dart';
import 'widget_reset_by_email.dart';

class WidgetValidationFailed extends StatefulWidget {
  static const route = '/validation_invalid';

  const WidgetValidationFailed({
    super.key,
  });

  @override
  State<WidgetValidationFailed> createState() => _WidgetValidationFailedState();
}

class _WidgetValidationFailedState extends State<WidgetValidationFailed> {
  final controller = Modular.get<ForgotPasswordController>();

  bool _isEmailLoading = false;
  bool _isDisposed = false;

  @override
  void dispose() {
    _isDisposed = true;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          HeaderRecoveryPassword(
            bottomBack: false,
            iconInnerTitle: Padding(
              padding: const EdgeInsets.only(right: 10),
              child: Icon(
                Icons.warning,
                color: Colors.red.shade700,
              ),
            ),
            title: 'Validação Falhou',
            subTitle:
                'Não foi possível recuperar sua senha pois a informação respondida não consta em seu cadastro.',
          ),
          const SizedBox(height: 10),
          Padding(
            padding: const EdgeInsets.only(
              left: 24.0,
              right: 24.0,
            ),
            child: SizedBox(
              height: 45.0,
              width: double.infinity,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6.0),
                  ),
                  backgroundColor: AppConfig.colorPrimary,
                  shadowColor: Colors.transparent,
                  elevation: 0,
                ),
                onPressed: _isEmailLoading ? null : () => orientacoesPorEmail(),
                child: _isEmailLoading
                    ? const CircularProgressIndicator(color: AppConfig.white)
                    : Text(
                        'Receber orientação por e-mail'.i18n,
                        style: const TextStyle(
                          color: AppConfig.white,
                          fontSize: 16,
                          fontFamily: 'Inter',
                          fontWeight: FontWeight.w400,
                        ),
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void orientacoesPorEmail() async {
    if (_isDisposed) return;
    setState(() {
      _isEmailLoading = true;
    });

    final response = await controller.postAlterarSenhaEmail();
    if (_isDisposed) return;

    if (response.sucesso) {
      Modular.to.pushNamed(
        WidgetResetByEmail.route,
        arguments: response.email,
      );
    } else {
      WidgetCustomToaster(
        context: context,
        message: response.mensagem,
        borderRadius: 4,
        duration: const Duration(seconds: 6),
      );
      if (!_isDisposed) {
        setState(() {
          _isEmailLoading = false;
        });
      }
    }

    if (!_isDisposed) {
      setState(() {
        _isEmailLoading = false;
      });
    }
  }
}
