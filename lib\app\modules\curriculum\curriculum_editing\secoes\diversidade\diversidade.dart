import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../controllers/diversities_controller.dart';
import '../../../curriculum_editing/widgets/secao.dart';
import '../../curriculum_editing_controller.dart';
import 'widgets/check_box_widget.dart.dart';
import 'widgets/radio_list_widget.dart';

class Diversidade extends StatefulWidget {
  const Diversidade({super.key});

  @override
  State<Diversidade> createState() => _DiversidadeState();
}

class _DiversidadeState extends State<Diversidade> {
  final DiversitiesController _curriculumController = Modular.get();
  final controller = Modular.get<CurriculumEditingController>();

  @override
  void initState() {
    _curriculumController.diversidadeToSave?.pronome =
        _curriculumController.diversidade?.pronome;

    _curriculumController.diversidadeToSave?.orientacaoSexual =
        _curriculumController.diversidade?.orientacaoSexual;

    _curriculumController.diversidadeToSave?.identidadeGenero =
        _curriculumController.diversidade?.identidadeGenero;

    _curriculumController.diversidadeToSave?.racaCor =
        _curriculumController.diversidade?.racaCor;

    _curriculumController.diversidadeToSave?.compartilharDadosDiversidade =
        _curriculumController.diversidade?.marcarCheckbox ?? false;

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Secao(
      fields: Column(
        children: [
          Container(
            width: double.maxFinite,
            margin: const EdgeInsets.only(top: 8),
            decoration: BoxDecoration(
              color: const Color(0xffd9edf7),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 22,
                    height: 22,
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                      color: Color(0xff31708f),
                    ),
                    child: const Center(
                      child: Icon(
                        Icons.priority_high_rounded,
                        size: 16,
                        color: Color(0xffd9edf7),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _curriculumController.diversidade?.alertTexto ?? '',
                      softWrap: true,
                      textAlign: TextAlign.left,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 10),
          RadioListWidget(
            controller: _curriculumController,
          ),
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.only(bottom: 40.0),
            child: CheckBoxWidget(
              onTap: () {
                _curriculumController
                        .diversidadeToSave?.compartilharDadosDiversidade =
                    !_curriculumController
                        .diversidadeToSave!.compartilharDadosDiversidade!;
                setState(() {});
              },
              onChanged: (value) {
                _curriculumController
                    .diversidadeToSave?.compartilharDadosDiversidade = value!;
                setState(() {});
              },
              value: _curriculumController
                  .diversidadeToSave!.compartilharDadosDiversidade!,
            ),
          ),
        ],
      ),
    );
  }
}
