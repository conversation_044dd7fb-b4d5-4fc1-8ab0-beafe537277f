class PerguntaSalvarResponseModel {
  final bool sucesso;
  final String mensagem;
  final int? index;
  final int? proximaPergunta;
  final bool? finalizado;
  final bool? mostrarNota;

  PerguntaSalvarResponseModel({
    this.sucesso = false,
    this.mensagem = "",
    this.index,
    this.proximaPergunta,
    this.finalizado,
    this.mostrarNota,
  });

  factory PerguntaSalvarResponseModel.fromJson(Map<String, dynamic> json) {
    return PerguntaSalvarResponseModel(
      sucesso: json['sucesso'] ?? false,
      mensagem: json['mensagem'] ?? "",
      index: json['index'] as int?,
      proximaPergunta: json['proximaPergunta'] as int?,
      finalizado: json['finalizado'] as bool?,
      mostrarNota: json['mostrarNota'] as bool?,
    );
  }
}
