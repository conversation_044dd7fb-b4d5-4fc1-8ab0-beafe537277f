import 'dart:async';

// ignore: import_of_legacy_library_into_null_safe
import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';

enum PlayerState { stopped, playing, paused }

typedef OnError = void Function(Exception exception);

class AppAudioPlayer extends StatefulWidget {
  final String? url;

  const AppAudioPlayer({super.key, this.url});

  @override
  _AppAudioPlayerState createState() => _AppAudioPlayerState();
}

class _AppAudioPlayerState extends State<AppAudioPlayer> {
  Duration duration = const Duration(seconds: 0);
  Duration? position;

  late AudioPlayer audioPlayer;

  String? localFilePath;

  PlayerState playerState = PlayerState.stopped;

  get isPlaying => playerState == PlayerState.playing;

  get isPaused => playerState == PlayerState.paused;

  get durationText => duration.toString().split('.').first;

  get positionText =>
      position != null ? position.toString().split('.').first : '';

  bool isMuted = false;

  late StreamSubscription _positionSubscription;
  late StreamSubscription _durationSubscription;
  late StreamSubscription _audioPlayerStateSubscription;

  @override
  void initState() {
    super.initState();
    initAudioPlayer();
  }

  @override
  void dispose() {
    _positionSubscription.cancel();
    _durationSubscription.cancel();
    _audioPlayerStateSubscription.cancel();
    audioPlayer.stop();
    super.dispose();
  }

  void initAudioPlayer() {
    audioPlayer = AudioPlayer();
    _positionSubscription = audioPlayer.onPositionChanged.listen(
      (p) => setState(() {
        position = p;
      }),
    );

    _durationSubscription = audioPlayer.onDurationChanged.listen((event) {
      duration = event;
      setState(() {});
    });

    _audioPlayerStateSubscription =
        audioPlayer.onPlayerStateChanged.listen((s) {
      // ignore: unrelated_type_equality_checks
      if (s == PlayerState.stopped) {
        onComplete();
        setState(() {
          position = duration;
        });
      }
    }, onError: (msg) {
      setState(() {
        playerState = PlayerState.stopped;
        duration = const Duration(seconds: 0);
        position = const Duration(seconds: 0);
      });
    });
  }

  Future play() async {
    await audioPlayer.play(UrlSource(widget.url!));
    setState(() {
      playerState = PlayerState.playing;
    });
  }

  Future pause() async {
    await audioPlayer.pause();
    setState(() => playerState = PlayerState.paused);
  }

  Future stop() async {
    await audioPlayer.stop();
    setState(() {
      playerState = PlayerState.stopped;
      position = const Duration();
    });
  }

  Future mute(bool muted) async {
    await audioPlayer.setVolume(muted ? 0 : 1);
    setState(() {
      isMuted = muted;
    });
  }

  void onComplete() {
    setState(() => playerState = PlayerState.stopped);
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Material(child: _buildPlayer()),
    );
  }

  Widget _buildPlayer() => Container(
        width: MediaQuery.of(context).size.width * 0.70,
        height: MediaQuery.of(context).size.height * 0.15,
        padding: EdgeInsets.zero,
        decoration: BoxDecoration(
          color: Colors.grey.withValues(alpha: 0.5),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Slider(
              activeColor: Colors.blue,
              inactiveColor: Colors.white,
              value: position?.inMilliseconds.toDouble() ?? 0,
              onChanged: (double value) {
                audioPlayer.seek(
                  Duration(milliseconds: value.toInt()),
                );
              },
              min: 0,
              max: duration.inMilliseconds.toDouble(),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Visibility(
                  visible: !isPlaying,
                  replacement: IconButton(
                    onPressed: pause,
                    icon: const Icon(Icons.pause),
                    color: Colors.white,
                  ),
                  child: IconButton(
                    onPressed: play,
                    icon: const Icon(Icons.play_arrow),
                    color: Colors.white,
                  ),
                ),
                Text(
                  position != null
                      ? "${positionText ?? ''} / ${durationText ?? ''}"
                      : durationText,
                  style: const TextStyle(fontSize: 14.0, color: Colors.white),
                ),
                Visibility(
                  visible: !isMuted,
                  replacement: IconButton(
                    onPressed: () => mute(false),
                    icon: const Icon(Icons.headset, color: Colors.white),
                  ),
                  child: IconButton(
                    onPressed: () => mute(true),
                    icon: const Icon(
                      Icons.headset_off,
                      color: Colors.white,
                    ),
                  ),
                )
              ],
            ),
          ],
        ),
      );
}
