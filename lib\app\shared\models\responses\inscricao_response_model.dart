import 'package:intl/intl.dart';

class InscricaoResponseModel {
  final bool sucesso;
  final String mensagem;
  final VagaModel? vaga;
  final CandidaturaModel? candidatura;
  final List<QuestionarioModel>? questionarios;

  InscricaoResponseModel({
    this.sucesso = false,
    this.mensagem = "",
    this.vaga,
    this.candidatura,
    this.questionarios,
  });

  factory InscricaoResponseModel.fromJson(Map<String, dynamic> json) {
    return InscricaoResponseModel(
      sucesso: json['sucesso'] ?? false,
      mensagem: json['mensagem'] ?? "",
      vaga: json['vaga'] == null
          ? null
          : VagaModel.fromJson(json['vaga'] as Map<String, dynamic>),
      candidatura: json['candidatura'] == null
          ? null
          : CandidaturaModel.fromJson(
              json['candidatura'] as Map<String, dynamic>),
      questionarios: (json['questionarios'] as List<dynamic>?)
          ?.map((e) => QuestionarioModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class VagaModel {
  final int? id;
  final String? titulo;
  final String? url;
  final String? empresa;
  final String? salario;
  final String? nivel;

  VagaModel({
    this.id,
    this.titulo,
    this.url,
    this.empresa,
    this.salario,
    this.nivel,
  });

  factory VagaModel.fromJson(Map<String, dynamic> json) {
    return VagaModel(
      id: json['id'] as int?,
      titulo: json['titulo'] as String?,
      url: json['url'] as String?,
      empresa: json['empresa'] as String?,
      salario: json['salario'] as String?,
      nivel: json['nivel'] as String?,
    );
  }
}

class CandidaturaModel {
  final String? data;
  final int? situacao;
  final String? situacaoText;
  final String? situacaoDescricao;
  final String? situacaoCorTexto;
  final String? situacaoCorFundo;

  CandidaturaModel({
    this.data,
    this.situacao,
    this.situacaoText,
    this.situacaoDescricao,
    this.situacaoCorTexto,
    this.situacaoCorFundo,
  });

  factory CandidaturaModel.fromJson(Map<String, dynamic> json) {
    return CandidaturaModel(
      data: json['data'] as String?,
      situacao: json['situacao'] as int?,
      situacaoText: json['situacaoText'] as String?,
      situacaoDescricao: json['situacaoDescricao'] as String?,
      situacaoCorTexto: json['situacaoCorTexto'] as String?,
      situacaoCorFundo: json['situacaoCorFundo'] as String?,
    );
  }
}

class QuestionarioModel {
  final int? id;
  final String? questionarioTitulo;
  final bool? liberarVisualizacao;
  final String? situacao;
  final String? situacaoCor;
  final String? nota;
  final String? responderAte;
  final String? solicitado;
  final String? iniciado;
  final String? respondido;

  String get solicitadoView {
    if (solicitado?.isEmpty ?? true) return "";

    return DateFormat('dd/MM/yyyy').format(DateTime.parse(solicitado!));
  }


  String get respondidoView {
    if (respondido?.isEmpty ?? true) return "";

    return DateFormat('dd/MM/yyyy').format(DateTime.parse(respondido!));
  }

  QuestionarioModel({
    this.id,
    this.questionarioTitulo,
    this.liberarVisualizacao,
    this.situacao,
    this.situacaoCor,
    this.nota,
    this.responderAte,
    this.solicitado,
    this.iniciado,
    this.respondido,
  });

  factory QuestionarioModel.fromJson(Map<String, dynamic> json) {
    return QuestionarioModel(
      id: json['id'] as int?,
      questionarioTitulo: json['questionarioTitulo'] as String?,
      liberarVisualizacao: json['liberarVisualizacao'] as bool?,
      situacao: json['situacao'] as String?,
      situacaoCor: json['situacaoCor'] as String?,
      nota: json['nota'] as String?,
      responderAte: json['responderAte'] as String?,
      solicitado: json['solicitado'] as String?,
      iniciado: json['iniciado'] as String?,
      respondido: json['respondido'] as String?,
    );
  }
}
