import 'package:flutter_modular/flutter_modular.dart';
import 'package:mobx/mobx.dart';

import '../../shared/models/job_parameter_model.dart';
import '../../shared/models/responses/inscricoes_response_model.dart';
import '../../shared/models/responses/job_sugestoes_response_model.dart';
import '../../shared/repositories/job_repository.dart';

part 'jobs_controller.g.dart';

class JobsController = _JobsControllerBase with _$JobsController;

abstract class _JobsControllerBase with Store {
  final JobRepository _repository = Modular.get();

  @observable
  bool loading = true;

  int? _totalPaginas;
  int? get totalPaginas => _totalPaginas;

  @observable
  int _totalVagas = 0;
  int get totalVagas => _totalVagas;

  @observable
  FacetsModel _facets = FacetsModel.empty();
  FacetsModel get facets => _facets;

  @observable
  bool _pcd = false;
  bool get isPcd => _pcd;

  @observable
  bool _asCegas = false;
  bool get isAsCegas => _asCegas;

  @observable
  List<JobModel> _jobs = <JobModel>[].asObservable();
  List<JobModel> get jobs => _jobs;

  @action
  clearJobs() => _jobs = <JobModel>[].asObservable();

  @action
  Future<void> load({JobParameterModel? filtros, bool reload = false}) async {
    try {
      loading = true;
      var data = await _repository.getJobs(filtros: filtros);

      if (reload) {
        _jobs = data.dados ?? [];
        _totalPaginas = data.totalPaginas!.floor();
        _totalVagas = data.totalVagas!.floor();
      } else if (data.dados!.isNotEmpty) {
        jobs.addAll(data.dados ?? []);
        _totalPaginas = data.totalPaginas!.floor();
        _totalVagas = data.totalVagas!.floor();
      }

      _facets = data.facets ?? FacetsModel.empty();
    } finally {
      loading = false;
    }
  }
}
