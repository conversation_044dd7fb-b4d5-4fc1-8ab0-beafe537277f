import 'package:flutter_modular/flutter_modular.dart';
import 'package:get/get.dart';

import '../../shared/mixins/loader_mixin.dart';
import '../../shared/models/job_parameter_model.dart';
import '../../shared/models/responses/inscricoes_response_model.dart';
import '../../shared/models/responses/job_sugestoes_response_model.dart';
import '../../shared/repositories/job_repository.dart';

class JobsController extends GetxController with LoaderMixin {
  final JobRepository _repository = Modular.get();

  int? _totalPaginas;
  int? get totalPaginas => _totalPaginas;

  int _totalVagas = 0;
  int get totalVagas => _totalVagas;

  FacetsModel _facets = FacetsModel.empty();
  FacetsModel get facets => _facets;

  bool _pcd = false;
  bool get isPcd => _pcd;

  bool _asCegas = false;
  bool get isAsCegas => _asCegas;

  List<JobModel> _jobs = <JobModel>[];
  List<JobModel> get jobs => _jobs;

  void clearJobs() {
    _jobs = <JobModel>[];
    update();
  }

  Future<void> load({JobParameterModel? filtros, bool reload = false}) async {
    try {
      changeLoading(true);
      var data = await _repository.getJobs(filtros: filtros);

      if (reload) {
        _jobs = data.dados ?? [];
        _totalPaginas = data.totalPaginas!.floor();
        _totalVagas = data.totalVagas!.floor();
      } else if (data.dados!.isNotEmpty) {
        jobs.addAll(data.dados ?? []);
        _totalPaginas = data.totalPaginas!.floor();
        _totalVagas = data.totalVagas!.floor();
      }

      _facets = data.facets ?? FacetsModel.empty();
      update();
    } finally {
      changeLoading(false);
    }
  }
}
