import 'package:dio/dio.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../core/app_rest.dart';
import '../models/responses/listar_grau_academico_response_model.dart';

class AcademicDegreeRepository {
  final AppRest _rest = Modular.get();

  Future<GrauAcademicoResponseModel> getGrauAcademico() async {
    try {
      final response = await _rest.get(
        '/variaveis/grau-academico/listar',
      );

      return GrauAcademicoResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return GrauAcademicoResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return GrauAcademicoResponseModel.fromJson(err.response!.data);
    }
  }
}
