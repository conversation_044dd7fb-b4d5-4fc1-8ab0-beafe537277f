import 'package:flutter/material.dart';

import '../curriculum_controller.dart';

class AlertaEndereco extends StatelessWidget {
  const AlertaEndereco({
    super.key,
    required this.controller,
  });

  final CurriculumController controller;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: DecoratedBox(
        decoration: BoxDecoration(
          color: const Color(0xFFfff4e5).withValues(alpha: 0.5),
          borderRadius: const BorderRadius.all(
            Radius.circular(4),
          ),
          border: Border.all(
            color: const Color(0xFFfff4e5),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(10.0),
          child: Row(
            children: [
              const Icon(
                Icons.warning_amber_outlined,
                size: 24,
                color: Color(0xFFEF6C00),
              ),
              const SizedBox(width: 5),
              RichText(
                text: TextSpan(
                  children: [
                    const TextSpan(
                      text: 'Endereço incompleto: ',
                      style: TextStyle(color: Color(0xFF663C00)),
                    ),
                    if (controller.pessoa?.logradouro == '')
                      const TextSpan(
                        text: 'nome da rua',
                        style: TextStyle(
                          color: Color(0xFF663C00),
                          fontWeight: FontWeight.w600,
                          fontFamily: 'Roboto',
                        ),
                      ),
                    if (controller.pessoa?.bairro == '')
                      const TextSpan(
                        text: ', ',
                        style: TextStyle(
                          color: Color(0xFF663C00),
                          fontWeight: FontWeight.w600,
                          fontFamily: 'Roboto',
                        ),
                      ),
                    if (controller.pessoa?.bairro == '')
                      const TextSpan(
                        text: 'bairro',
                        style: TextStyle(
                          color: Color(0xFF663C00),
                          fontWeight: FontWeight.w600,
                          fontFamily: 'Roboto',
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
