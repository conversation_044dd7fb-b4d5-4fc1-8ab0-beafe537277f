import 'package:flutter/material.dart';

class PerguntaResponseModel {
  final bool sucesso;
  final String mensagem;
  final PerguntaModel? pergunta;

  PerguntaResponseModel(
      {this.sucesso = false, this.mensagem = "", this.pergunta});

  factory PerguntaResponseModel.fromJson(Map<String, dynamic> json) {
    return PerguntaResponseModel(
      sucesso: json['sucesso'] ?? false,
      mensagem: json['mensagem'] ?? "",
      pergunta: json['pergunta'] == null
          ? null
          : PerguntaModel.fromJson(json['pergunta'] as Map<String, dynamic>),
    );
  }
}

class PerguntaModel {
  final String? id;
  final String? descricao;
  final int? tempo;
  final String? tipo;
  final int? index;
  final List<AlternativaModel>? alternativas;
  final String? conteudo;

  final controller = TextEditingController();

  PerguntaModel({
    this.id,
    this.descricao,
    this.tempo,
    this.tipo,
    this.index,
    this.alternativas,
    this.conteudo,
  });

  factory PerguntaModel.fromJson(Map<String, dynamic> json) {
    return PerguntaModel(
      id: json['id'] as String?,
      descricao: json['descricao'] as String?,
      tempo: json['tempo'] as int?,
      tipo: json['tipo'] as String?,
      index: json['index'] as int?,
      alternativas: (json['alternativas'] as List<dynamic>?)
          ?.map((e) => AlternativaModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      conteudo: json['conteudo'] as String?,
    );
  }
}

class AlternativaModel {
  final int? id;
  final String? descricao;

  AlternativaModel({this.id, this.descricao});

  factory AlternativaModel.fromJson(Map<String, dynamic> json) {
    return AlternativaModel(
      id: json['id'] as int?,
      descricao: json['descricao'] as String?,
    );
  }
}
