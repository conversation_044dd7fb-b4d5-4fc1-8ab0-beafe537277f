import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

import 'custom_check_box.dart';

class PrivacyPolitics extends StatelessWidget {
  const PrivacyPolitics({
    super.key,
    required this.acceptTermsOfUse,
    required this.onChanged,
    this.termsOfUse,
    this.privacyPolicy,
    this.cookiesPolicy,
  });

  final bool acceptTermsOfUse;
  final Function(bool)? onChanged;

  final String? termsOfUse;
  final String? privacyPolicy;
  final String? cookiesPolicy;

  void _openUrl(BuildContext context, String url) {
    if (url.isNotEmpty) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => WebViewWidget(
            controller: WebViewController()..loadRequest(Uri.parse(url)),
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 48,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomCheckBox(
            onChanged: onChanged,
            tamanho: 20,
            value: acceptTermsOfUse,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: RichText(
              text: TextSpan(
                text: 'Li e aceito os ',
                style: const TextStyle(
                  fontSize: 12,
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w400,
                  color: Colors.black,
                ),
                children: [
                  TextSpan(
                    text: 'Termos de Uso',
                    style: const TextStyle(
                      color: Colors.blue,
                      decoration: TextDecoration.underline,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        if (termsOfUse != null) {
                          _openUrl(context, termsOfUse!);
                        }
                      },
                  ),
                  const TextSpan(text: ' e a '),
                  TextSpan(
                    text: 'Política de Privacidade',
                    style: const TextStyle(
                      color: Colors.blue,
                      decoration: TextDecoration.underline,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        if (privacyPolicy != null) {
                          _openUrl(context, privacyPolicy!);
                        }
                      },
                  ),
                  const TextSpan(text: ', incluindo a '),
                  TextSpan(
                    text: 'Política de Cookies',
                    style: const TextStyle(
                      color: Colors.blue,
                      decoration: TextDecoration.underline,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        if (cookiesPolicy != null) {
                          _openUrl(context, cookiesPolicy!);
                        }
                      },
                  ),
                  const TextSpan(text: ' da EMPREGARE.'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
