import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:get/get.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../../../shared/core/app_utils.dart';
import '../../../../shared/helpers/snack_bar_helper.dart';
import '../../../../shared/models/responses/listar_formacoes_response_model.dart';
import '../../curriculum_controller.dart';
import '../../curriculum_editing/curriculum_editing_module.dart';
import '../../widgets/add_button.dart';
import '../../widgets/curriculum_default_field.dart';
import '../../widgets/dialog_confirm_delete.dart';
import '../secao/secao_widget.dart';

class FormacaoAcademicaWidget extends StatefulWidget {
  const FormacaoAcademicaWidget({super.key});

  @override
  _FormacaoAcademicaWidgetState createState() =>
      _FormacaoAcademicaWidgetState();
}

class _FormacaoAcademicaWidgetState extends State<FormacaoAcademicaWidget> {
  final CurriculumController controller = Modular.get();

  final _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void onAddOrEdit(FormacaoModel toSave) async {
    controller.formacaoToSave = toSave;
    var result = await Modular.to.pushNamed(
      CurriculumEditingModule.route,
      arguments: CurriculumEditingModule.formacaoAcademica,
    );
    if (result == true) {
      SnackbarHelper.showSnackbarSucesso(
        context,
        'Formação Acadêmica salva com sucesso',
      );
      controller.loadFormacoes();
      _scrollController.jumpTo(_scrollController.offset);
    }
  }

  void onRemove(FormacaoModel toRemove) async {
    bool? confirmDelete = await showDialog(
      context: context,
      builder: (_) => const ConfirmDeleteDialog(
        secao: 'Formação acadêmica',
      ),
    );

    if (confirmDelete == true) {
      var result = await controller.deleteFormacaoAcademica(toRemove.id);
      if (result == true) {
        SnackbarHelper.showSnackbarSucesso(
          context,
          'Formação acadêmica removida com sucesso',
        );
        controller.loadFormacoes();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CurriculumController>(
      builder: (_) => SingleChildScrollView(
        controller: _scrollController,
        child: SecaoWidget(
          header: 'Formação acadêmica'.i18n,
          showEdit: false,
          hasContent: controller.formacoesSeguro.isNotEmpty,
          replaceEditWithAdd: false,
          content: ColoredBox(
            color: Colors.white,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 15),
              child: Column(
                children: [
                  ...controller.formacoesSeguro.map(
                    (f) => Column(
                      children: [
                        DecoratedBox(
                          decoration: BoxDecoration(
                            color:
                                const Color(0xFFf3f3fc).withValues(alpha: 0.5),
                            borderRadius: const BorderRadius.all(
                              Radius.circular(4),
                            ),
                            border: Border.all(
                              color: const Color(0xFFdee2e6),
                            ),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Expanded(
                                      child: Text(
                                        f.grau!,
                                        style: const TextStyle(
                                          fontSize: 14,
                                          fontFamily: 'Inter',
                                          fontWeight: FontWeight.w400,
                                        ),
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.only(
                                        left: 5.0,
                                      ),
                                      child: Material(
                                        child: InkWell(
                                          onTap: () => onAddOrEdit(f.copy()),
                                          child: Image.asset(
                                            'lib/assets/icons/pencil-square.png',
                                            scale: 1.75,
                                          ),
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 25),
                                    Material(
                                      child: InkWell(
                                        onTap: () => onRemove(f),
                                        child: const Icon(
                                          Icons.delete,
                                          color: Colors.red,
                                          size: 24,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                if (!f.isEnsinoFundamental && !f.isEnsinoMedio)
                                  Text(
                                    '${f.curso} - ${f.local}',
                                    style: const TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w400,
                                      fontFamily: 'Inter',
                                    ),
                                  ),
                                if (f.isEnsinoMedio)
                                  Text(
                                    f.local!,
                                    style: const TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w400,
                                      fontFamily: 'Inter',
                                    ),
                                  ),
                                if (!f.isEnsinoFundamental)
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 5.0,
                                    ),
                                    child: Text(
                                      '%s de %s até %s de %s %s'.i18n.fill(
                                        [
                                          getMes(int.parse(f.mesInicio!)),
                                          f.anoInicio!,
                                          getMes(int.parse(f.mesFim!)),
                                          f.anoFim!,
                                          f.andamentoStr!
                                        ],
                                      ),
                                      style: const TextStyle(
                                        color: Color(0xFF5F5F91),
                                        fontSize: 12,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(
                          height: 5,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  AddButton(
                    onTap: () => onAddOrEdit(FormacaoModel()),
                    btnTitle: 'Adicionar formação',
                  ),
                ],
              ),
            ),
          ),
          noContent: CurriculumDefaultField(
            bottomAdd: () => onAddOrEdit(FormacaoModel()),
            textBottom: 'Adicionar formação',
            description:
                'Seu histórico de formação acadêmica (ensino médio, graduação, pós-graduação, doutorado, etc.)',
          ),
        ),
      ),
    );
  }
}
