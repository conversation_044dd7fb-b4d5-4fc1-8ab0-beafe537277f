class PrepareMetadataModel {
  final String? token;
  final int? blocksCount;
  final String? fileName;
  final int? fileSize;
  final String? idUpload;
  final String? contentType;
  final int? tipo;

  PrepareMetadataModel({
    this.token,
    this.blocksCount,
    this.fileName,
    this.idUpload,
    this.fileSize,
    this.contentType,
    this.tipo,
  });

  Map<String, dynamic> toJson() {
    final result = <String, dynamic>{};

    result.addAll({'token': token});
    result.addAll({'blocksCount': blocksCount});
    result.addAll({'fileName': fileName});
    result.addAll({'fileSize': fileSize});
    result.addAll({'idUpload': idUpload});
    result.addAll({'contentType': contentType});
    result.addAll({'tipo': tipo});

    return result;
  }
}
