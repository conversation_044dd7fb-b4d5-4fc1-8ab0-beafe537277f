import '../../../../controllers/diversities_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';

import 'custom_radio_list.dart';

class RadioListWidget extends StatelessWidget {
  final DiversitiesController controller;

  const RadioListWidget({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Observer(
      builder: (_) => Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Expanded(
            child: SizedBox(
              width: double.infinity,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const TitleDiversity(
                    title: 'Pronome',
                  ),
                  CustomRadioListWithInfoColumn(
                    dataList: controller.pronomeDiversidades.map((diversidade) {
                      return {
                        'title': diversidade.pronomeTexto,
                        'value': diversidade.pronome,
                      };
                    }).toList(),
                    titleKey: 'title',
                    groupValue: controller.diversidadeToSave?.pronome,
                    onChanged: (value) {
                      controller.updatePronome(value ?? 0);
                    },
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  const TitleDiversity(
                    title: 'Orientação sexual',
                  ),
                  CustomRadioListWithInfoColumn(
                    dataList:
                        controller.orientacaoDiversidades.map((diversidade) {
                      return {
                        'title': diversidade.orientacaoSexualTexto,
                        'value': diversidade.orientacaoSexual,
                      };
                    }).toList(),
                    titleKey: 'title',
                    groupValue: controller.diversidadeToSave?.orientacaoSexual,
                    onChanged: (value) {
                      controller.updateOrientacao(value ?? 0);
                    },
                    getToolTip: controller.toolTip,
                  ),
                ],
              ),
            ),
          ),
          Expanded(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.only(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const TitleDiversity(
                    title: 'Identidade de gênero',
                  ),
                  CustomRadioListWithInfoColumn(
                    dataList:
                        controller.identidadeDiversidades.map((diversidade) {
                      return {
                        'title': diversidade.identidadeGeneroTexto,
                        'value': diversidade.identidadeGenero,
                      };
                    }).toList(),
                    titleKey: 'title',
                    groupValue: controller.diversidadeToSave?.identidadeGenero,
                    onChanged: (value) {
                      controller.updateIdentidade(value ?? 0);
                    },
                    getToolTip: controller.toolTip,
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  const TitleDiversity(
                    title: "Raça ou Cor",
                  ),
                  CustomRadioListWithInfoColumn(
                    dataList: controller.racaDiversidades.map((diversidade) {
                      return {
                        'title': diversidade.racaCorTexto,
                        'value': diversidade.racaCor,
                      };
                    }).toList(),
                    titleKey: 'title',
                    groupValue: controller.diversidadeToSave?.racaCor,
                    onChanged: (value) {
                      controller.updateRaca(value ?? 0);
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class TitleDiversity extends StatelessWidget {
  final String? title;
  const TitleDiversity({
    super.key,
    this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: Text(
        title ?? '',
        style: const TextStyle(
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
