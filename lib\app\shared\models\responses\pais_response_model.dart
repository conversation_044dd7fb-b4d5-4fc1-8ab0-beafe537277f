class PaisResponseModel {
  final bool sucesso;
  final String mensagem;
  final List<PaisModel>? dados;

  PaisResponseModel({
    this.sucesso = false,
    this.mensagem = "",
    this.dados,
  });

  factory PaisResponseModel.fromJson(Map<String, dynamic> json) {
    return PaisResponseModel(
      sucesso: json['sucesso'] ?? false,
      mensagem: json['mensagem'] ?? "",
      dados: (json['dados'] as List<dynamic>?)
          ?.map((e) => PaisModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class PaisModel {
  final String? id;
  final String? nome;

  PaisModel({this.id, this.nome});
  factory PaisModel.fromJson(Map<String, dynamic> json) {
    return PaisModel(
      id: json['id'] as String?,
      nome: json['nome'] as String?,
    );
  }

  String get nomeSeguro => nome ?? '';

  @override
  String toString() => nomeSeguro;

  @override
  operator ==(other) => other is PaisModel && other.id == id;
  @override
  int get hashCode => id.hashCode ^ nomeSeguro.hashCode;
}
