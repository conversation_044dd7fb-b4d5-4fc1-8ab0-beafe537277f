import 'dart:async';
import 'dart:io';

import 'package:camera/camera.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:mobx/mobx.dart';

import '../../../shared/core/app_utils.dart';

part 'questionnaire_video_controller.g.dart';

class QuestionnaireVideoController = _QuestionnaireVideoControllerBase
    with _$QuestionnaireVideoController;

abstract class _QuestionnaireVideoControllerBase with Store {
  late Timer _tContagem;

  late Timer _tTempo;

  @observable
  CameraController? cameraController;

  @observable
  int _tempo = 0;
  String get tempo => formatSeconds(_tempo);

  @observable
  int contagem = 3;

  @observable
  bool inicializado = false;

  @action
  Future<void> initialize({int tempo = 90}) async {
    try {
      _tempo = tempo;

      var cameras = await availableCameras();
      var camera = cameras
          .firstWhere((e) => e.lensDirection == CameraLensDirection.front);

      cameraController = CameraController(camera, ResolutionPreset.high);
      await cameraController!.initialize();

      await start();
    } finally {
      inicializado = true;
    }
  }

  @action
  Future<void> start() async {
    _tContagem = Timer.periodic(const Duration(seconds: 1), (timer) async {
      if (contagem > 1) {
        contagem--;
      } else {
        await record();
        contagem--;
        _tContagem.cancel();
      }
    });
  }

  @action
  Future<void> record() async {
    if (cameraController!.value.isRecordingVideo) {
      return;
    }
    try {
      await cameraController!.startVideoRecording();
      // ignore: empty_catches
    } catch (e) {}

    _tTempo = Timer.periodic(const Duration(seconds: 1), (timer) async {
      if (_tempo > 0) {
        _tempo--;
      } else {
        await stop();
      }
    });
  }

  Future<void> stop() async {
    try {
      if (!cameraController!.value.isRecordingVideo) {
        return;
      }
      _tTempo.cancel();
      _tContagem.cancel();
      var result = await cameraController!.stopVideoRecording();
      Modular.to.pop(File(result.path));
    } catch (e) {
      Modular.to.pop();
    }
  }
}
