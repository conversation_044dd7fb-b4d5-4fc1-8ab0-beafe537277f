import 'dart:io';

import 'package:chewie/chewie.dart';
import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

class WidgetVideoPlayer extends StatefulWidget {
  final File? videoFile;
  final double? width;
  final double? height;
  final UniqueKey? newKey;

  const WidgetVideoPlayer({
    super.key,
    this.videoFile,
    this.newKey,
    this.width,
    this.height,
  });

  @override
  _WidgetVideoPlayerState createState() => _WidgetVideoPlayerState();
}

class _WidgetVideoPlayerState extends State<WidgetVideoPlayer> {
  VideoPlayerController? _controller;
  ChewieController? _chewie;

  @override
  void initState() {
    _initControllers(widget.videoFile!);
    super.initState();
  }

  void _initControllers(File file) {
    _controller = VideoPlayerController.file(file);
    _chewie = ChewieController(
      videoPlayerController: _controller!,
      aspectRatio: widget.width! / widget.height!,
      autoPlay: false,
      looping: true,
      autoInitialize: true,
    );
  }

  @override
  void dispose() {
    _controller?.dispose();
    _chewie?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width,
      height: widget.height,
      color: Colors.black,
      child: Chewie(
        controller: _chewie!,
      ),
    );
  }
}
