import 'dart:io';

import '../curriculum_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:i18n_extension/default.i18n.dart';
import 'package:image_picker/image_picker.dart';

import '../../../app_controller.dart';
import '../../../shared/widgets/app_usuario_card/app_usuario_card_controller.dart';

class AlterarFotoDialog extends StatelessWidget {
  final CurriculumController controller;
  final AppUsuarioCardController controllerUsuario;

  const AlterarFotoDialog({
    super.key,
    required this.controller,
    required this.controllerUsuario,
  });

  Future<void> _pickAndSavePhoto(
      BuildContext context, ImageSource source) async {
    if (!context.mounted) return;

    final messenger = ScaffoldMessenger.maybeOf(context);
    if (messenger == null) return;

    Navigator.of(context).pop();

    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(source: source);

      if (pickedFile != null) {
        final photoFile = File(pickedFile.path);
        final response = await controller.saveFoto(photoFile);

        if (!context.mounted) return;

        if (response.sucesso) {
          Modular.get<AppController>().updateProfile();
          await Future.wait([
            controllerUsuario.load(),
            controller.loadDadosPessoais(),
          ]);

          if (!context.mounted) return;

          final updatedMessenger = ScaffoldMessenger.maybeOf(context);
          if (updatedMessenger != null) {
            updatedMessenger.showSnackBar(
              const SnackBar(content: Text('Foto atualizada com sucesso!')),
            );
          }
        }
      }
    } catch (e) {
      debugPrint('Erro ao selecionar imagem: $e');

      if (!context.mounted) return;

      final errorMessenger = ScaffoldMessenger.maybeOf(context);
      if (errorMessenger != null) {
        errorMessenger.showSnackBar(
          const SnackBar(content: Text('Erro ao selecionar a imagem')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Alterar Foto',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
            const Divider(),
            InkWell(
              onTap: () => _pickAndSavePhoto(context, ImageSource.camera),
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  vertical: 8.0,
                  horizontal: 10,
                ),
                child: Row(
                  children: [
                    const Icon(Icons.camera_alt, color: Colors.blue),
                    const SizedBox(width: 20),
                    Text(
                      'Tirar Foto'.i18n,
                      style: theme.textTheme.bodyLarge,
                    ),
                  ],
                ),
              ),
            ),
            InkWell(
              onTap: () => _pickAndSavePhoto(context, ImageSource.gallery),
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  vertical: 8.0,
                  horizontal: 10,
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.image_search_outlined,
                      color: Colors.green,
                    ),
                    const SizedBox(width: 20),
                    Text(
                      'Buscar Foto'.i18n,
                      style: theme.textTheme.bodyLarge,
                    ),
                  ],
                ),
              ),
            ),
            if (controllerUsuario.foto !=
                "https://storage.empregare.com/pessoas/sem-foto.png")
              InkWell(
                onTap: () async {
                  if (!context.mounted) return;

                  final messenger = ScaffoldMessenger.maybeOf(context);
                  if (messenger == null) return;

                  Navigator.of(context).pop();

                  await controller.removeFoto();
                  await Future.wait([
                    controllerUsuario.load(),
                    controller.loadDadosPessoais(),
                  ]);
                  Modular.get<AppController>().updateProfile();

                  if (!context.mounted) return;

                  final updatedMessenger = ScaffoldMessenger.maybeOf(context);
                  if (updatedMessenger != null) {
                    updatedMessenger.showSnackBar(
                      const SnackBar(
                          content: Text('Foto removida com sucesso!')),
                    );
                  }
                },
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 8.0,
                    horizontal: 10,
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.delete_forever, color: Colors.red),
                      const SizedBox(width: 20),
                      Text(
                        'Remover Foto'.i18n,
                        style: theme.textTheme.bodyLarge
                            ?.copyWith(color: Colors.red),
                      ),
                    ],
                  ),
                ),
              ),
            Observer(
              builder: (_) {
                if (!controller.loading) return const SizedBox.shrink();
                return Padding(
                  padding: const EdgeInsets.only(top: 16.0),
                  child: Column(
                    children: [
                      const CircularProgressIndicator(),
                      const SizedBox(height: 8),
                      Text(
                        'Salvando mudanças...',
                        style: theme.textTheme.bodyMedium,
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
