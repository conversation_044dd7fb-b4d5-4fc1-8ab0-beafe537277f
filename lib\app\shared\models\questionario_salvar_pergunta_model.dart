const escolhaUnica = 'EscolhaUnica';
const respostaMultilinha = 'RespostaMultilinha';
const anexo = 'Anexo';
const video = 'Video';
const respostaCurta = 'RespostaCurta';
const escolhaMultipla = 'EscolhaMultipla';
const listaSuspensaMultipla = 'ListaSuspensaMultipla';
const listaSuspensa = 'ListaSuspensa';
const caixaSelecao = 'CaixaSelecao';

List<String> tiposDePerguntas = [
  escolhaUnica,
  respostaMultilinha,
  video,
  anexo,
  respostaCurta,
  listaSuspensaMultipla,
  listaSuspensa,
  caixaSelecao,
  escolhaMultipla,
];

class PerguntaSalvarModel {
  final String? token;
  final String? id;
  final int? totalPerguntas;
  final int? index;
  final int? tipo;
  final int? tempo;
  final List<int>? alternativas;
  final String? conteudo;

  PerguntaSalvarModel({
    this.token,
    this.id,
    this.totalPerguntas,
    this.index,
    this.tipo,
    this.tempo,
    this.alternativas,
    this.conteudo,
  });

  Map<String, dynamic> toJson() {
    final result = <String, dynamic>{};

    result.addAll({'token': token});
    result.addAll({'id': id});
    result.addAll({'totalPerguntas': totalPerguntas});
    result.addAll({'index': index});
    result.addAll({'tipo': tipo});
    result.addAll({'tempo': tempo});
    result.addAll({'alternativas': alternativas});
    result.addAll({'conteudo': conteudo});

    return result;
  }
}
