import 'package:flutter_modular/flutter_modular.dart';

import 'subscription_controller.dart';
import 'subscription_page.dart';

class SubscriptionModule extends Module {
  static const route = '/subscription';

  @override
  void binds(i) {
    i.addLazySingleton(SubscriptionController.new);
  }

  @override
  void routes(r) {
    r.child(
      Modular.initialRoute,
      child: (context) => SubscriptionPage(candidaturaID: r.args.data),
    );
  }
}
