import 'package:dio/dio.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../core/app_rest.dart';
import '../models/responses/simple_response_model.dart';

class SettingsRepository {
  final AppRest _rest = Modular.get();

  Future<SimpleResponseModel> postAlterarSenha(
      {required String? senhaAtual, required String? senhaNova}) async {
    try {
      final response = await _rest.post(
        '/candidato/configuracoes/alterar-senha',
        data: {
          "senhaAtual": senhaAtual,
          "novaSenha": senhaNova,
        },
      );
      return SimpleResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return SimpleResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return SimpleResponseModel.fromJson(err.response!.data);
    }
  }

  Future<SimpleResponseModel> postDesativarConta() async {
    try {
      final response = await _rest.post('/candidato/configuracoes/desativar-conta/true');
      return SimpleResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return SimpleResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return SimpleResponseModel.fromJson(err.response!.data);
    }
  }

  Future<SimpleResponseModel> deleteAccount() async {
    try {
      final response = await _rest.post('/candidato/configuracoes/excluir-conta');
      return SimpleResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return SimpleResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return SimpleResponseModel.fromJson(err.response!.data);
    }
  }
}
