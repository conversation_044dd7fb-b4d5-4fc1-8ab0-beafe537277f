import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:get/get.dart';

import '../../shared/core/app_translation.i18n.dart';
import '../../shared/mixins/loader_mixin.dart';
import '../../shared/models/estado_civil_model.dart';
import '../../shared/models/genero_model.dart';
import '../../shared/models/responses/cidade_response_model.dart';
import '../../shared/models/responses/estado_response_model.dart';
import '../../shared/models/responses/listar_cursos_response_model.dart';
import '../../shared/models/responses/listar_formacoes_response_model.dart';
import '../../shared/models/responses/listar_grau_academico_response_model.dart';
import '../../shared/models/responses/listar_pretensoes_response_model.dart';
import '../../shared/models/responses/pais_response_model.dart';
import '../../shared/models/responses/pessoa_response_model.dart';
import '../../shared/models/responses/simple_response_model.dart';
import '../../shared/models/salvar_pessoa_model.dart';
import '../../shared/repositories/academic_degree_repository.dart';
import '../../shared/repositories/cities_repository.dart';
import '../../shared/repositories/curriculum_repository.dart';
import '../imagecrop/imagecrop_module.dart';
import 'controllers/complementary_controller.dart';
import 'controllers/computing_controller.dart';
import 'controllers/deficiencies_controller.dart';
import 'controllers/diversities_controller.dart';
import 'controllers/experience_controller.dart';
import 'controllers/languages_controller.dart';
import 'controllers/objective_controller.dart';
import 'controllers/social_media_controller.dart';

class CurriculumController extends GetxController with LoaderMixin {
  final CurriculumRepository _repository = Modular.get();
  final CitiesRepository _citiesRepository = Modular.get();
  final AcademicDegreeRepository _academicDegreeRepository = Modular.get();

  final ObjectiveController objective = Modular.get();
  final LanguagesController languages = Modular.get();
  final SocialMediaController socialMedia = Modular.get();
  final DeficienciesController deficiencies = Modular.get();
  final DiversitiesController diversities = Modular.get();
  final ComputingController computing = Modular.get();
  final ComplementaryController complementary = Modular.get();
  final ExperienceController experience = Modular.get();

  List<GeneroModel> get generos => _generos;
  final List<GeneroModel> _generos = [
    GeneroModel(id: 'F', nome: 'Feminino'.i18n),
    GeneroModel(id: 'M', nome: 'Masculino'.i18n),
    GeneroModel(id: 'N', nome: 'Não-binário'.i18n),
    GeneroModel(id: 'O', nome: 'Outros'.i18n),
    GeneroModel(id: 'I', nome: 'Prefiro não informar'.i18n),
  ];

  final estadosCivis = [
    EstadoCivilModel(id: null, nome: 'Prefiro não informar'),
    EstadoCivilModel(id: 1, nome: 'Solteiro(a)'.i18n),
    EstadoCivilModel(id: 6, nome: 'Mora Junto (Amasiado)'.i18n),
    EstadoCivilModel(id: 2, nome: 'Casado(a)'.i18n),
    EstadoCivilModel(id: 3, nome: 'Divorciado(a)'.i18n),
    EstadoCivilModel(id: 4, nome: 'Viúvo(a)'.i18n),
    EstadoCivilModel(id: 5, nome: 'Outro'.i18n),
  ];

  List<PaisModel> _paises = <PaisModel>[];
  List<PaisModel> get paises => _paises;

  List<EstadoModel> _estados = <EstadoModel>[];
  List<EstadoModel> get estados => _estados;

  List<CidadeModel> _cidades = <CidadeModel>[];
  List<CidadeModel> get cidades => _cidades;

  SalvarPessoaModel? pessoaToSave;

  FormacaoModel? formacaoToSave;

  CursoModel? cursoToSave;

  bool loading = false;
  PessoaResponseModel? pessoa;

  /// Getter seguro para acessar pessoa sem usar operador de verificação nula
  PessoaResponseModel get pessoaSeguro => pessoa ?? PessoaResponseModel();

  List<GrauAcademicoModel>? grauAcademico = <GrauAcademicoModel>[];
  List<FormacaoModel>? formacoes = <FormacaoModel>[];
  List<CursoModel>? cursos = <CursoModel>[];

  List<CursoModel> get cursosSeguro => cursos ?? <CursoModel>[];
  List<FormacaoModel> get formacoesSeguro => formacoes ?? <FormacaoModel>[];

  bool loadingPaises = false;
  bool loadingEstados = false;
  bool loadingCidades = false;

  String? sintese = '';

  int? selectedCardIndex;

  selectCard(int index) {
    selectedCardIndex = index;
    update();
  }

  initializeSelectedCard(int? value) {
    if (value != null) {
      selectedCardIndex = value;
    } else {
      selectedCardIndex ??= 1;
    }
    update();
  }

  bool salvandoPrivacidade = false;

  PrivacidadeResponseModel? _privacidade;
  PrivacidadeResponseModel? get privacidade => _privacidade;

  int _privacy = 0;
  int get privacy => _privacy;

  setPrivacy(int value) {
    _privacy = value;
    update();
  }

  @override
  void onInit() {
    super.onInit();
    load();
  }

  Future<void> salvarPrivacidade(int tipo) async {
    try {
      salvandoPrivacidade = true;
      update();
      await _repository.salvarPrivacidade(_privacy);
      _privacidade = PrivacidadeResponseModel(tipo: _privacy);
    } finally {
      salvandoPrivacidade = false;
      update();
    }
  }

  Future load() async {
    try {
      changeLoading(true);
      log("LOAD");
      await Future.wait([
        loadDadosPessoais(),
        socialMedia.load(),
        deficiencies.load(),
        objective.load(),
        loadGrauAcademico(),
        loadFormacoes(),
        diversities.load(),
        experience.load(),
        loadCursos(),
        languages.load(),
        computing.load(),
        complementary.load(),
      ]);
    } finally {
      changeLoading(false);
    }
  }

  String getPerfilStatus() {
    String status = "Informe";

    final pessoaStatus = pessoa ?? PessoaResponseModel();
    if (pessoaStatus.cep == null) {
      status += " o CEP dos Dados Pessoais ";
    } else if ((socialMedia.redesSociais ?? []).isEmpty) {
      status += " uma Rede Social ";
    }
    if ((objective.areas ?? []).isEmpty) {
      status += " suas Áreas de Interesse em Objetivo ";
    } else if (sintese?.isEmpty ?? true) {
      status += " sua Síntese ";
    } else if ((formacoes ?? []).isEmpty) {
      status += " sua Formação Acadêmica ";
    } else if ((experience.experiencias ?? []).isEmpty) {
      status += " sua Experiência ";
    } else if ((diversities.diversidade != null)) {
      status += " sua Diversidade ";
    } else if ((cursos ?? []).isEmpty) {
      status += " seus Cursos Extracurriculares ";
    } else if ((languages.idiomas ?? []).isEmpty) {
      status += " os idiomas que você sabe ";
    } else if ((computing.informatica ?? []).isEmpty) {
      status += " alguma habilidade de Informática ";
    } else {
      status = "";
    }

    if (status.isNotEmpty) {
      status += "para completar o perfil.";
    }
    return status;
  }

  Future loadDadosPessoais() async {
    pessoa = (await _repository.getDadosPessoais()).pessoa;
    deficiencies.deficienciaTexto = pessoaSeguro.deficienciaTexto;
    sintese = pessoaSeguro.miniCurriculo;
    if (pessoaSeguro.pretensaoSalarial != null) {
      objective.pretensaoSalarialMax = pessoaSeguro.pretensaoSalarialMax;
      objective.pretensaoSalarialMin = pessoaSeguro.pretensaoSalarialMin;
      objective.moeda = pessoaSeguro.moedaSigla;

      objective.setPretensao(
        PretensaoModel(
          id: pessoaSeguro.pretensaoSalarialID.toString(),
          titulo: pessoaSeguro.pretensaoSalarial,
        ),
      );
    }
    update();
  }

  void showSnackbarErro(String message, BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        backgroundColor: Colors.redAccent,
        content: Text(message),
      ),
    );
  }

  Future getPaises({bool clean = true}) async {
    if (clean) {
      cleanEstado();
      cleanCidade();
    }
    loadingPaises = true;
    update();
    PaisResponseModel paisResponseModel = await _citiesRepository.getPaises();
    if (paisResponseModel.sucesso) {
      _paises = [
        PaisModel(id: null, nome: 'Não informado'),
        ...paisResponseModel.dados ?? <PaisModel>[]
      ];
    }
    loadingPaises = false;
    update();
  }

  void cleanEstado() {
    pessoaToSave!.estadoID = null;
    update();
  }

  Future getEstados({bool clean = true}) async {
    if (clean) {
      cleanEstado();
      cleanCidade();
    }
    loadingEstados = true;
    update();
    EstadoResponseModel estadoResponseModel =
        await _citiesRepository.getEstados(paisID: pessoaToSave?.paisID);
    if (estadoResponseModel.sucesso) {
      _estados = [
        EstadoModel(id: null, nome: 'Não informado'),
        ...estadoResponseModel.dados ?? <EstadoModel>[]
      ];
    }
    loadingEstados = false;
    update();
  }

  void cleanCidade() {
    pessoaToSave!.cidadeID = null;
    update();
  }

  Future getCidades({bool clean = true}) async {
    if (clean) cleanCidade();
    if (pessoaToSave?.estadoID == 0) return;
    loadingCidades = true;
    update();
    CidadeResponseModel cidadeResponseModel =
        await _citiesRepository.getCidades(estadoID: pessoaToSave?.estadoID);
    if (cidadeResponseModel.sucesso) {
      _cidades = [
        CidadeModel(id: null, nome: 'Não informado'),
        ...cidadeResponseModel.dados ?? <CidadeModel>[]
      ];
    }
    loadingCidades = false;
    update();
  }

  Future<SimpleResponseModel> saveDadosPessoais() async {
    changeLoading(true);
    try {
      return await _repository.saveDadosPessoais(pessoaToSave!);
    } finally {
      changeLoading(false);
    }
  }

  Future<SimpleResponseModel> saveFoto(File? foto) async {
    SimpleResponseModel response;
    changeLoading(true);
    try {
      final result =
          await Modular.to.pushNamed(ImagecropModule.route, arguments: foto);

      response = (await _repository.saveFoto(foto: result as File));
    } finally {
      changeLoading(false);
    }
    return response;
  }

  Future<SimpleResponseModel> removeFoto() async {
    SimpleResponseModel response;
    changeLoading(true);
    try {
      response = (await _repository.deleteFoto());
    } finally {
      changeLoading(false);
    }
    return response;
  }

  Future<bool> saveSintese() async {
    bool response = false;
    changeLoading(true);
    try {
      response = (await _repository.saveSintese(sintese));
    } finally {
      changeLoading(false);
    }
    return response;
  }

  Future loadGrauAcademico() async {
    grauAcademico =
        (await _academicDegreeRepository.getGrauAcademico()).grauAcademico;
  }

  Future<void> reloadSection(String section) async {
    try {
      switch (section) {
        case 'idiomas':
          await languages.loadIdiomas();
          break;
        case 'formacoes':
          await loadFormacoes();
          break;
        case 'cursos':
          await loadCursos();
          break;
        case 'experiencias':
          await experience.loadExperiencias();
          break;
        case 'informatica':
          await computing.loadInformatica();
          break;
        case 'complementares':
          await complementary.loadInformacoesComplementares();
          break;
        case 'redes-sociais':
          await socialMedia.loadRedesSociais();
          break;
        case 'deficiencias':
          await deficiencies.loadDeficiencias();
          break;
        case 'diversidades':
          await diversities.loadDiversidade();
          break;
        case 'objetivos':
          await objective.loadObjetivos();
          break;
        default:
          await load();
          break;
      }
      update();
    } catch (e) {
      log('Erro ao recarregar seção $section: $e');
    }
  }

  /// Recarrega múltiplas seções do currículo
  Future<void> reloadSections(List<String> sections) async {
    for (String section in sections) {
      await reloadSection(section);
    }
  }

  Future loadFormacoes() async {
    final response = await _repository.getFormacoes();
    formacoes = response.dados ?? <FormacaoModel>[];
    update();
  }

  Future<SimpleResponseModel> saveFormacaoAcademica() async {
    SimpleResponseModel response;
    changeLoading(true);
    try {
      response = (await _repository.saveFormacao(formacaoToSave!));
      if (response.sucesso) {
        await reloadSection('formacoes');
      }
    } on DioException catch (err) {
      throw err.message ?? '';
    } finally {
      changeLoading(false);
    }
    return response;
  }

  Future<bool> deleteFormacaoAcademica(int? id) async {
    bool response = false;
    changeLoading(true);
    try {
      response = (await _repository.deleteFormacao(id));
      if (response) {
        await reloadSection('formacoes');
      }
    } finally {
      changeLoading(false);
    }
    return response;
  }

  Future loadCursos() async {
    final response = await _repository.getCursos();
    cursos = response.dados ?? <CursoModel>[];
    update();
  }

  Future<SimpleResponseModel> saveCurso() async {
    SimpleResponseModel response;
    changeLoading(true);
    try {
      response = (await _repository.saveCurso(cursoToSave!));
      if (response.sucesso) {
        await reloadSection('cursos');
      }
    } on DioException catch (err) {
      throw err.message ?? '';
    } finally {
      changeLoading(false);
    }
    return response;
  }

  Future<bool> deleteCurso(int? id) async {
    bool response = false;
    changeLoading(true);
    try {
      response = (await _repository.deleteCurso(id));
      if (response) {
        await reloadSection('cursos');
      }
    } finally {
      changeLoading(false);
    }
    return response;
  }
}
