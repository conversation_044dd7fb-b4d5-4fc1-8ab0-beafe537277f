class InformaticaResponseModel {
  final bool sucesso;
  final String mensagem;
  final List<InformaticaModel>? dados;

  InformaticaResponseModel(
      {this.sucesso = false, this.mensagem = "", this.dados});

  factory InformaticaResponseModel.fromJson(Map<String, dynamic> json) {
    return InformaticaResponseModel(
      sucesso: json['sucesso'] ?? false,
      mensagem: json['mensagem'] ?? "",
      dados: (json['dados'] as List<dynamic>?)
          ?.map((e) => InformaticaModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class InformaticaModel {
  static const int basico = 1;
  static const int intermediario = 2;
  static const int avancado = 3;

  final int? id;
  final String? nome;
  final int? pessoaID;
  final int? nivel;

  InformaticaModel({this.id, this.nome, this.pessoaID, this.nivel});

  factory InformaticaModel.fromJson(Map<String, dynamic> json) {
    return InformaticaModel(
      id: json['id'] as int?,
      nome: json['nome'] as String?,
      pessoaID: json['pessoaID'] as int?,
      nivel: json['nivel'] as int?,
    );
  }
}
