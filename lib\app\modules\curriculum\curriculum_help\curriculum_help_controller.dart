import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../shared/libs/file_picker_lib.dart';
import '../../../shared/repositories/curriculum_repository.dart';
import '../curriculum_module.dart';

class CurriculumHelpController with ChangeNotifier {
  final CurriculumRepository _repository = Modular.get();

  bool loading = false;
  void changeLoading(bool value) {
    loading = value;
    notifyListeners();
  }

  Future<void> selectCurriculum(Function showError) async {
    changeLoading(true);
    var result = await FilePickerLib.pickFile(
      allowCompression: false,
      allowedExtensions: ['docx', 'pdf', 'jpg'],
    );
    changeLoading(false);

    if (result == null) {
      showError("Nenhum documento selecionado!");
      return;
    }

    changeLoading(true);
    var response = await _repository.upload(file: result);
    changeLoading(false);

    if (response.sucesso) {
      Modular.to.pushReplacementNamed(
        CurriculumModule.route,
        arguments: true,
      );
    } else {
      showError(response.mensagem);
    }
  }
}
