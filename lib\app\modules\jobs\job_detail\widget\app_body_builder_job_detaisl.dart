import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../../shared/widgets/app_html.dart';
import '../../../curriculum/curriculum_module.dart';
import '../job_detail_controller.dart';
import 'shimmer_details_job.dart';
import 'widget_description_new.dart';
import 'widget_info_column.dart';
import 'widget_related_jobs.dart';

class AppBodyBuilderJobDetails extends StatefulWidget {
  const AppBodyBuilderJobDetails({
    super.key,
    required this.jobId,
    this.bottomSheetOffset,
  });

  final int jobId;
  final double? bottomSheetOffset;

  @override
  State<AppBodyBuilderJobDetails> createState() =>
      _AppBodyBuilderJobDetailsState();
}

class _AppBodyBuilderJobDetailsState extends State<AppBodyBuilderJobDetails> {
  Future<dynamic>? future;

  final controllerJob = Modular.get<JobDetailController>();

  @override
  void initState() {
    future = controllerJob.getJobDetails(widget.jobId);
    super.initState();
  }

  List<String> _getBeneficiosList(String beneficios) {
    return beneficios.split(',').map((b) => b.trim()).toList();
  }

  bool get incompleteCV {
    return controllerJob.subscription?.retorno == "CurriculoIncompleto";
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: future,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting &&
            !snapshot.hasData) {
          return const ShimmerDetailsJob();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: IntrinsicWidth(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    DecoratedBox(
                      decoration: BoxDecoration(
                        color: const Color(0xFFF5F5F5),
                        borderRadius: BorderRadius.circular(500),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 10,
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Image.asset(
                              'lib/assets/icons/briefcase-job.png',
                              scale: 2,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              controllerJob.jobDetails?.nivel ?? '',
                              style: const TextStyle(
                                color: Colors.black,
                                fontSize: 10,
                                fontFamily: 'Inter',
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    DecoratedBox(
                      decoration: BoxDecoration(
                        color: const Color(0xFFF5F5F5),
                        borderRadius: BorderRadius.circular(500),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 10,
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Image.asset(
                              'lib/assets/icons/money.png',
                              scale: 2,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              controllerJob.jobDetails?.salario?.isNotEmpty ??
                                      false
                                  ? controllerJob.jobDetails?.salario ?? ''
                                  : 'Salário não informado',
                              style: const TextStyle(
                                color: Colors.black,
                                fontSize: 10,
                                fontFamily: 'Inter',
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            if (incompleteCV)
              Container(
                margin: const EdgeInsets.only(top: 16),
                padding: const EdgeInsets.only(
                  top: 16,
                  left: 16,
                  right: 16,
                  bottom: 10,
                ),
                decoration: BoxDecoration(
                  color: Colors.orange.shade100,
                  border: Border.all(color: Colors.orange.shade200),
                  borderRadius: BorderRadius.circular(5),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      "Currículo Incompleto",
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 18,
                      ),
                    ),
                    const Text(
                      "Bloqueamos sua inscrição na vaga porque seu currículo está incompleto. Efetue as mudanças solicitadas para se inscrever nas vagas de nosso site.",
                    ),
                    MaterialButton(
                      color: Colors.orange,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(5),
                      ),
                      elevation: 0,
                      child: const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.insert_drive_file_outlined,
                            size: 15,
                            color: Colors.white,
                          ),
                          SizedBox(width: 5),
                          Text(
                            "Completar Currículo!",
                            style: TextStyle(
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                      onPressed: () {
                        Modular.to
                            .pushNamed(CurriculumModule.route)
                            .whenComplete(
                          () async {
                            await controllerJob.verificarInscricao();
                            setState(() {});
                          },
                        );
                      },
                    ),
                  ],
                ),
              ),
            const SizedBox(height: 16),
            SizedBox(
              height: heightCidades(),
              child: ListView.builder(
                shrinkWrap: true,
                clipBehavior: Clip.antiAlias,
                scrollDirection: Axis.vertical,
                itemCount: controllerJob.jobDetails?.cidades?.length,
                itemBuilder: (_, index) {
                  return Row(
                    children: [
                      Image.asset(
                        'lib/assets/icons/location-minus.png',
                        scale: 2,
                      ),
                      const SizedBox(width: 10),
                      Text(
                        controllerJob.jobDetails?.cidades?[index].nome ?? '',
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
            InfoColumn(
              time: controllerJob.jobDetails?.horario,
              typeContract: controllerJob.jobDetails?.contratacao,
              workModel: controllerJob.jobDetails?.modalidadeTitulo,
              level: controllerJob.jobDetails?.nivel,
            ),
            const SizedBox(height: 16),
            WidgetDescripitionNew(controllerJob: controllerJob),
            if (controllerJob.jobDetails?.requisito != '')
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Requisitos',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                      fontFamily: 'Inter',
                    ),
                  ),
                  AppHtml(
                    data: controllerJob.jobDetails?.requisito ?? '',
                  ),
                ],
              ),
            if (controllerJob.jobDetails?.beneficio != '')
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Benefícios',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                      fontFamily: 'Inter',
                    ),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8.0,
                    runSpacing: 8.0,
                    children: _getBeneficiosList(
                            controllerJob.jobDetails?.beneficio ?? '')
                        .map(
                          (beneficio) => Container(
                            padding: const EdgeInsets.symmetric(
                                vertical: 8, horizontal: 12),
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: const Color(0xFF9E9EEF),
                              ),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Text(
                              beneficio,
                              style: const TextStyle(
                                color: Color(0xFF1B1B63),
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                fontFamily: 'Inter',
                              ),
                            ),
                          ),
                        )
                        .toList(),
                  ),
                ],
              ),
            const SizedBox(height: 16),
            const Column(
              children: [Text('')],
            ),
            if (controllerJob.jobDetails?.vagasRelacionadas?.isNotEmpty ??
                false)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Vagas relacionadas',
                    style: TextStyle(
                      fontSize: 18,
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  WidgetRelatedJobs(
                    onUpdate: () {
                      setState(() {});
                    },
                  ),
                ],
              )
          ],
        );
      },
    );
  }

  double heightCidades() {
    if (controllerJob.jobDetails?.cidades?.length != 3) {
      if (controllerJob.mostrarMaisCidades) {
        return (controllerJob.jobDetails!.cidades!.length + 1) * 14.0;
      } else {
        return 56.0;
      }
    }

    return controllerJob.jobDetails!.cidades!.length * 15.0;
  }
}
