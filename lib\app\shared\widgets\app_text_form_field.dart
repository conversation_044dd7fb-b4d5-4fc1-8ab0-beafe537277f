import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../core/app_config.dart';

class AppTextFormField extends StatefulWidget {
  final TextStyle? style;
  final Widget? suffixIcon;
  final int maxLines;
  final int? maxLength;
  final int? minLines;
  final String hintText;
  final double? hintFontSize;
  final TextEditingController? controller;
  final bool readOnly;
  final bool enabled;
  final bool isPassword;
  final FocusNode? focusNode;
  final bool optional;
  final List<TextInputFormatter>? inputFormatters;
  final String? Function(String?)? validator;
  final TextInputType? keyboardType;
  final Function()? onTap;
  final Function(String)? onFieldSubmitted;
  final Function(String)? onChanged;
  final Widget? prefixIcon;
  final TextInputAction textInputAction;
  final Color? fillColor;
  final Color? hintStyleColor;
  final double? radius;
  final double? horizontalPadding;
  final bool autofocus;
  final String? initialText;
  final String? suffixText;

  const AppTextFormField({
    super.key,
    this.style,
    this.suffixIcon,
    this.maxLines = 1,
    this.maxLength,
    this.minLines,
    required this.hintText,
    this.hintFontSize,
    this.controller,
    this.readOnly = false,
    this.enabled = true,
    this.isPassword = false,
    this.focusNode,
    this.optional = false,
    this.inputFormatters,
    this.validator,
    this.keyboardType,
    this.onTap,
    this.onFieldSubmitted,
    this.onChanged,
    this.prefixIcon,
    this.textInputAction = TextInputAction.next,
    this.fillColor = Colors.white,
    this.hintStyleColor,
    this.radius,
    this.horizontalPadding,
    this.autofocus = false,
    this.initialText,
    this.suffixText,
  });

  @override
  State<AppTextFormField> createState() => _AppTextFormFieldState();
}

class _AppTextFormFieldState extends State<AppTextFormField> {
  bool obscureText = true;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          minLines: widget.minLines,
          autofocus: widget.autofocus,
          initialValue: widget.initialText,
          onChanged: widget.onChanged,
          style: widget.style,
          keyboardType: widget.keyboardType,
          maxLines: widget.maxLines,
          focusNode: widget.focusNode,
          onTap: widget.onTap,
          textInputAction: widget.textInputAction,
          readOnly: widget.readOnly,
          enabled: widget.enabled,
          onFieldSubmitted: widget.onFieldSubmitted,
          controller: widget.controller,
          obscureText: !widget.isPassword ? false : obscureText,
          inputFormatters: widget.inputFormatters,
          maxLength: widget.maxLength,
          validator: widget.validator ??
              (value) {
                if (widget.optional == false &&
                    (value?.trim().isEmpty ?? true)) {
                  return 'Campo obrigatório.';
                }
                return null;
              },
          decoration: InputDecoration(
            filled: true,
            suffixText: widget.suffixText,
            fillColor: widget.fillColor,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 10,
            ),
            suffixIcon: widget.isPassword
                ? IconButton(
                    padding: const EdgeInsets.only(right: 22),
                    icon: Icon(
                      obscureText
                          ? Icons.visibility_off_outlined
                          : Icons.visibility_outlined,
                      color: const Color(0xff292d32),
                    ),
                    onPressed: () {
                      obscureText = !obscureText;
                      setState(() {});
                    },
                  )
                : widget.suffixIcon,
            suffixIconConstraints: const BoxConstraints(maxWidth: 30),
            prefixIcon: widget.prefixIcon,
            hintText: widget.hintText,
            hintStyle: TextStyle(
              fontSize: widget.hintFontSize ?? 14,
              fontFamily: 'Inter',
              fontWeight: FontWeight.w400,
              color: widget.hintStyleColor,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(widget.radius ?? 8),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(widget.radius ?? 8),
              borderSide:
                  const BorderSide(color: Color(0xffeeeff3), width: 1.8),
            ),
            disabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(widget.radius ?? 8),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(widget.radius ?? 8),
              borderSide: const BorderSide(
                color: AppConfig.colorPrimary,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(widget.radius ?? 8),
              borderSide: const BorderSide(
                color: Colors.red,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
