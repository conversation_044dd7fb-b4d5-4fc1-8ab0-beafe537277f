import '../helpers/timezone_helper.dart';

class TimeActivityUtils {
  static final Map<String, int> meses = {
    'janeiro': 1,
    'fevereiro': 2,
    'março': 3,
    'abril': 4,
    'maio': 5,
    'junho': 6,
    'julho': 7,
    'agosto': 8,
    'setembro': 9,
    'outubro': 10,
    'novembro': 11,
    'dezembro': 12
  };

  static DateTime parseData(String dataString) {
    List<String> partes = dataString.split(', ');
    List<String> diaMes = partes[1].split('/');
    int dia = int.parse(diaMes[0]);
    int mes = meses[diaMes[1]]!;
    return DateTime(TimezoneHelper.instance.now().year, mes, dia);
  }

  static String calcularDiferenca(DateTime dataFornecida, DateTime dataAtual) {
    Duration diferenca = dataAtual.difference(dataFornecida);
    int dias = diferenca.inDays;
    int meses = (dias / 30).floor();
    int anos = (dias / 365).floor();

    if (anos > 0) {
      return "$anos ano${anos > 1 ? 's' : ''}";
    } else if (meses > 0) {
      return "$meses mes${meses > 1 ? 'es' : ''}";
    } else if (dias > 0) {
      return "$dias dia${dias > 1 ? 's' : ''}";
    } else {
      return "menos de 1 dia";
    }
  }

  static String calcularTempoAtiva(String dataString) {
    if (dataString.isEmpty) return "Não informado";

    DateTime dataFornecida = parseData(dataString);
    DateTime dataAtual = TimezoneHelper.instance.now();

    if (dataFornecida.isAfter(dataAtual)) {
      dataFornecida = DateTime(
          dataFornecida.year - 1, dataFornecida.month, dataFornecida.day);
    }

    return calcularDiferenca(dataFornecida, dataAtual);
  }
}
