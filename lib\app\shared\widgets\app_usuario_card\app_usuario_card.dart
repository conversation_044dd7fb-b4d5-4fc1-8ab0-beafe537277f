import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';
import 'package:shimmer/shimmer.dart';

import '../../core/app_config.dart';
import '../../core/app_translation.i18n.dart';
import 'app_usuario_card_controller.dart';

class AppUsuarioCard extends StatefulWidget {
  final Function? onEdit;

  const AppUsuarioCard({super.key, this.onEdit});

  @override
  _AppUsuarioCardState createState() => _AppUsuarioCardState();
}

class _AppUsuarioCardState extends State<AppUsuarioCard> {
  final controller = Modular.get<AppUsuarioCardController>();

  @override
  void initState() {
    super.initState();
    controller.load();
  }

  @override
  Widget build(BuildContext context) {
    return Observer(
      builder: (_) {
        if (controller.loading) {
          return Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            child: const Padding(
              padding: EdgeInsets.all(15),
              child: Row(
                children: <Widget>[
                  ColoredBox(
                    color: Colors.white,
                    child: SizedBox(width: 62, height: 62.0),
                  ),
                  SizedBox(width: 20),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        ColoredBox(
                          color: Colors.white,
                          child: SizedBox(
                            height: 10,
                            width: double.maxFinite,
                          ),
                        ),
                        SizedBox(height: 20),
                        ColoredBox(
                          color: Colors.white,
                          child: SizedBox(
                            height: 10,
                            width: double.maxFinite,
                          ),
                        ),
                        SizedBox(height: 10),
                        ColoredBox(
                          color: Colors.white,
                          child: SizedBox(
                            height: 16.0,
                            width: double.maxFinite,
                          ),
                        ),
                      ],
                    ),
                  )
                ],
              ),
            ),
          );
        }

        return Padding(
          padding: const EdgeInsets.all(15),
          child: Row(
            children: <Widget>[
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.withValues(alpha: 0.4)),
                ),
                height: 60,
                width: 60,
                child: controller.foto != null && controller.foto!.isNotEmpty
                    ? CachedNetworkImage(
                        imageUrl: controller.foto!,
                        imageBuilder: (context, imageProvider) {
                          return Container(
                            decoration: BoxDecoration(
                              image: DecorationImage(
                                image: imageProvider,
                                fit: BoxFit.fitWidth,
                                alignment: Alignment.center,
                              ),
                            ),
                          );
                        },
                        placeholder: (context, url) => SpinKitCircle(
                          color: Colors.blue[300],
                        ),
                        errorWidget: (context, url, error) => const Icon(
                          Icons.broken_image,
                          size: 60,
                        ),
                      )
                    : const Icon(
                        Icons.person,
                        size: 60,
                        color: Colors.grey,
                      ),
              ),
              const SizedBox(width: 15),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        Expanded(
                          child: Text(
                            controller.nome ?? '',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        Visibility(
                          visible: widget.onEdit != null,
                          child: InkWell(
                            onTap: widget.onEdit as void Function()?,
                            child: Text(
                              'EDITAR'.i18n,
                              style: const TextStyle(
                                color: AppConfig.colorPrimary,
                              ),
                            ),
                          ),
                        )
                      ],
                    ),
                    const SizedBox(height: 10),
                    Text(
                      '${controller.progresso} % ${'PERFIL COMPLETADO'.i18n}',
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w300,
                      ),
                    ),
                    const SizedBox(height: 5),
                    Observer(
                      builder: (_) => LinearPercentIndicator(
                        animation: true,
                        animationDuration: 2000,
                        lineHeight: 12.0,
                        percent: controller.progresso / 100.0,
                        backgroundColor: const Color(0xFFE2E8ED),
                        progressColor: const Color(0xFF64CB6B),
                        padding: const EdgeInsets.symmetric(horizontal: 5),
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
        );
      },
    );
  }
}
