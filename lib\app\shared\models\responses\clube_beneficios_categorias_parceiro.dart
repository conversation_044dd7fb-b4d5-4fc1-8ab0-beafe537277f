class ClubeBeneficiosCategoriasParceiroModel {
  bool sucesso;
  List<ParceiroModel> dados;

  ClubeBeneficiosCategoriasParceiroModel(
      {required this.sucesso, required this.dados});

  factory ClubeBeneficiosCategoriasParceiroModel.fromJson(
      Map<String, dynamic> json) {
    return ClubeBeneficiosCategoriasParceiroModel(
      sucesso: json['sucesso'],
      dados: List<ParceiroModel>.from(
          json['dados'].map((dado) => ParceiroModel.fromJson(dado))),
    );
  }
}

class ParceiroModel {
  int id;
  String nome;

  ParceiroModel({required this.id, required this.nome});

  factory ParceiroModel.fromJson(Map<String, dynamic> json) {
    return ParceiroModel(
      id: json['id'],
      nome: json['nome'],
    );
  }
}
