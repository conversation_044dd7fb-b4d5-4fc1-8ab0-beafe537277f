import 'package:empregare_app/app/shared/mixins/loader_mixin.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:get/get.dart';

import '../../models/responses/profile_response_model.dart';
import '../../repositories/curriculum_repository.dart';

class AppUsuarioCardController extends GetxController with LoaderMixin {
  final CurriculumRepository _repository = Modular.get();

  ProfileResponseModel? _profile;

  int get progresso => _profile?.progresso?.floor() ?? 0;

  String? get nome => _profile?.nome ?? '';

  String? get foto => _profile?.foto ?? '';

  Future<void> load() async {
    try {
      changeLoading(true);
      _profile = (await _repository.getProgressoResumido());
    } finally {
      changeLoading(false);
    }
  }
}
