import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../core/app_rest.dart';
import '../core/app_rest_upload.dart';
import '../models/prepare_metadata_model.dart';
import '../models/questionario_salvar_pergunta_model.dart';
import '../models/responses/pergunta_response_model.dart';
import '../models/responses/prepare_metadata_response_response_model.dart';
import '../models/responses/questionario_pendente_response_model.dart';
import '../models/responses/questionario_response_model.dart';
import '../models/responses/questionario_salvar_pergunta_response_model.dart';
import '../models/responses/upload_block_response_model.dart';

class QuestionnaireRepository {
  final AppRest _rest = Modular.get();
  final AppRestUpload _upload = Modular.get();

  Future<QuestionarioPendenteResponseModel> getPendentes() async {
    try {
      final response = await _rest.get('/candidato/questionario/pendentes');
      return QuestionarioPendenteResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return QuestionarioPendenteResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return QuestionarioPendenteResponseModel.fromJson(err.response!.data);
    }
  }

  Future<QuestionarioResponseModel> getQuestionario(int? id) async {
    try {
      final response = await _rest.get(
        '/candidato/questionario/detalhes/$id',
      );

      if (response.data is String) {
        throw response.data;
      }

      return QuestionarioResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return QuestionarioResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return QuestionarioResponseModel.fromJson(err.response!.data);
    } catch (err) {
      rethrow;
    }
  }

  Future<PerguntaResponseModel> getPergunta(
      {String? token, int? index, bool? iniciou}) async {
    try {
      final response = await _rest
          .get('/candidato/questionario/pergunta/listar', queryParameters: {
        'token': token,
        'index': index,
        'iniciou': iniciou
      });
      return PerguntaResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return PerguntaResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return PerguntaResponseModel.fromJson(err.response!.data);
    }
  }

  Future<PerguntaSalvarResponseModel> postPergunta(
      PerguntaSalvarModel model) async {
    try {
      final response = await _rest.post(
          '/candidato/questionario/pergunta/salvar',
          data: model.toJson());
      return PerguntaSalvarResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return PerguntaSalvarResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return PerguntaSalvarResponseModel.fromJson(err.response!.data);
    }
  }

  Future<PrepareMetadataResponseModel> postPrepareMetadata(
      PrepareMetadataModel model) async {
    try {
      final response = await _rest.post(
          '/candidato/questionario/prepare-metadata',
          data: model.toJson());
      return PrepareMetadataResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return PrepareMetadataResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return PrepareMetadataResponseModel.fromJson(err.response!.data);
    }
  }

  Future<UploadBlockResponseModel> postUploadBlock(
      {required int id, required String? idUpload, required File file}) async {
    try {
      FormData formData = FormData.fromMap({
        "Slice": await MultipartFile.fromFile(file.path,
            filename: file.path.split('/').last),
      });
      final response = await _upload.post(
          '/candidato/questionario/upload-block/$id/$idUpload',
          data: formData);
      return UploadBlockResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return UploadBlockResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return UploadBlockResponseModel.fromJson(err.response!.data);
    }
  }

  Future<bool?> postFinalizar(String? token) async {
    try {
      final response =
          await _rest.post('/candidato/questionario/finalizar/$token');
      return response.data['sucesso'] as bool?;
    } on DioException catch (err) {
      throw err.message ?? '';
    }
  }
}
