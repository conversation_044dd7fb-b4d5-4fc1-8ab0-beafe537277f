import 'package:empregare_app/app/shared/mixins/loader_mixin.dart';
import 'package:get/get.dart';

import '../../../shared/models/filhos_model.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:i18n_extension/default.i18n.dart';
import 'package:mobx/mobx.dart';

import '../../../shared/models/responses/listar_complementar_response_model.dart';
import '../../../shared/models/salvar_complementar_model.dart';
import '../../../shared/repositories/curriculum_repository.dart';
import '../curriculum_controller.dart';

class ComplementaryController extends GetxController with LoaderMixin {
  final CurriculumRepository _repository = Modular.get();

  SalvarComplementarModel? complementarToSave;

  List<ComplementarModel>? complementares = <ComplementarModel>[];
  Future loadInformacoesComplementares() async {
    final response = await _repository.getComplementar();
    complementares = response.dados;
  }

  Future<void> reloadFromCurriculumController() async {
    try {
      final curriculumController = Modular.get<CurriculumController>();
      await curriculumController.reloadSection('complementares');
    } catch (e) {
      await loadInformacoesComplementares();
    }
  }

  @action
  Future<bool> saveComplementar() async {
    bool response = false;

    try {
      changeLoading(true);
      response = (await _repository.saveComplementar(complementarToSave!));
      if (response) {
        await reloadFromCurriculumController();
      }
    } finally {
      changeLoading(false);
    }
    return response;
  }

  Future load() async {
    try {
      changeLoading(true);

      await Future.wait([
        loadInformacoesComplementares(),
      ]);
    } finally {
      changeLoading(false);
    }
  }

  final filhos = [
    FilhosModel(id: 0, nome: 'Nenhum filho'.i18n),
    FilhosModel(id: 1, nome: '%d filho'.i18n.fill([1])),
    FilhosModel(id: 2, nome: '%d filhos'.i18n.fill([2])),
    FilhosModel(id: 3, nome: '%d filhos'.i18n.fill([3])),
    FilhosModel(id: 4, nome: '%d filhos'.i18n.fill([4])),
    FilhosModel(id: 5, nome: '%d filhos'.i18n.fill([5])),
    FilhosModel(id: 6, nome: 'Mais de %d filhos'.i18n.fill([5])),
  ];
}
