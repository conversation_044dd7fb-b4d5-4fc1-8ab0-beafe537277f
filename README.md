# empregare_app

'A new Flutter project. Created by <PERSON><PERSON><PERSON>'

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://flutter.dev/docs/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://flutter.dev/docs/cookbook)

For help getting started with Flutter, view our
[online documentation](https://flutter.dev/docs), which offers tutorials,
samples, guidance on mobile development, and a full API reference.

plugins

add_2_calendar
<key>NSCalendarsUsageDescription</key>
<string>INSERT_REASON_HERE</string>
<key>NSContactsUsageDescription</key>
<string>INSERT_REASON_HERE</string>

rate_my_app
<key>LSApplicationQueriesSchemes</key>
<array>
<string>itms</string>
</array>

@Marcelo Castro criei um usuário no sistema para você conseguir resetar as respostas do questionário:
url: https://corporate.empregare.com
usuário: <EMAIL>
senha: 123456

Universal links
https://www.cognizantsoftvision.com/blog/ios-universal-links/

SizedBox(
height: 60,
child: Container(
padding: EdgeInsets.only(bottom: 7),
decoration: BoxDecoration(
color: Colors.white,
boxShadow: [
BoxShadow(
color: Colors.grey.withValues(alpha:0.1),
offset: Offset(0.0, -7.0),
blurRadius: 10.0,
spreadRadius: 0.0)
],
),
child: TabBar(
tabs: [
Tab(
//iconMargin: EdgeInsets.all(5),
icon: Observer(
builder: (_) {
return Stack(
children: <Widget>[
Padding(
padding: const EdgeInsets.symmetric(
horizontal: 8.0),
child: Icon(
Icons.notifications,
color: controller.tabSelected == 0
? AppConfig.colorPrimary
: Colors.grey,
),
),
if (controller.notifications > 0)
Positioned(
right: 5,
child: Container(
height: 15,
width: 15,
decoration: new BoxDecoration(
color: Colors.red,
borderRadius: BorderRadius.circular(50),
),
child: Center(
child:
Text('${controller.notifications}')
.setFontSize(10)
.white()
.center(),
),
),
),
],
);
},
),
child: FittedBox(
child: Text('Notificações'.i18n).setFontSize(10),
)),
Tab(
//iconMargin: EdgeInsets.all(5),
icon: Observer(
builder: (_) => Icon(
Icons.work,
color: controller.tabSelected == 1
? AppConfig.colorPrimary
: Colors.grey,
),
),
child: FittedBox(
child: Text('Vagas'.i18n).setFontSize(10),
),
),
Tab(
//iconMargin: EdgeInsets.all(5),
icon: Observer(
builder: (_) => Icon(
Icons.message,
color: controller.tabSelected == 2
? AppConfig.colorPrimary
: Colors.grey,
),
),
child: FittedBox(
child: Text('Mensagens'.i18n).setFontSize(10),
),
),
Tab(
//iconMargin: EdgeInsets.all(5),
icon: Observer(
builder: (_) => Icon(
Icons.subscriptions,
color: controller.tabSelected == 3
? AppConfig.colorPrimary
: Colors.grey,
),
),
child: FittedBox(
child: Text('Inscrições'.i18n).setFontSize(10),
)),
Tab(
//iconMargin: EdgeInsets.all(5),
icon: Observer(
builder: (\_) => Icon(
Icons.person_pin,
color: controller.tabSelected == 4
? AppConfig.colorPrimary
: Colors.grey,
),
),
child: FittedBox(
child: Text('Perfil'.i18n).setFontSize(10),
))
],
controller: \_tabController,
labelColor: AppConfig.colorPrimary,
unselectedLabelColor: Colors.grey[600],
indicatorColor: AppConfig.colorPrimary,
),
),
)

empregare

#verificar arvore de dependencias
flutter pub deps

41810839858
123456

Flutter 2.2.3 • channel stable • https://github.com/flutter/flutter.git
Framework • revision f4abaa0735 (1 year, 4 months ago) • 2021-07-01 12:46:11 -0700
Engine • revision 241c87ad80
Tools • Dart 2.13.4


