import 'package:flutter_modular/flutter_modular.dart';

import 'chat_controller.dart';
import 'chat_page.dart';

class ChatModule extends Module {
  static const route = '/chat';

  @override
  void binds(i) {
    i.addLazySingleton(ChatController.new);
  }

  @override
  void routes(r) {
    r.child(
      Modular.initialRoute,
      child: (context) => ChatPage(
        title: r.args.data['title'],
        subTitle: r.args.data['subTitle'],
        chatID: r.args.data['chatID'],
      ),
    );
  }
}
