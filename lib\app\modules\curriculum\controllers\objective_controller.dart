import 'package:dio/dio.dart';
import 'package:empregare_app/app/shared/mixins/loader_mixin.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:get/get.dart';

import '../../../shared/models/responses/listar_areas_response_model.dart';
import '../../../shared/models/responses/listar_objetivos_response_model.dart';
import '../../../shared/models/responses/listar_pretensoes_response_model.dart';
import '../../../shared/models/responses/simple_response_model.dart';
import '../../../shared/models/salvar_objetivo_model.dart';
import '../../../shared/repositories/curriculum_repository.dart';
import '../../../shared/repositories/positions_repository.dart';

class ObjectiveController extends GetxController with LoaderMixin {
  final CurriculumRepository _repository = Modular.get();
  final PositionsRepository _positionsRepository = Modular.get();

  Future<void> load() async {
    try {
      changeLoading(true);
      await Future.wait([
        loadPretensoes(),
        loadAreasInteresse(),
        loadObjetivos(),
      ]);
    } finally {
      changeLoading(false);
    }
  }

  List<AreaModel>? areas = <AreaModel>[];

  List<AreaModel> get areasSeguro => areas ?? <AreaModel>[];

  PretensaoModel? pretensaoSalarial;

  setPretensao(PretensaoModel pretensao) => pretensaoSalarial = pretensao;

  List<AreaModel> areasInteresseToSave = <AreaModel>[];

  String? moeda = "R\$";

  void setMoeda(String? value) {
    moeda = value;
    update();
  }

  int? pretensaoSalarialMin;

  int? pretensaoSalarialMax;

  Future<SimpleResponseModel> saveObjetivo() async {
    SimpleResponseModel response;
    changeLoading(true);
    try {
      SalvarObjetivoModel salvarObjetivoModel = SalvarObjetivoModel(
        objetivoAreas: areasInteresseToSave.map((a) => a.id).toList(),
        pretensaoSalarial: int.parse(pretensaoSalarial?.id ?? '0'),
        moeda: moeda,
        pretensaoSalarialMin: pretensaoSalarialMin,
        pretensaoSalarialMax: pretensaoSalarialMax,
      );
      response = (await _repository.saveObjetivo(salvarObjetivoModel));
    } on DioException catch (err) {
      throw err.message ?? '';
    } finally {
      changeLoading(false);
    }
    return response;
  }

  List<PretensaoModel> pretensoes = <PretensaoModel>[];

  Future loadPretensoes() async {
    pretensoes = [
      PretensaoModel(id: null, titulo: 'Prefiro não informar'),
      ...(await _repository.getPretensoes()).dados ?? <PretensaoModel>[]
    ];
    update();
  }

  Future loadAreasInteresse() async {
    areas =
        (await _positionsRepository.getAreasInteresse()).data ?? <AreaModel>[];
    update();
  }

  List<ObjetivoModel>? objetivos = <ObjetivoModel>[];

  Future loadObjetivos() async {
    final response = await _repository.getObjetivos();
    objetivos = response.dados;
    areasInteresseToSave = <AreaModel>[];

    if (objetivos != null) {
      objetivos!
          .map((o) => AreaModel(id: o.id, nome: o.nome))
          .forEach((a) => areasInteresseToSave.add(a));
    }
    update();
  }

  void addAreasInteresseToSave(AreaModel area) {
    areasInteresseToSave.add(area);
    update();
  }

  void removeAreasInteresseToSave(AreaModel area) {
    areasInteresseToSave.remove(area);
    update();
  }
}
