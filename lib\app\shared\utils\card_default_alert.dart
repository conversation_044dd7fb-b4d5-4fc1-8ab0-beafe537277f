import 'package:flutter/material.dart';

class CardAlertDefault extends StatelessWidget {
  final String? title;
  final Color? colorBackground;
  final Color? colorBorder;
  final double? width;
  final IconData? icon;

  const CardAlertDefault({
    super.key,
    this.title,
    this.colorBackground,
    this.colorBorder,
    this.width,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width ?? 300,
      child: DecoratedBox(
        decoration: BoxDecoration(
          color:
              colorBackground ?? const Color(0xFFf3f3fc).withValues(alpha: 0.5),
          borderRadius: const BorderRadius.all(
            Radius.circular(4),
          ),
          border: Border.all(
            color: colorBorder ?? const Color(0xFFdee2e6),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(22.0),
          child: Row(
            children: [
              Icon(
                icon ?? Icons.warning_amber_rounded,
                size: 24,
                color: const Color(0xFF212529),
              ),
              const SizedBox(width: 5),
              Text(
                title ?? "",
                style: const TextStyle(
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                  color: Color(0xFF212529),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
