import 'package:flutter/material.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../../../../shared/core/app_config.dart';

class SecaoWidgetDiversity extends StatefulWidget {
  final String header;
  final Widget? content;
  final Widget? noContent;
  final bool hasContent;
  final bool? showEdit;
  final Function? onEdit;
  final bool replaceEditWithAdd;

  const SecaoWidgetDiversity({
    super.key,
    required this.header,
    this.content,
    this.noContent,
    this.hasContent = false,
    this.showEdit,
    this.onEdit,
    this.replaceEditWithAdd = false,
  });

  @override
  _SecaoWidgetState createState() => _SecaoWidgetState();
}

class _SecaoWidgetState extends State<SecaoWidgetDiversity> {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(
        horizontal: 15,
        vertical: 8,
      ),
      color: Colors.white,
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Align(
                alignment: Alignment.topLeft,
                child: Text(
                  widget.header,
                  style: const TextStyle(fontSize: 16),
                ),
              ),
              Visibility(
                visible: widget.showEdit ?? widget.hasContent,
                child: Align(
                  alignment: Alignment.topRight,
                  child: widget.replaceEditWithAdd
                      ? InkWell(
                          onTap: widget.onEdit as void Function()?,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(
                                Icons.add,
                                color: AppConfig.colorPrimary,
                              ),
                              Text(
                                'ADICIONAR'.i18n,
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: AppConfig.colorPrimary,
                                  fontWeight: FontWeight.w500,

                                ),
                              ),
                            ],
                          ),
                        )
                      : InkWell(
                          onTap: widget.onEdit as void Function()?,
                          child: Text(
                            'EDITAR'.i18n,
                            style: const TextStyle(
                              color: AppConfig.colorPrimary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                ),
              )
            ],
          ),
          const SizedBox(height: 10),
          widget.hasContent ? widget.content! : widget.noContent ?? Container(),
        ],
      ),
    );
  }
}
