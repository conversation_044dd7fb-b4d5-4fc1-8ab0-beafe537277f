import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import '../../../shared/core/app_config.dart';
import '../controllers/social_media_controller.dart';

class SocialNetworkLine extends StatelessWidget {
  const SocialNetworkLine({
    super.key,
    required this.controller,
    required this.socialNetworkName,
    required this.iconData,
    required this.title,
  });

  final SocialMediaController controller;
  final String socialNetworkName;
  final String title;
  final IconData iconData;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: 10,
        horizontal: 24,
      ),
      child: Row(
        children: [
          FaIcon(
            iconData,
            size: 20,
            color: AppConfig.colorPrimary,
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Text(
              (controller.getRedeSocial(socialNetworkName)?.isNotEmpty == true)
                  ? controller.getRedeSocial(socialNetworkName)!
                  : title,
              style: const TextStyle(
                fontFamily: 'Inter',
                fontSize: 16,
                color: Color(0xFF444455),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
