import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

import '../../../../shared/models/responses/inscricoes_response_model.dart';
import '../../../../shared/utils/time_activity_utils.dart';

// ignore: must_be_immutable
class HeaderDeatails extends StatelessWidget {
  final JobModel job;
  late double? changeOff;
  final bool? closeScreen;

  HeaderDeatails({
    super.key,
    required this.job,
    this.changeOff,
    this.closeScreen,
  });

  @override
  Widget build(BuildContext context) {
    if (closeScreen == true) {
      Future.delayed(
        const Duration(milliseconds: 1500),
        () {
          Future.microtask(() => Navigator.of(context).pop());
        },
      );
    }
    return ColoredBox(
      color: Colors.white,
      child: Column(
        children: [
          const SizedBox(
            height: 4,
          ),
          Container(
            width: 84,
            height: 3,
            decoration: BoxDecoration(
              color: const Color(0xFFE3E3E3),
              borderRadius: BorderRadius.circular(200),
            ),
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 14,
                    horizontal: 24,
                  ),
                  child: Row(
                    children: [
                      AnimatedOpacity(
                        curve: Curves.bounceInOut,
                        opacity: 1,
                        duration: const Duration(milliseconds: 500),
                        child: CachedNetworkImage(
                          imageUrl: job.logo?.thumb ?? '',
                          imageBuilder: (context, imageProvider) => Container(
                            decoration: BoxDecoration(
                              image: DecorationImage(
                                image: imageProvider,
                                fit: BoxFit.contain,
                                alignment: Alignment.center,
                              ),
                            ),
                          ),
                          placeholder: (context, url) => SpinKitCircle(
                            color: Colors.blue[300],
                          ),
                          errorWidget: (context, url, error) => const Icon(
                            Icons.broken_image,
                            size: 60,
                          ),
                        ),
                      ),
                      const SizedBox(width: 15),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '${job.titulo}',
                              maxLines: 2,
                              style: const TextStyle(
                                color: Colors.black,
                                fontFamily: 'Inter',
                                fontWeight: FontWeight.w500,
                                fontSize: 16,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(
                              height: 4,
                            ),
                            Text(
                              '${job.empresa} • há ${TimeActivityUtils.calcularTempoAtiva(job.data ?? '')}',
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              style: const TextStyle(
                                color: Colors.black,
                                fontSize: 14,
                                fontFamily: 'Inter',
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(right: 16.0),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    customBorder: const CircleBorder(),
                    splashColor: Colors.grey.withValues(alpha: 0.6),
                    child: Container(
                      padding: const EdgeInsets.all(8.0),
                      decoration: const BoxDecoration(
                        shape: BoxShape.circle,
                      ),
                      child: Image.asset(
                        'lib/assets/images/close.png',
                        scale: 2,
                      ),
                    ),
                  ),
                ),
              )
            ],
          ),
        ],
      ),
    );
  }
}
