class VerificarSenhaResponseModel {
  final bool sucesso;
  final String? token;
  final String mensagem;

  VerificarSenhaResponseModel({
    this.sucesso = false,
    this.mensagem = "",
    this.token,
  });

  factory VerificarSenhaResponseModel.fromJson(Map<String, dynamic> json) {
    return VerificarSenhaResponseModel(
      sucesso: json['sucesso'] ?? false,
      mensagem: json['mensagem'] ?? "",
      token: json['token'] as String?,
    );
  }
}
