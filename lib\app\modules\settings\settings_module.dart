import 'package:flutter_modular/flutter_modular.dart';

import 'security_and_lgpd/security_and_lgpd_controller.dart';
import 'security_and_lgpd/security_and_lgpd_page.dart';
import 'settings_controller.dart';
import 'settings_page.dart';

class SettingsModule extends Module {
  static const route = '/settings';

  @override
  void binds(i) {
    i.addLazySingleton(SettingsController.new);
    i.addLazySingleton(SecurityAndLGPDController.new);
  }

  @override
  void routes(r) {
    r.child(
      '/',
      child: (context) => SettingsPage(),
      transition: TransitionType.noTransition,
    );

    r.child(
      SecurityAndLGPDPage.route,
      child: (context) => const SecurityAndLGPDPage(),
      transition: TransitionType.noTransition,
    );
  }
}
