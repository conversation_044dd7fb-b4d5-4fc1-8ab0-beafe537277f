import 'package:flutter/foundation.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:mobx/mobx.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:update_available/update_available.dart';

import '../../../shared/models/responses/clube_beneficios_categorias_parceiro.dart';
import '../../../shared/models/responses/clube_beneficios_detalhes_model.dart';
import '../../../shared/models/responses/clube_beneficios_promocoes.dart';
import '../../../shared/models/responses/clube_beneficios_response_model.dart';
import '../../../shared/models/responses/cluble_beneficios_categorias_model.dart';
import '../../../shared/models/responses/dash_articles_response_model.dart';
import '../../../shared/models/responses/dash_notificacoes_response_model.dart';
import '../../../shared/models/responses/inscricoes_response_model.dart';
import '../../../shared/repositories/dashboard_repository.dart';
import '../../../shared/repositories/job_repository.dart';
import '../home_controller.dart';

part 'tab_notifications_controller.g.dart';

class TabNotificationsController = _TabNotificationsControllerBase
    with _$TabNotificationsController;

abstract class _TabNotificationsControllerBase with Store {
  final DashboardRepository _repository = Modular.get();
  final JobRepository _jobRepository = Modular.get();

  @observable
  bool loading = true;

  @observable
  bool loadingUrl = false;

  @observable
  bool loadingPromo = false;

  @observable
  bool? temNotificacao;

  @observable
  int countNotificacao = 0;

  @observable
  ObservableList<QuestionarioModel> questionarios =
      ObservableList<QuestionarioModel>();

  @observable
  ObservableList<AgendaModel> agendas = ObservableList<AgendaModel>();

  @observable
  bool hasNewVersion = false;

  @observable
  ClubeBeneficiosResponseModel? beneficios;

  @observable
  ClubeBeneficiosDetalhesModel? beneficiosDetalhes;

  @observable
  ClubeBeneficiosPromocoesModel? beneficiosPromocoes;

  @observable
  DashArticlesResponseModel? articles;

  @observable
  ClubeBeneficiosCategoriasParceiroModel? beneficiosCategoriasParceiro;

  @observable
  ClubeBeneficiosCategoriasModel? beneficiosCategorias;
  @observable
  List<JobModel> recomendacoes = <JobModel>[].asObservable();

  @observable
  List<JobModel> allRecomendacoes = <JobModel>[].asObservable();

  @observable
  bool loadingRecomendacoes = false;

  @action
  Future<void> load() async {
    try {
      loading = true;
      final responseModel = await _repository.getNotificacoes();

      questionarios =
          (responseModel.questionarios ?? <QuestionarioModel>[]).asObservable();
      agendas = (responseModel.agenda ?? <AgendaModel>[]).asObservable();
      temNotificacao = responseModel.temNoticacao;

      countNotificacao = questionarios.length +
          agendas.length +
          ((responseModel.talentoday ?? false) ? 1 : 0);

      // hasNewVersion = GetVersionLib.instance.projectVersion !=
      //     responseModel.versaoApp.toString();

      final updateAvailability = await getUpdateAvailability();
      hasNewVersion = updateAvailability is UpdateAvailable;

      Modular.get<HomeController>().notifications = countNotificacao;
    } catch (exception, stack) {
      Sentry.captureException(exception, stackTrace: stack);
    } finally {
      loading = false;
    }
  }

  @action
  Future<void> loadBeneficios() async {
    try {
      beneficios = await _repository.getClubeBeneficios();
    } catch (exception, stack) {
      beneficios = ClubeBeneficiosResponseModel(sucesso: false, dados: []);
      Sentry.captureException(exception, stackTrace: stack);
    }
  }

  @action
  Future<void> loadArticles() async {
    try {
      articles = await _repository.getArticles();
    } catch (exception, stack) {
      Sentry.captureException(exception, stackTrace: stack);
    }
  }

  @action
  Future<void> getClubeBeneficiosDetalhes(int id) async {
    loadingPromo = true;
    try {
      beneficiosDetalhes = await _repository.getClubeBeneficiosDetalhes(
        detalhesId: id,
      );
    } catch (exception, stack) {
      Sentry.captureException(exception, stackTrace: stack);
    } finally {
      loadingPromo = false;
    }
  }

  @action
  Future<void> getClubeBeneficiosPromocoes(int id) async {
    try {
      beneficiosPromocoes = await _repository.getClubeBeneficiosPromocoes(
        detalhesId: id,
      );
    } catch (exception, stack) {
      Sentry.captureException(exception, stackTrace: stack);
    }
  }

  @action
  Future<void> getClubeBeneficiosCategoriasParceiro(int? id) async {
    try {
      beneficiosCategoriasParceiro =
          await _repository.getClubeBeneficiosCategoriasParceiro(
        id: id ?? 0,
      );
    } catch (exception, stack) {
      Sentry.captureException(exception, stackTrace: stack);
    }
  }

  @action
  Future<void> getClubeBeneficiosCategorias() async {
    try {
      beneficiosCategorias = await _repository.getClubeBeneficiosCategorias();
    } catch (exception, stack) {
      Sentry.captureException(exception, stackTrace: stack);
    }
  }

  @action
  Future<void> loadRecomendacoes() async {
    try {
      loadingRecomendacoes = true;

      final response = await _jobRepository.getRecomendacoes(pagina: 1);
      allRecomendacoes = (response.dados ?? <JobModel>[]).asObservable();

      recomendacoes = allRecomendacoes.take(4).toList().asObservable();

      if (kDebugMode && allRecomendacoes.isNotEmpty) {
        debugPrint(
            '✅ ${allRecomendacoes.length} recomendações carregadas (mostrando ${recomendacoes.length})');
      }
    } catch (exception, stack) {
      if (kDebugMode) {
        debugPrint('❌ Erro ao carregar recomendações: $exception');
      }
      Sentry.captureException(exception, stackTrace: stack);
    } finally {
      loadingRecomendacoes = false;
    }
  }

  @computed
  bool get hasMoreRecomendacoes => allRecomendacoes.length > 4;
}
