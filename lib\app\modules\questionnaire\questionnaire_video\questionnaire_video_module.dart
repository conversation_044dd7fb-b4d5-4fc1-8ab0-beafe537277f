import 'package:flutter_modular/flutter_modular.dart';

import 'questionnaire_video_controller.dart';
import 'questionnaire_video_page.dart';

class QuestionnaireVideoModule extends Module {
  static const route = '/questionnaire_video';

  @override
  void binds(i) {
    i.addL<PERSON>y<PERSON><PERSON>leton(QuestionnaireVideoController.new);
  }

  @override
  void routes(r) {
    r.child(
      Modular.initialRoute,
      child: (context) => QuestionnaireVideoPage(
        tempo: r.args.data,
      ),
    );
  }
}
