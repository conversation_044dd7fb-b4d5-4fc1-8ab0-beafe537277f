import 'package:auto_size_text/auto_size_text.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../../../../shared/core/app_config.dart';
import '../../../../../shared/widgets/app_dropdown_search.dart';
import '../../../../curriculum/curriculum_controller.dart';
import '../../../controllers/deficiencies_controller.dart';
import '../../curriculum_editing_controller.dart';
import '../../widgets/secao.dart';
import 'widgets/deficiencias_check_boxes.dart';

class Deficiencias extends StatefulWidget {
  const Deficiencias({super.key});

  @override
  State<Deficiencias> createState() => _DeficienciasState();
}

class _DeficienciasState extends State<Deficiencias> {
  final DeficienciesController _curriculumController = Modular.get();
  final controller = Modular.get<CurriculumEditingController>();

  @override
  void initState() {
    super.initState();

    controller.selectedOption =
        (Modular.get<CurriculumController>().pessoa?.semDeficiencia ?? false)
            ? "naoPossuo"
            : _curriculumController.deficiencias?.firstWhereOrNull((d) {
                      return d.nome?.contains("INSS") ?? false;
                    }) !=
                    null
                ? "reabilitadoPeloInss"
                : "possuoDeficiencia";

    controller.changeDiretrizesLeiCotasPcd(
      ["possuoDeficiencia", "reabilitadoPeloInss"]
          .contains(controller.selectedOption),
    );

    controller.naoPossuoDeficiencia = controller.selectedOption == "naoPossuo";
  }

  @override
  Widget build(BuildContext context) {
    return Secao(
      fields: Observer(
        builder: (_) {
          return Column(
            children: [
              InkWell(
                onTap: () {
                  controller.changeSelectedOption("possuoDeficiencia");
                },
                child: SizedBox(
                  height: 60,
                  child: Row(
                    children: [
                      Checkbox(
                        value: controller.selectedOption == "possuoDeficiencia",
                        onChanged: (value) {
                          controller.changeSelectedOption("possuoDeficiencia");
                        },
                      ),
                      const Text(
                        'Possuo Deficiência',
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 18,
                          color: Colors.black,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              InkWell(
                onTap: () {
                  controller.changeSelectedOption("reabilitadoPeloInss");
                },
                child: SizedBox(
                  height: 60,
                  child: Row(
                    children: [
                      Checkbox(
                        value:
                            controller.selectedOption == "reabilitadoPeloInss",
                        onChanged: (value) {
                          controller
                              .changeSelectedOption("reabilitadoPeloInss");
                        },
                      ),
                      const Text(
                        'Sou Reabilitado pelo INSS',
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              InkWell(
                onTap: () async {
                  controller.changeSelectedOption("naoPossuo");
                },
                child: SizedBox(
                  height: 60,
                  child: Row(
                    children: [
                      Checkbox(
                        value: controller.selectedOption == "naoPossuo",
                        onChanged: (value) {
                          controller.changeSelectedOption("naoPossuo");
                        },
                      ),
                      const Text(
                        'Não possuo deficiência',
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          color: Colors.black,
                          fontSize: 18,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Visibility(
                visible: controller.selectedOption == "possuoDeficiencia",
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsets.only(
                        top: _curriculumController.deficienciaAuditiva != null
                            ? 8
                            : 0,
                      ),
                      child: DropdownSearch(
                        context,
                        hintText: 'Auditiva'.i18n,
                        title: 'Auditiva'.i18n,
                        items: _curriculumController.categoriasDeficiencias
                            .where((c) => c.tipo == 1 || c.tipo == null)
                            .toList()
                            .map((e) => DropdownSearchItem(
                                  value: e,
                                  searchKey: e.nome!,
                                  text: e.nome,
                                  child: Text(e.nome!),
                                ))
                            .toList(),
                        onSelected: (dynamic item) {
                          if (item != null) {
                            if (item.id != null) {
                              _curriculumController.addDeficienciaToSave(
                                item,
                                'Auditiva',
                              );
                            } else {
                              _curriculumController.removeDeficienciaToSave(
                                _curriculumController.deficienciaAuditiva!.id,
                              );
                            }
                          }
                        },
                        value: _curriculumController.deficienciaAuditiva,
                      ),
                    ),
                    const SizedBox(height: 10),
                    Padding(
                      padding: EdgeInsets.only(
                          top: _curriculumController.deficienciaFisica != null
                              ? 8
                              : 0),
                      child: DropdownSearch(
                        context,
                        hintText: 'Física'.i18n,
                        title: 'Física'.i18n,
                        items: _curriculumController.categoriasDeficiencias
                            .where((c) => c.tipo == 3 || c.tipo == null)
                            .toList()
                            .map((e) => DropdownSearchItem(
                                  value: e,
                                  searchKey: e.nome!,
                                  text: e.nome,
                                  child: Text(e.nome!),
                                ))
                            .toList(),
                        onSelected: (dynamic item) {
                          if (item != null) {
                            if (item.id != null) {
                              _curriculumController.addDeficienciaToSave(
                                item,
                                'Física',
                              );                            } else {
                              final deficiencia = _curriculumController.deficienciaFisica;
                              if (deficiencia?.id != null) {
                                _curriculumController.removeDeficienciaToSave(
                                  deficiencia!.id,
                                );
                              }
                            }
                          }
                        },
                        value: _curriculumController.deficienciaFisica,
                      ),
                    ),
                    const SizedBox(height: 10),
                    Padding(
                      padding: EdgeInsets.only(
                          top: _curriculumController.deficienciaVisual != null
                              ? 8
                              : 0),
                      child: DropdownSearch(
                        context,
                        hintText: 'Visual'.i18n,
                        title: 'Visual'.i18n,
                        items: _curriculumController.categoriasDeficiencias
                            .where((c) => c.tipo == 5 || c.tipo == null)
                            .toList()
                            .map((e) => DropdownSearchItem(
                                  value: e,
                                  searchKey: e.nome!,
                                  text: e.nome,
                                  child: Text(e.nome!),
                                ))
                            .toList(),
                        onSelected: (dynamic item) {
                          if (item != null) {
                            if (item.id != null) {
                              _curriculumController.addDeficienciaToSave(
                                item,
                                'Visual',
                              );
                            } else {
                              _curriculumController.removeDeficienciaToSave(
                                _curriculumController.deficienciaVisual!.id,
                              );
                            }
                          }
                        },
                        value: _curriculumController.deficienciaVisual,
                      ),
                    ),
                    const SizedBox(height: 20),
                    Text(
                      'Intelectual'.i18n,
                      style: const TextStyle(
                        fontSize: 18,
                      ),
                    ),
                    const DeficienciasCheckBoxes(
                      tipo: 4,
                      categoriaNome: 'Intelectual',
                    ),
                    const SizedBox(height: 15),
                    Text(
                      'Mental'.i18n,
                      style: const TextStyle(
                        fontSize: 18,
                      ),
                    ),
                    const DeficienciasCheckBoxes(
                      tipo: 6,
                      categoriaNome: 'Mental',
                    ),
                    const SizedBox(height: 15),
                  ],
                ),
              ),
              Visibility(
                visible: [
                  "possuoDeficiencia",
                  "reabilitadoPeloInss",
                ].contains(controller.selectedOption),
                child: Column(
                  children: [
                    ..._curriculumController.laudos!.map(
                      (l) => Card(
                        elevation: 0,
                        color: const Color(0xFFF7F7F7),
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  const Icon(Icons.attach_file, size: 18),
                                  const Text(
                                    'Laudo PcD',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  const Spacer(),
                                  Material(
                                    child: InkWell(
                                      onTap: () {
                                        if (l.arquivo != null) {
                                          _curriculumController
                                              .downloadLaudo(l.arquivo!);
                                        }
                                      },
                                      child: const Icon(
                                        Icons.sim_card_download_rounded,
                                        color: AppConfig.colorPrimary,
                                        size: 24,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 25),
                                  Material(
                                    child: InkWell(
                                      onTap: () =>
                                          controller.onRemoveLaudo(context, l),
                                      child: const Icon(
                                        Icons.delete,
                                        color: Colors.red,
                                        size: 24,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              Text(
                                l.dataCadastro!,
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 10),
                    const Divider(),
                    const SizedBox(height: 20),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppConfig.white,
                            padding: const EdgeInsets.symmetric(
                              vertical: 15,
                              horizontal: 30,
                            ),
                            elevation: 0,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(6),
                              side: const BorderSide(
                                color: AppConfig.colorPrimary,
                                width: 1.8,
                              ),
                            ),
                          ),
                          onPressed: () {
                            controller.showDialogUploadLaudo(context);
                          },
                          child: Row(
                            children: [
                              const Icon(
                                Icons.add_circle,
                                color: AppConfig.colorPrimary,
                                size: 18,
                              ),
                              const SizedBox(width: 5),
                              Text(
                                'Adicionar Laudo'.i18n,
                                textAlign: TextAlign.center,
                                style: const TextStyle(
                                  color: AppConfig.colorPrimary,
                                  fontFamily: 'Inter',
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    const Divider(),
                    InkWell(
                      onTap: () => controller.changeDiretrizesLeiCotasPcd(),
                      child: Padding(
                        padding: const EdgeInsets.only(bottom: 24.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Checkbox(
                              value: controller.diretrizesLeiCotasPcd,
                              onChanged: (value) {
                                controller.changeDiretrizesLeiCotasPcd(value);
                                setState(() {});
                              },
                            ),
                            SizedBox(
                              width: MediaQuery.of(context).size.width * 0.75,
                              child: const AutoSizeText(
                                'Ao preencher o campo “Deficiências”, você está '
                                'de acordo com o tratamento dos seus dados '
                                'pessoais pela empresa, conforme as diretrizes da '
                                'Lei nº 13.709/18 (Lei Geral de Proteção de Dados), '
                                'única e exclusivamente para preenchimento de Cotas '
                                'PCD, de acordo com a base legal de obrigação legal, '
                                'prevista no artigo 11º, II, a da lei.',
                                softWrap: true,
                                overflow: TextOverflow.clip,
                                style: TextStyle(fontSize: 14),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              )
            ],
          );
        },
      ),
    );
  }
}
