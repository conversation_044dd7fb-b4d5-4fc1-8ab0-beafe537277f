import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:i18n_extension/default.i18n.dart';
import 'package:mobx/mobx.dart';

import '../../../shared/core/app_config.dart';
import '../../../shared/firebase/firebase_messaging.dart';
import '../../../shared/models/cadastrar_model.dart';
import '../../../shared/models/genero_model.dart';
import '../../../shared/models/responses/cidade_response_model.dart';
import '../../../shared/models/responses/estado_response_model.dart';
import '../../../shared/models/responses/pais_response_model.dart';
import '../../../shared/models/session_model.dart';
import '../../../shared/repositories/cities_repository.dart';
import '../../../shared/repositories/register_repository.dart';
import '../../../shared/services/session_service.dart';

part 'signup_controller.g.dart';

enum SignupState {
  idle,
  loadingPaises,
  loadingEstados,
  loadingCidades,
  saving,
  saved,
}

class SignupController = _SignupControllerBase with _$SignupController;

abstract class _SignupControllerBase with Store {
  final RegisterRepository _registerRepository = RegisterRepository();
  final CitiesRepository _citiesRepository = Modular.get();

  _SignupControllerBase() {
    init();
  }

  @action
  Future<void> init() async {
    _state = SignupState.loadingPaises;
    var paisResponseModel = await _citiesRepository.getPaises();

    if (paisResponseModel.sucesso) {
      _paises = (paisResponseModel.dados ?? []).asObservable();
    }

    _state = SignupState.idle;
  }

  final List<GeneroModel> generos = [
    GeneroModel(id: 'F', nome: 'Feminino'),
    GeneroModel(id: 'M', nome: 'Masculino'),
    GeneroModel(id: 'I', nome: 'Prefiro não informar'),
  ];

  @observable
  SignupState _state = SignupState.idle;
  SignupState get state => _state;

  @observable
  List<PaisModel> _paises = <PaisModel>[].asObservable();
  List<PaisModel> get paises => _paises;

  @observable
  List<EstadoModel> _estados = <EstadoModel>[].asObservable();

  List<EstadoModel> get estados => _estados;

  @observable
  List<CidadeModel> _cidades = <CidadeModel>[].asObservable();

  List<CidadeModel> get cidades => _cidades;

  @observable
  PaisModel? _pais;
  PaisModel? get pais => _pais;
  setPais(value) => _pais = value;

  @observable
  EstadoModel? _estado;
  EstadoModel? get estado => _estado;
  setEstado(value) => _estado = value;

  @observable
  CidadeModel? _cidade;
  CidadeModel? get cidade => _cidade;
  setCidade(value) => _cidade = value;

  @observable
  GeneroModel? _genero;
  GeneroModel? get genero => _genero;
  setGenero(value) => _genero = value;

  @observable
  DateTime? _dataNascimento;
  DateTime? get dataNascimento => _dataNascimento;
  setDataNascimento(value) => _dataNascimento = value;

  String _nome = '';
  String get nome => _nome;
  setNome(value) => _nome = value;

  String _cpf = '';
  String get cpf => _cpf;
  setCpf(value) => _cpf = value;

  @observable
  String _celularPaisCode = '+55';
  String get celularPaisCode => _celularPaisCode;
  @action
  setCelularPaisCode(value) => _celularPaisCode = value;

  String _celular = '';
  String get celular => _celular;
  setCelular(value) => _celular = value;

  @observable
  String _telefonePaisCode = '+55';
  String get telefonePaisCode => _telefonePaisCode;
  @action
  setTelefonePaisCode(value) => _telefonePaisCode = value;

  String _telefone = '';
  String get telefone => _telefone;
  setTelefone(value) => _telefone = value;

  String _email = '';
  String get email => _email;
  setEmail(value) => _email = value;

  String _senha = '';
  String get senha => _senha;
  setSenha(value) => _senha = value;

  void showSnackBarRequiredField(String fieldName, BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        backgroundColor: AppConfig.red,
        content: Text('Campo %s é obrigatório'.i18n.fill([fieldName])),
      ),
    );
  }

  @action
  void cleanEstado() => setEstado(null);

  @action
  Future getEstados() async {
    cleanEstado();
    cleanCidade();
    _state = SignupState.loadingEstados;
    EstadoResponseModel estadoResponseModel = await _citiesRepository.getEstados(paisID: _pais?.id);
    if (estadoResponseModel.sucesso) {
      _estados = estadoResponseModel.dados!.asObservable();
    }
    _state = SignupState.idle;
  }

  @action
  void cleanCidade() => setCidade(null);

  @action
  Future getCidades() async {
    cleanCidade();
    _state = SignupState.loadingCidades;

    CidadeResponseModel cidadeResponseModel = await _citiesRepository.getCidades(
      estadoID: _estado?.id,
    );

    if (cidadeResponseModel.sucesso) {
      _cidades = cidadeResponseModel.dados!.asObservable();
    }
    _state = SignupState.idle;
  }

  @action
  Future postCadastrar() async {
    _state = SignupState.saving;

    var responseModel = await _registerRepository.postCadastrar(CadastrarModel(
      paisID: _pais!.id,
      email: _email,
      cpf: _cpf,
      senha: _senha,
      ano: _dataNascimento!.year,
      celular: _celular,
      celularPaisCode: _celularPaisCode,
      cidadeID: _cidade?.id,
      dia: _dataNascimento!.day,
      mes: _dataNascimento!.month,
      nome: _nome,
      sexo: _genero?.id,
      telefonePaisCode: _telefonePaisCode,
      telefone: _telefone,
    ));

    if (responseModel.sucesso) {
      _state = SignupState.saved;
    } else {
      _state = SignupState.idle;
    }

    return responseModel;
  }

  Future<void> saveSessionAndUpdateFirebase(String? token) async {
    await Modular.get<SessionService>().set(SessionModel(token: token));
    FirebaseMessagingUtils.instance.updateToken();
  }

  @observable
  bool obscureText = true;

  @action
  void showHidePassword() => obscureText = !obscureText;
}
