import 'pessoa_response_model.dart';

class LoginResponseModel {
  final bool sucesso;
  final String mensagem;
  final String? token;
  final String? redirect;
  final List<PessoaResponseModel>? pessoas;

  LoginResponseModel({
    this.sucesso = false,
    this.mensagem = "",
    this.token,
    this.redirect,
    this.pessoas,
  });

  factory LoginResponseModel.fromJson(Map<String, dynamic> json) {
    for (var key in json.keys.toList()) {
      json[key.toLowerCase()] = json[key];
    }

    return LoginResponseModel(
      sucesso: json['sucesso'] ?? false,
      mensagem: json['mensagem'] ?? "",
      token: json['token'] as String?,
      redirect: json['redirect'] as String?,
      pessoas: (json['pessoas'] as List<dynamic>?)
          ?.map((e) => PessoaResponseModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}
