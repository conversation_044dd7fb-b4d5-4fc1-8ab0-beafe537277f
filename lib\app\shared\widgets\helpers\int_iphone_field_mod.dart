library intl_phone_field;

import 'dart:async';

import 'package:empregare_app/app/shared/core/app_translation.i18n.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:international_phone_input/international_phone_input.dart'
    as ipi;
import 'package:intl_phone_field/countries.dart';
import 'package:intl_phone_field/country_picker_dialog.dart';
import 'package:intl_phone_field/phone_number.dart';

class IntlPhoneFieldMod extends StatefulWidget {
  final bool obscureText;

  final TextAlign textAlign;

  final TextAlignVertical? textAlignVertical;
  final VoidCallback? onTap;

  final bool readOnly;
  final FormFieldSetter<PhoneNumber>? onSaved;

  final ValueChanged<PhoneNumber>? onChanged;

  final ValueChanged<Country>? onCountryChanged;

  final FutureOr<String?> Function(PhoneNumber?)? validator;

  final TextInputType keyboardType;

  final TextEditingController? controller;

  final FocusNode? focusNode;

  final void Function(String)? onSubmitted;

  final bool enabled;

  final Brightness? keyboardAppearance;

  final String? initialValue;

  final String languageCode;

  final String? initialCountryCode;

  final List<Country>? countries;

  final InputDecoration decoration;

  final TextStyle? style;

  final bool disableLengthCheck;

  final bool showDropdownIcon;

  final BoxDecoration dropdownDecoration;

  final TextStyle? dropdownTextStyle;

  final List<TextInputFormatter>? inputFormatters;

  final String searchText;

  final IconPosition dropdownIconPosition;

  final Icon dropdownIcon;

  final bool autofocus;

  final AutovalidateMode? autovalidateMode;

  final bool showCountryFlag;

  final String? invalidNumberMessage;

  final Color? cursorColor;

  final double? cursorHeight;

  final Radius? cursorRadius;

  final double cursorWidth;

  final bool? showCursor;

  final EdgeInsetsGeometry flagsButtonPadding;

  final TextInputAction? textInputAction;

  final PickerDialogStyle? pickerDialogStyle;

  final EdgeInsets flagsButtonMargin;

  final bool disableAutoFillHints;

  final int? maxLength;

  const IntlPhoneFieldMod({
    super.key,
    @Deprecated('Use searchFieldInputDecoration of PickerDialogStyle instead')
    this.obscureText = false,
    this.textAlign = TextAlign.left,
    this.textAlignVertical,
    this.onTap,
    this.readOnly = false,
    this.onSaved,
    this.onChanged,
    this.onCountryChanged,
    this.validator,
    this.keyboardType = TextInputType.phone,
    this.controller,
    this.focusNode,
    this.onSubmitted,
    this.enabled = true,
    this.keyboardAppearance,
    this.initialValue,
    this.languageCode = 'pt_BR',
    this.initialCountryCode,
    this.countries,
    this.decoration = const InputDecoration(),
    this.style,
    this.disableLengthCheck = false,
    this.showDropdownIcon = true,
    this.dropdownDecoration = const BoxDecoration(),
    this.dropdownTextStyle,
    this.inputFormatters,
    this.searchText = 'Procurar pais',
    this.dropdownIconPosition = IconPosition.leading,
    this.dropdownIcon = const Icon(Icons.arrow_drop_down),
    this.autofocus = false,
    this.autovalidateMode = AutovalidateMode.onUserInteraction,
    this.showCountryFlag = true,
    this.invalidNumberMessage = 'Número de celular inválido',
    this.cursorColor,
    this.cursorHeight,
    this.cursorRadius = Radius.zero,
    this.cursorWidth = 2.0,
    this.showCursor = true,
    this.flagsButtonPadding = EdgeInsets.zero,
    this.textInputAction,
    this.pickerDialogStyle,
    this.flagsButtonMargin = EdgeInsets.zero,
    this.disableAutoFillHints = false,
    this.maxLength,
  });

  @override
  _IntlPhoneFieldModState createState() => _IntlPhoneFieldModState();
}

class _IntlPhoneFieldModState extends State<IntlPhoneFieldMod> {
  late List<Country> _countryList;
  late Country _selectedCountry;
  late List<Country> filteredCountries;
  late String number;
  int? myMaxLenght;

  String? validatorMessage;

  @override
  void initState() {
    super.initState();

    _initializeCountryList();
    _initializeSelectedCountryAndNumber();

    if (widget.autovalidateMode == AutovalidateMode.always) {
      _initializeValidatorMessage();
    }
  }

  void _initializeCountryList() {
    _countryList = widget.countries ?? countries;
    filteredCountries = _countryList;
  }

  void _initializeSelectedCountryAndNumber() {
    number = widget.initialValue ?? '';

    if (widget.initialCountryCode == null && number.startsWith('+')) {
      _initializeSelectedCountryFromNumber();
    } else {
      _initializeSelectedCountryFromCode();
    }

    _adjustNumberBasedOnSelectedCountry();
  }

  void _initializeSelectedCountryFromNumber() {
    number = number.substring(1);
    _selectedCountry = _countryList.firstWhere(
      (country) => number.startsWith(country.fullCountryCode),
      orElse: () => _countryList.first,
    );

    number = number.replaceFirst(
      RegExp("^${_selectedCountry.fullCountryCode}"),
      "",
    );
  }

  void _initializeSelectedCountryFromCode() {
    _selectedCountry = _countryList.firstWhere(
      (item) => item.dialCode == (widget.initialCountryCode),
      orElse: () {
        return _countryList.firstWhere(
          (item) => item.code == 'BR',
          orElse: () => _countryList.first,
        );
      },
    );

    final codePrefix = _selectedCountry.dialCode.startsWith('+')
        ? '\\+${_selectedCountry.fullCountryCode}'
        : _selectedCountry.fullCountryCode;

    number = number.replaceFirst(
      RegExp("^$codePrefix"),
      "",
    );
  }

  void _adjustNumberBasedOnSelectedCountry() {
    if (_selectedCountry.dialCode == '55') {
      myMaxLenght = 15;
    }
  }

  void _initializeValidatorMessage() {
    final initialPhoneNumber = PhoneNumber(
      countryISOCode: _selectedCountry.code,
      countryCode: '+${_selectedCountry.dialCode}',
      number: widget.initialValue ?? '',
    );

    final value = widget.validator?.call(initialPhoneNumber);

    if (value != null) {
      (value is String)
          ? validatorMessage = value
          : (value as Future).then((msg) {
              validatorMessage = msg;
            });
    }
  }

  Future<void> _changeCountry() async {
    filteredCountries = _countryList;
    await showDialog(
      context: context,
      useRootNavigator: false,
      builder: (context) => StatefulBuilder(
        builder: (ctx, setState) => CountryPickerDialog(
          languageCode: widget.languageCode,
          style: widget.pickerDialogStyle,
          filteredCountries: filteredCountries,
          searchText: widget.searchText,
          countryList: _countryList,
          selectedCountry: _selectedCountry,
          onCountryChanged: (Country country) {
            _selectedCountry = country;
            myMaxLenght = null;
            if (_selectedCountry.dialCode == '55') myMaxLenght = 15;
            widget.onCountryChanged?.call(country);
            setState(() {});

            var cleanNumber =
                widget.controller?.text.replaceAll(RegExp(r'[^0-9]'), '') ?? '';

            if (_selectedCountry.code == 'BR') {
              setState(() {
                if (widget.controller!.text.length < myMaxLenght!) {
                  validatorMessage = 'Número Inválido'.i18n;
                } else {
                  return;
                }
              });
            } else {
              ipi.PhoneService.parsePhoneNumber(
                      cleanNumber, _selectedCountry.code)
                  .then((value) {
                validatorMessage =
                    value == true ? null : 'Número Inválido'.i18n;
                setState(() {});
              });
            }
          },
        ),
      ),
    );
    if (mounted) setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      initialValue: (widget.controller == null) ? number : null,
      autofillHints: widget.disableAutoFillHints
          ? null
          : [AutofillHints.telephoneNumberNational],
      readOnly: widget.readOnly,
      obscureText: widget.obscureText,
      textAlign: widget.textAlign,
      textAlignVertical: widget.textAlignVertical,
      cursorColor: widget.cursorColor,
      onTap: widget.onTap,
      controller: widget.controller,
      focusNode: widget.focusNode,
      cursorHeight: widget.cursorHeight,
      cursorRadius: widget.cursorRadius,
      cursorWidth: widget.cursorWidth,
      showCursor: widget.showCursor,
      onFieldSubmitted: widget.onSubmitted,
      decoration: widget.decoration.copyWith(
        prefixIcon: _buildFlagsButton(),
        counterText: !widget.enabled ? '' : null,
        errorText: validatorMessage,
        counter: const SizedBox.shrink(),
        enabledBorder: const OutlineInputBorder(
          borderRadius: BorderRadius.all(
            Radius.circular(6),
          ),
          borderSide: BorderSide(
            color: Color(0xffeeeff3),
            width: 1.8,
          ),
        ),
        border: const OutlineInputBorder(
          borderRadius: BorderRadius.all(
            Radius.circular(6),
          ),
          borderSide: BorderSide(
            width: 5,
          ),
        ),
      ),
      style: widget.style,
      onSaved: (value) {
        widget.onSaved?.call(
          PhoneNumber(
            countryISOCode: _selectedCountry.code,
            countryCode:
                '+${_selectedCountry.dialCode}${_selectedCountry.regionCode}',
            number: value!,
          ),
        );
      },
      onChanged: (value) async {
        final phoneNumber = PhoneNumber(
          countryISOCode: _selectedCountry.code,
          countryCode: '+${_selectedCountry.fullCountryCode}',
          number: value,
        );

        if (widget.autovalidateMode != AutovalidateMode.disabled) {
          validatorMessage = await widget.validator?.call(phoneNumber);

          var cleanNumber = value.replaceAll(RegExp(r'[^0-9]'), '');
          if (phoneNumber.countryISOCode == 'BR') {
            setState(() {
              if (value.length < myMaxLenght!) {
                validatorMessage = 'Número Inválido'.i18n;
              } else {
                return;
              }
            });
          } else {
            ipi.PhoneService.parsePhoneNumber(
                    cleanNumber, _selectedCountry.code)
                .then(
              (value) {
                validatorMessage =
                    value == true ? null : 'Número Inválido'.i18n;
                setState(() {});
              },
            );
          }
        }

        widget.onChanged?.call(phoneNumber);
      },
      maxLength: widget.disableLengthCheck
          ? null
          : myMaxLenght ?? widget.maxLength ?? _selectedCountry.maxLength,
      keyboardType: widget.keyboardType,
      inputFormatters: widget.inputFormatters,
      enabled: widget.enabled,
      keyboardAppearance: widget.keyboardAppearance,
      autofocus: widget.autofocus,
      textInputAction: widget.textInputAction,
      autovalidateMode: widget.autovalidateMode,
    );
  }

  Container _buildFlagsButton() {
    return Container(
      margin: widget.flagsButtonMargin,
      child: DecoratedBox(
        decoration: widget.dropdownDecoration,
        child: InkWell(
          borderRadius: widget.dropdownDecoration.borderRadius as BorderRadius?,
          onTap: widget.enabled ? _changeCountry : null,
          child: Padding(
            padding: widget.flagsButtonPadding,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                const SizedBox(
                  width: 10,
                ),
                if (widget.showCountryFlag) ...[
                  kIsWeb
                      ? Image.asset(
                          'assets/flags/${_selectedCountry.code.toLowerCase()}.png',
                          package: 'intl_phone_field',
                          width: 32,
                        )
                      : Text(
                          _selectedCountry.flag,
                          style: const TextStyle(fontSize: 24),
                        ),
                  const SizedBox(width: 8),
                ],
                FittedBox(
                  child: Text(
                    '+${_selectedCountry.dialCode}',
                    style: widget.dropdownTextStyle,
                  ),
                ),
                if (widget.enabled &&
                    widget.showDropdownIcon &&
                    widget.dropdownIconPosition == IconPosition.leading) ...[
                  widget.dropdownIcon,
                  const SizedBox(width: 4),
                ],
                if (widget.enabled &&
                    widget.showDropdownIcon &&
                    widget.dropdownIconPosition == IconPosition.trailing) ...[
                  const SizedBox(width: 4),
                  widget.dropdownIcon,
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}

enum IconPosition {
  leading,
  trailing,
}
