import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:path_provider/path_provider.dart';

import '../core/app_rest.dart';
import '../models/responses/categorias_deficiencias_response_model.dart';
import '../models/responses/categorias_idiomas_response_model.dart';
import '../models/responses/dados_pessoais_response_model.dart';
import '../models/responses/listar_complementar_response_model.dart';
import '../models/responses/listar_cursos_response_model.dart';
import '../models/responses/listar_deficiencias_response_model.dart';
import '../models/responses/listar_diversidade_response_model.dart';
import '../models/responses/listar_experiencias_response_model.dart';
import '../models/responses/listar_formacoes_response_model.dart';
import '../models/responses/listar_idiomas_response_model.dart';
import '../models/responses/listar_informatica_response_model.dart';
import '../models/responses/listar_objetivos_response_model.dart';
import '../models/responses/listar_pretensoes_response_model.dart';
import '../models/responses/listar_redes_sociais_response_model.dart';
import '../models/responses/profile_response_model.dart';
import '../models/responses/simple_response_model.dart';
import '../models/salvar_complementar_model.dart';
import '../models/salvar_deficiencias_model.dart';
import '../models/salvar_diversidade_model.dart';
import '../models/salvar_experiencia_model.dart';
import '../models/salvar_informatica_model.dart';
import '../models/salvar_objetivo_model.dart';
import '../models/salvar_pessoa_model.dart';
import '../models/salvar_redes_sociais_model.dart';

class CurriculumRepository {
  final AppRest _rest = Modular.get();

  Future<SimpleResponseModel> upload({required File file}) async {
    try {
      String fileName = file.path.split('/').last;
      FormData formData = FormData.fromMap({
        "file": await MultipartFile.fromFile(file.path, filename: fileName),
      });

      final response = await _rest.post(
        '/candidato/curriculo/upload',
        data: formData,
      );

      return SimpleResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return SimpleResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return SimpleResponseModel.fromJson(err.response!.data);
    } catch (error) {
      return SimpleResponseModel.fromJson({
        'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
      });
    }
  }

  Future<ProfileResponseModel> getProgressoResumido() async {
    try {
      final response = await _rest.get(
        '/candidato/curriculo/progresso/resumido',
      );

      return ProfileResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return ProfileResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return ProfileResponseModel.fromJson(err.response!.data);
    }
  }

  Future<DadosPessoaisResponseModel> getDadosPessoais() async {
    try {
      final response = await _rest.get(
        '/candidato/curriculo/dados-pessoais/listar',
      );

      return DadosPessoaisResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return DadosPessoaisResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return DadosPessoaisResponseModel.fromJson(err.response!.data);
    }
  }

  Future<SimpleResponseModel> saveDadosPessoais(
    SalvarPessoaModel pessoaModel,
  ) async {
    try {
      final response = await _rest.post(
        '/candidato/curriculo/dados-pessoais/salvar',
        data: pessoaModel.toJson(),
      );

      final responseModel = SimpleResponseModel.fromJson(response.data);
      return responseModel;
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return SimpleResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return SimpleResponseModel.fromJson(err.response!.data);
    }
  }

  Future<SimpleResponseModel> saveFoto({required File foto}) async {
    try {
      String fileName = foto.path.split('/').last;
      FormData formData = FormData.fromMap({
        "file": await MultipartFile.fromFile(foto.path, filename: fileName),
      });

      final response = await _rest.post(
        '/candidato/curriculo/foto/salvar',
        data: formData,
      );
      return SimpleResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return SimpleResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return SimpleResponseModel.fromJson(err.response!.data);
    }
  }

  Future<SimpleResponseModel> deleteFoto() async {
    SimpleResponseModel success;
    try {
      final response = await _rest.delete('/candidato/curriculo/foto/excluir');
      success = SimpleResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return SimpleResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return SimpleResponseModel.fromJson(err.response!.data);
    }
    return success;
  }

  Future<RedeSocialResponseModel> getRedesSociais() async {
    try {
      final response = await _rest.get('/candidato/curriculo/social/listar');

      return RedeSocialResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return RedeSocialResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return RedeSocialResponseModel.fromJson(err.response!.data);
    }
  }

  Future<SimpleResponseModel> saveRedesSociais(List<Map> redesSociais) async {
    SimpleResponseModel responseModel;
    try {
      final salvar = SalvarRedeSocialModel(dados: redesSociais);
      final response = await _rest.post(
        '/candidato/curriculo/social/salvar',
        data: salvar.toJson()['dados'],
      );

      responseModel = SimpleResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return SimpleResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return SimpleResponseModel.fromJson(err.response!.data);
    }

    return responseModel;
  }

  Future<CategoriaDeficienciaResponseModel> getCategoriasDeficiencias() async {
    try {
      final response = await _rest.get(
        '/candidato/curriculo/deficiencia/categorias',
      );

      return CategoriaDeficienciaResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return CategoriaDeficienciaResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return CategoriaDeficienciaResponseModel.fromJson(err.response!.data);
    }
  }

  Future<DeficienciaResponseModel> getDeficiencias() async {
    try {
      final response =
          await _rest.get('/candidato/curriculo/deficiencia/listar');
      return DeficienciaResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return DeficienciaResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return DeficienciaResponseModel.fromJson(err.response!.data);
    }
  }

  Future<SimpleResponseModel> uploadLaudo({required File file}) async {
    try {
      String fileName = file.path.split('/').last;
      FormData formData = FormData.fromMap({
        "file": await MultipartFile.fromFile(file.path, filename: fileName),
      });

      final response = await _rest.post(
        '/candidato/curriculo/deficiencia/upload-laudo',
        data: formData,
      );
      return SimpleResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return SimpleResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return SimpleResponseModel.fromJson(err.response!.data);
    }
  }

  Future<String> downloadLaudo(String arquivo) async {
    try {
      String extension = arquivo.split('.').last;
      Response response = await _rest.post(
        '/candidato/curriculo/deficiencia/download-laudo',
        data: [
          {"key": "arquivo", "value": arquivo}
        ],
      );

      Directory downloadsDirectory = await getApplicationDocumentsDirectory();
      var path = "${downloadsDirectory.path}/Laudo.$extension";
      await _rest.download(response.data["url"], path,
          options: Options(extra: {"no-auth": true}));
      return path;
    } on DioException catch (err) {
      throw err.message ?? '';
    }
  }

  Future<bool> deleteLaudo(int? id) async {
    bool success;
    try {
      final response = await _rest
          .delete('/candidato/curriculo/deficiencia/excluir-laudo?id=$id');
      success = response.data['sucesso'] ?? false;
    } on DioException catch (err) {
      throw err.message ?? '';
    }
    return success;
  }

  Future<SimpleResponseModel> saveDeficiencias(
    SalvarDeficienciasModel salvarModel,
  ) async {
    SimpleResponseModel responseModel;
    try {
      final response = await _rest.post(
        '/candidato/curriculo/deficiencia/salvar',
        data: salvarModel.toJson(),
      );
      responseModel = SimpleResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return SimpleResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return SimpleResponseModel.fromJson(err.response!.data);
    }
    return responseModel;
  }

  Future<ObjetivoResponseModel> getObjetivos() async {
    try {
      final response = await _rest.get('/candidato/curriculo/objetivo/listar');
      return ObjetivoResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return ObjetivoResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return ObjetivoResponseModel.fromJson(err.response!.data);
    }
  }

  Future<SimpleResponseModel> saveObjetivo(
    SalvarObjetivoModel salvarModel,
  ) async {
    SimpleResponseModel responseModel;
    try {
      final response = await _rest.post(
        '/candidato/curriculo/objetivo/salvar',
        data: salvarModel.toJson(),
      );
      responseModel = SimpleResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return SimpleResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return SimpleResponseModel.fromJson(err.response!.data);
    }
    return responseModel;
  }

  Future<PretensoesResponseModel> getPretensoes() async {
    try {
      final response = await _rest.get('/candidato/curriculo/pretensao/listar');
      return PretensoesResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return PretensoesResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return PretensoesResponseModel.fromJson(err.response!.data);
    }
  }

  Future<bool> saveSintese(String? sintese) async {
    bool success;
    try {
      final response = await _rest.post('/candidato/curriculo/sintese/salvar',
          data: {"conteudo": sintese});
      success = response.data['sucesso'] ?? false;
    } on DioException catch (err) {
      throw err.message ?? '';
    }
    return success;
  }

  Future<FormacaoResponseModel> getFormacoes() async {
    try {
      final response = await _rest.get(
        '/candidato/curriculo/formacao/listar',
      );
      return FormacaoResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return FormacaoResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return FormacaoResponseModel.fromJson(err.response!.data);
    }
  }

  Future<SimpleResponseModel> saveFormacao(FormacaoModel formacao) async {
    SimpleResponseModel responseModel;
    try {
      final response = await _rest.post(
        '/candidato/curriculo/formacao/salvar',
        data: formacao.toJson(),
      );
      responseModel = SimpleResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return SimpleResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return SimpleResponseModel.fromJson(err.response!.data);
    }
    return responseModel;
  }

  Future<bool> deleteFormacao(int? id) async {
    bool success;
    try {
      final response =
          await _rest.delete('/candidato/curriculo/formacao/excluir/$id');
      success = response.data['sucesso'] ?? false;
    } on DioException catch (err) {
      throw err.message ?? '';
    }
    return success;
  }

  Future<ExperienciaResponseModel> getExperiencias() async {
    try {
      final response = await _rest.get(
        '/candidato/curriculo/experiencia/listar',
      );

      return ExperienciaResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return ExperienciaResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return ExperienciaResponseModel.fromJson(err.response!.data);
    }
  }

  Future<SimpleResponseModel> saveExperiencia(
    SalvarExperienciaModel? experiencia, {
    bool remover = false,
  }) async {
    SimpleResponseModel responseModel;
    try {
      final response = await _rest.post(
        '/candidato/curriculo/experiencia/salvar',
        data: remover ? {"remover": 1} : experiencia!.toJson(),
      );

      responseModel = SimpleResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return SimpleResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return SimpleResponseModel.fromJson(err.response!.data);
    }
    return responseModel;
  }

  Future<SimpleResponseModel> deleteExperiencia(int? id) async {
    SimpleResponseModel responseModel;
    try {
      final response = await _rest.delete(
        '/candidato/curriculo/experiencia/excluir/$id',
      );

      responseModel = SimpleResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return SimpleResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return SimpleResponseModel.fromJson(err.response!.data);
    }
    return responseModel;
  }

  Future<SimpleResponseModel> saveDiversidade(
      SalvarDiversidadeModel? diversidade) async {
    SimpleResponseModel responseModel;
    try {
      final response = await _rest.post(
        '/candidato/curriculo/diversidade/salvar',
        data: diversidade!.toJson(),
      );
      responseModel = SimpleResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return SimpleResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return SimpleResponseModel.fromJson(err.response!.data);
    }
    return responseModel;
  }

  Future<DiversidadeResponseModel> getDiversidade() async {
    try {
      final response = await _rest.get(
        '/candidato/curriculo/diversidade/listar',
      );
      return DiversidadeResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return DiversidadeResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return DiversidadeResponseModel.fromJson(err.response!.data);
    }
  }

  Future<CursoResponseModel> getCursos() async {
    try {
      final response = await _rest.get('/candidato/curriculo/curso/listar');
      return CursoResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return CursoResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return CursoResponseModel.fromJson(err.response!.data);
    }
  }

  Future<SimpleResponseModel> saveCurso(CursoModel curso) async {
    SimpleResponseModel responseModel;
    try {
      final response = await _rest.post(
        '/candidato/curriculo/curso/salvar',
        data: curso.toJson(),
      );
      responseModel = SimpleResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return SimpleResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return SimpleResponseModel.fromJson(err.response!.data);
    }
    return responseModel;
  }

  Future<bool> deleteCurso(int? id) async {
    bool success;
    try {
      final response =
          await _rest.delete('/candidato/curriculo/curso/excluir/$id');
      success = response.data['sucesso'] ?? false;
    } on DioException catch (err) {
      throw err.message ?? '';
    }
    return success;
  }

  Future<CategoriaIdiomaResponseModel> getCategoriasIdiomas() async {
    try {
      final response =
          await _rest.get('/candidato/curriculo/idioma/categorias');
      return CategoriaIdiomaResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return CategoriaIdiomaResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return CategoriaIdiomaResponseModel.fromJson(err.response!.data);
    }
  }

  Future<IdiomaResponseModel> getIdiomas() async {
    try {
      final response = await _rest.get('/candidato/curriculo/idioma/listar');
      return IdiomaResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return IdiomaResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return IdiomaResponseModel.fromJson(err.response!.data);
    }
  }

  Future<bool> saveIdioma(IdiomaModel idioma) async {
    bool success;
    try {
      final response = await _rest.post('/candidato/curriculo/idioma/salvar',
          data: idioma.toJson());
      success = response.data['sucesso'] ?? false;
    } on DioException catch (err) {
      throw err.message ?? '';
    }
    return success;
  }

  Future<bool> deleteIdioma(int? id) async {
    bool success;
    try {
      final response =
          await _rest.delete('/candidato/curriculo/idioma/excluir/$id');
      success = response.data['sucesso'] ?? false;
    } on DioException catch (err) {
      throw err.message ?? '';
    }
    return success;
  }

  Future<InformaticaResponseModel> getInformatica() async {
    try {
      final response =
          await _rest.get('/candidato/curriculo/informatica/listar');
      return InformaticaResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return InformaticaResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return InformaticaResponseModel.fromJson(err.response!.data);
    }
  }

  Future<bool> saveInformatica(SalvarInformaticaModel salvarModel) async {
    bool success;
    try {
      final response = await _rest.post(
          '/candidato/curriculo/informatica/salvar',
          data: salvarModel.toJson());
      success = response.data['sucesso'] ?? false;
    } on DioException catch (err) {
      throw err.message ?? '';
    }
    return success;
  }

  Future<ComplementarResponseModel> getComplementar() async {
    try {
      final response =
          await _rest.get('/candidato/curriculo/complementar/listar');
      return ComplementarResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return ComplementarResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return ComplementarResponseModel.fromJson(err.response!.data);
    }
  }

  Future<bool> saveComplementar(SalvarComplementarModel salvarModel) async {
    bool success;
    try {
      final response = await _rest.post(
          '/candidato/curriculo/complementar/salvar',
          data: salvarModel.toJson());
      success = response.data['sucesso'] ?? false;
    } on DioException catch (err) {
      throw err.message ?? '';
    }
    return success;
  }

  Future<String> downloadWord() async {
    try {
      Directory downloadsDirectory = await getApplicationDocumentsDirectory();
      var path = '${downloadsDirectory.path}/meu_curriculo.docx';
      await _rest.download('/candidato/curriculo/download/word', path);
      return path;
    } on DioException catch (err) {
      throw err.message ?? '';
    }
  }

  Future<String> downloadPdf() async {
    try {
      Directory downloadsDirectory = await getApplicationDocumentsDirectory();
      var path = '${downloadsDirectory.path}/meu_curriculo.pdf';
      await _rest.download('/candidato/curriculo/download/pdf', path);
      return path;
    } on DioException catch (err) {
      throw err.message ?? '';
    }
  }

  Future<SimpleResponseModel> salvarPrivacidade(int tipo) async {
    SimpleResponseModel responseModel;
    try {
      final response = await _rest.post(
        '/candidato/curriculo/privacidade/salvar/$tipo',
      );
      responseModel = SimpleResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return SimpleResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return SimpleResponseModel.fromJson(err.response!.data);
    }
    return responseModel;
  }
}
