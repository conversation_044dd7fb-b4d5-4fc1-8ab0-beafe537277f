class ClubeBeneficiosDetalhesModel {
  final bool? sucesso;
  final ClubeBeneficiosDetalhes? dados;

  ClubeBeneficiosDetalhesModel({
    this.sucesso,
    this.dados,
  });

  factory ClubeBeneficiosDetalhesModel.fromJson(Map<String, dynamic> json) {
    return ClubeBeneficiosDetalhesModel(
      sucesso: json['sucesso'] as bool?,
      dados: json['dados'] != null
          ? ClubeBeneficiosDetalhes.fromJson(
              json['dados'] as Map<String, dynamic>)
          : null,
    );
  }
}

class ClubeBeneficiosDetalhes {
  final int? id;
  final String? nome;
  final String? beneficio;
  final String? descricao;
  final String? logo;
  final String? palavrasChave;

  ClubeBeneficiosDetalhes({
    this.id,
    this.nome,
    this.beneficio,
    this.descricao,
    this.logo,
    this.palavrasChave,
  });

  factory ClubeBeneficiosDetalhes.fromJson(Map<String, dynamic> json) {
    return ClubeBeneficiosDetalhes(
      id: json['id'] as int?,
      nome: json['nome'] as String?,
      beneficio: json['beneficio'] as String?,
      descricao: json['descricao'] as String?,
      logo: json['logo'] as String?,
      palavrasChave: json['palavrasChave'] as String?,
    );
  }
}
