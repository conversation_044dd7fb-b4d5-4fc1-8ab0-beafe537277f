import 'pessoa_response_model.dart';

class DadosPessoaisResponseModel {
  final bool sucesso;
  final String mensagem;

  final PessoaResponseModel? pessoa;

  DadosPessoaisResponseModel({
    this.sucesso = false,
    this.pessoa,
    this.mensagem = "",
  });

  factory DadosPessoaisResponseModel.fromJson(Map<String, dynamic> json) {
    return DadosPessoaisResponseModel(
      sucesso: json['sucesso'] ?? false,
      mensagem: json['mensagem'] ?? "",
      pessoa: json['pessoa'] == null
          ? null
          : PessoaResponseModel.fromJson(
              json['pessoa'] as Map<String, dynamic>),
    );
  }
}
