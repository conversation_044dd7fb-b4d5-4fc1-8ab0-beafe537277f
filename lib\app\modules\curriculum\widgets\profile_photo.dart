import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:i18n_extension/default.i18n.dart';

class ProfilePhoto extends StatelessWidget {
  const ProfilePhoto({
    super.key,
    required this.url,
    required this.onPressed,
  });

  final String? url;
  final Function() onPressed;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      child: SizedBox(
        width: 133,
        height: 130,
        child: Card(
          elevation: 5,
          child: Column(
            children: [
              const SizedBox(height: 5),
              if (url != null)
                SizedBox(
                  width: 110,
                  height: 95,
                  child: CachedNetworkImage(
                    imageUrl: url ?? '',
                    imageBuilder: (context, imageProvider) {
                      return Container(
                        decoration: BoxDecoration(
                          image: DecorationImage(
                            image: imageProvider,
                            fit: BoxFit.fitWidth,
                            alignment: Alignment.center,
                          ),
                        ),
                      );
                    },
                    placeholder: (context, url) => SpinKitCircle(
                      color: Colors.blue[300],
                    ),
                    errorWidget: (context, url, error) => const Icon(
                      Icons.broken_image,
                      size: 60,
                    ),
                  ),
                ),
              if (url == null)
                const Icon(
                  Icons.perm_identity,
                  color: Colors.grey,
                  size: 80,
                ),
              const SizedBox(height: 5),
              FittedBox(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.photo_camera,
                      color: Colors.blue,
                      size: 15,
                    ),
                    const SizedBox(width: 5),
                    Text(
                      'ADICIONAR FOTO'.i18n,
                      style: const TextStyle(
                        color: Colors.blue,
                        fontSize: 10,
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
