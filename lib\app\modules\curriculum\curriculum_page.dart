import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';

import '../../shared/core/app_config.dart';
import '../../shared/widgets/app_usuario_card/app_usuario_card_controller.dart';
import '../home/<USER>/tab_profile_controller.dart';
import '../home/<USER>/utils/progress_indicator_app_bar.dart';
import 'controllers/deficiencies_controller.dart';
import 'curriculum_controller.dart';
import 'secoes/cursos/cursos_widget.dart';
import 'secoes/dados_pessoais/dados_pessoais_widget.dart';
import 'secoes/deficiencia/deficiencia_widget.dart';
import 'secoes/diversidade/diversidade_widget.dart';
import 'secoes/experiencia/experiencia_widget.dart';
import 'secoes/formacao_academica/formacao_academica_widget.dart';
import 'secoes/idiomas/idiomas_widget.dart';
import 'secoes/informacoes_complementares/informacoes_complementares_widget.dart';
import 'secoes/informatica/informatica_widget.dart';
import 'secoes/pretensao/objetivo_pretensao_widget.dart';
import 'secoes/privacidade/privacity.dart';
import 'secoes/redes_sociais/redes_sociais_widget.dart';
import 'secoes/sintese/sintese_widget.dart';

class CurriculumPage extends StatefulWidget {
  final bool isHelper;

  const CurriculumPage({super.key, this.isHelper = false});

  @override
  _CurriculumPageState createState() => _CurriculumPageState();
}

class _CurriculumPageState extends State<CurriculumPage> {
  final deficienciesController = Modular.get<DeficienciesController>();
  final curriculumController = Modular.get<CurriculumController>();
  final _scrollController = ScrollController();
  final controllerUsuario = Modular.get<AppUsuarioCardController>();
  final controlleTabProfile = Modular.get<TabProfileController>();

  final maskFormatterBrazil = MaskTextInputFormatter(
    mask: "(##) #####-####",
  );

  bool editingRedesSociais = false;
  bool editingDeficiencias = false;

  @override
  void initState() {
    super.initState();
    Modular.get<CurriculumController>().load();
    controlleTabProfile.getPrivacidade();
  }

  @override
  void dispose() {
    super.dispose();

    _scrollController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade100,
      appBar: AppBar(
        backgroundColor: Colors.white,
        toolbarHeight: 75,
        leadingWidth: 0,
        shadowColor: Colors.black38,
        title: ProgressIndicatorAppBar(),
        leading: const SizedBox.shrink(),
      ),
      body: Stack(
        children: [
          SingleChildScrollView(
            controller: _scrollController,
            padding: const EdgeInsets.only(bottom: 60),
            child: GetBuilder<CurriculumController>(
              init: curriculumController,
              builder: (_) => Column(
                children: [
                  Container(
                    padding: const EdgeInsets.only(
                      bottom: 4,
                    ),
                    color: AppConfig.grey,
                  ),
                  const DadosPessoaisWidget(),
                  Privacity(
                    controllerTabProfile: controlleTabProfile,
                  ),
                  const RedesSociaisWidget(),
                  const DeficienciaWidget(),
                  const DiversidadeWidget(),
                  const ObjetivoPretensaoWidget(),
                  const SinteseWidget(),
                  const FormacaoAcademicaWidget(),
                  const ExperienciaWidget(),
                  const CursosWidget(),
                  const IdiomasWidget(),
                  const InformaticaWidget(),
                  const InformacoesComplementaresWidget(),
                ],
              ),
            ),
          ),
          GetBuilder<DeficienciesController>(
            init: deficienciesController,
            builder: (_) {
              return Visibility(
                visible: deficienciesController.loading,
                child: Positioned(
                  top: 0,
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    color: Colors.white,
                    child: const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Aguarde...',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 22,
                              color: Colors.black,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          SizedBox(height: 20),
                          SpinKitCircle(color: AppConfig.colorPrimary),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
          GetBuilder<DeficienciesController>(
            builder: (_) {
              return Visibility(
                visible: deficienciesController.loadingLaudo ?? false,
                child: Positioned(
                  left: 0,
                  right: 0,
                  top: 0,
                  bottom: 0,
                  child: Container(
                    color: Colors.white,
                    child: const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: <Widget>[
                          Text(
                            'Baixando arquivo...',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 22,
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          SizedBox(height: 20),
                          SpinKitCircle(color: AppConfig.colorPrimary),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
