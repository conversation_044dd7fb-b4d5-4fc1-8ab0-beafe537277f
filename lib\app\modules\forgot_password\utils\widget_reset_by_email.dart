import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../../shared/widgets/app_default_button.dart';
import '../forgot_password_controller.dart';
import 'header_recovery_password.dart';
import 'widget_custom_toaster.dart';
import 'widget_step_question.dart';

class WidgetResetByEmail extends StatefulWidget {
  static const route = '/reset_by_email';

  const WidgetResetByEmail({
    super.key,
  });

  @override
  State<WidgetResetByEmail> createState() => _WidgetResetByEmailState();
}

class _WidgetResetByEmailState extends State<WidgetResetByEmail> {
  final controller = Modular.get<ForgotPasswordController>();

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          HeaderRecoveryPassword(
            bottomBack: true,
            title: 'Verifique seu e-mail',
            subTitle:
                'O link para você alterar sua senha foi enviado no e-mail abaixo:',
            onTap: () {},
          ),
          Padding(
            padding: const EdgeInsets.only(
              left: 24.0,
              right: 20.0,
              bottom: 32.0,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  Modular.args.data ?? '',
                  style: const TextStyle(
                    fontSize: 16,
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w700,
                    color: Color(0xff161519),
                  ),
                ),
                const SizedBox(
                  height: 8,
                ),
                const Text(
                  'Caso não encontre o e-mail, verifique em sua caixa de SPAM ou Lixo Eletrônico.',
                  style: TextStyle(
                    fontSize: 14,
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w400,
                    color: Color(0xff161519),
                  ),
                )
              ],
            ),
          ),
          const SizedBox(height: 20),
          Padding(
            padding: const EdgeInsets.only(
              left: 24.0,
              right: 24.0,
              bottom: 16,
            ),
            child: AppDefaultButton(
              withBorder: true,
              title: Text(
                'Não tenho acesso a esse e-mail'.i18n,
                style: const TextStyle(
                  fontSize: 16,
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w400,
                ),
              ),
              onPressed: () {
                alterarPorQuestionario();
              },
            ),
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Future<void> alterarPorQuestionario() async {
    final response = await controller.postIniciarQuestionario();

    if (response.sucesso) {
      Modular.to.pushNamed(WidgetStepQuestion.route);
    } else {
      WidgetCustomToaster(
        context: context,
        message: response.mensagem,
        borderRadius: 4,
        duration: const Duration(seconds: 6),
      ).show();
    }
  }
}
