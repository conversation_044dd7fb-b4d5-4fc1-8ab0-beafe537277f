import 'package:flutter/material.dart';
// import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:get/get_state_manager/get_state_manager.dart';

import '../../../../shared/core/app_translation.i18n.dart';
import '../../../../shared/models/responses/listar_cursos_response_model.dart';
import '../../curriculum_controller.dart';
import '../../curriculum_editing/curriculum_editing_module.dart';
import '../../widgets/add_button.dart';
import '../../widgets/curriculum_default_field.dart';
import '../../widgets/dialog_confirm_delete.dart';
import '../../../../shared/helpers/snack_bar_helper.dart';
import '../secao/secao_widget.dart';

class CursosWidget extends StatefulWidget {
  const CursosWidget({super.key});

  @override
  _CursosWidgetState createState() => _CursosWidgetState();
}

class _CursosWidgetState extends State<CursosWidget> {
  final CurriculumController controller = Modular.get();

  void onAddOrEdit(CursoModel toSave) async {
    controller.cursoToSave = toSave;
    var result = await Modular.to.pushNamed(
      CurriculumEditingModule.route,
      arguments: CurriculumEditingModule.cursos,
    );

    if (!mounted) return;

    if (result == true) {
      SnackbarHelper.showSnackbarSucesso(
        context,
        'Curso extracurricular salvo com sucesso',
      );
      controller.loadCursos();
    }
  }

  void onRemove(CursoModel toRemove) async {
    bool? confirmDelete = await showDialog(
      context: context,
      builder: (_) => const ConfirmDeleteDialog(secao: 'Curso extracurricular'),
    );

    if (confirmDelete == true) {
      var result = await controller.deleteCurso(toRemove.id);

      if (!mounted) return;

      if (result == true) {
        SnackbarHelper.showSnackbarSucesso(
          context,
          'Curso extracurricular removido com sucesso',
        );
        controller.loadCursos();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CurriculumController>(
      builder: (_) => SecaoWidget(
        header: 'Cursos extracurriculares'.i18n,
        showEdit: false,
        hasContent: controller.cursosSeguro.isNotEmpty,
        content: ColoredBox(
          color: Colors.white,
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 24,
              vertical: 15,
            ),
            child: Column(
              children: [
                ...controller.cursosSeguro.map(
                  (c) => Column(
                    children: [
                      DecoratedBox(
                        decoration: BoxDecoration(
                          color: const Color(0xFFf3f3fc).withValues(alpha: 0.5),
                          borderRadius: const BorderRadius.all(
                            Radius.circular(4),
                          ),
                          border: Border.all(
                            color: const Color(0xFFdee2e6),
                          ),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      c.nome!,
                                      style: const TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                      ),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(
                                      left: 5.0,
                                    ),
                                    child: Material(
                                      child: InkWell(
                                        onTap: () => onAddOrEdit(c.copy()),
                                        child: Image.asset(
                                          'lib/assets/icons/pencil-square.png',
                                          scale: 1.75,
                                        ),
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 25),
                                  Material(
                                    child: InkWell(
                                      onTap: () => onRemove(c),
                                      child: const Icon(
                                        Icons.delete,
                                        color: Colors.red,
                                        size: 24,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              Text(
                                '${c.cargaHoraria!.isNotEmpty ? '%s hora(s) - '.i18n.fill(
                                    [c.cargaHoraria!],
                                  ) : ''}${c.instituicao}',
                                style: const TextStyle(fontSize: 12),
                              ),
                              const SizedBox(height: 5),
                              Text(
                                c.anoInicio!,
                                style: const TextStyle(
                                  color: Colors.grey,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: 5,
                      )
                    ],
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                AddButton(
                  onTap: () => onAddOrEdit(CursoModel()),
                  btnTitle: 'Adcionar curso',
                ),
              ],
            ),
          ),
        ),
        noContent: CurriculumDefaultField(
          bottomAdd: () => onAddOrEdit(CursoModel()),
          textBottom: 'Adicionar curso',
          description: 'Cursos extracurriculares, certificações, etc.',
        ),
      ),
    );
  }
}
