// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../../shared/app_container.dart';
import '../../../shared/core/app_config.dart';
import '../../../shared/core/app_utils.dart';
import '../../../shared/widgets/app_default_button.dart';
import '../questionnaire_controller.dart';
import '../utils/show_bottom_sheet_photo.dart';

class WidgetPerguntaAnexo extends StatefulWidget {
  const WidgetPerguntaAnexo({
    super.key,
  });

  @override
  State<WidgetPerguntaAnexo> createState() => _WidgetPerguntaAnexoState();
}

class _WidgetPerguntaAnexoState extends State<WidgetPerguntaAnexo> {
  final controller = Modular.get<QuestionnaireController>();
  final _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    return Observer(builder: (_) {
      if (controller.anexo != null) {
        return AppContainer(
          padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 30),
          width: double.infinity,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const Text(
                'Seu arquivo está pronto, clique botão Salvar para enviar a sua resposta',
                textAlign: TextAlign.center,
              ),
              const SizedBox(
                height: 20,
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 70, vertical: 15),
                child: Container(
                  padding: const EdgeInsets.all(5),
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey,
                        offset: Offset(0.0, 0.0),
                        blurRadius: 10.0,
                        spreadRadius: 0.0,
                      )
                    ],
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.file_upload,
                        size: 45,
                      ),
                      const SizedBox(
                        width: 15,
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Arquivo ${controller.anexo!.path.split('/').last.split('.').last}',
                            style: const TextStyle(fontWeight: FontWeight.w500),
                          ),
                          Text(
                            '${(controller.anexo!.lengthSync() / umMb).toStringAsFixed(2)}mb',
                          ),
                        ],
                      )
                    ],
                  ),
                ),
              ),
              const SizedBox(
                height: 25,
              ),
              AppDefaultButton(
                color: AppConfig.green,
                onPressed: () async {
                  await controller.enviarArquivo();
                },
                title: Text(
                  'ENVIAR ARQUIVO'.i18n,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                ),
              ),
              const SizedBox(height: 25),
              InkWell(
                child: const Text(
                  'ALTERAR ARQUIVO',
                  style: TextStyle(
                    fontSize: 16,
                    color: AppConfig.colorPrimary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                onTap: () {
                  showBottomSheetPhotoQuestionnaire(
                    _scaffoldKey,
                    controller,
                    context,
                  );
                },
              ),
              const SizedBox(
                height: 15,
              ),
            ],
          ),
        );
      } else {
        return AppContainer(
          padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 30),
          width: double.infinity,
          child: Column(
            children: [
              const Text(
                'Arquivos permitidos: pdf, doc, docx, ppt, pptx, xls, xlsx, txt',
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 5),
              const SizedBox(height: 20),
              AppDefaultButton(
                onPressed: () {
                  showBottomSheetPhotoQuestionnaire(
                    _scaffoldKey,
                    controller,
                    context,
                  );
                },
                title: Text(
                  'SELECIONAR ARQUIVO'.i18n,
                  style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.white),
                ),
              ),
            ],
          ),
        );
      }
    });
  }
}
