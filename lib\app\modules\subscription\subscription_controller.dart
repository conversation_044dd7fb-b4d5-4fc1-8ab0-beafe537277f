import 'package:flutter_modular/flutter_modular.dart';
import 'package:mobx/mobx.dart';

import '../../shared/models/responses/cancelar_inscricao_response_model.dart';
import '../../shared/models/responses/inscricao_response_model.dart';
import '../../shared/repositories/inscription_repository.dart';

part 'subscription_controller.g.dart';

class SubscriptionController = _SubscriptionControllerBase
    with _$SubscriptionController;

abstract class _SubscriptionControllerBase with Store {
  final InscriptionRepository _repository = Modular.get();

  @observable
  bool loading = false;

  @observable
  InscricaoResponseModel? _inscricao;
  InscricaoResponseModel? get inscricao => _inscricao;

  @observable
  CancelarInscricaoResponseModel? _cancelarInscricao;
  CancelarInscricaoResponseModel? get cancelarInscricao => _cancelarInscricao;

  @action
  Future<void> load(int? candidaturaID) async {
    try {
      loading = true;
      _inscricao = await _repository.getInscricao(candidaturaID: candidaturaID);
    } finally {
      loading = false;
    }
  }

  @action
  Future<String?> getInscricaoSituacao(int candidaturaID) async {
    try {
      var res = await _repository.getInscricao(candidaturaID: candidaturaID);
      return res.candidatura?.situacaoText;
    } catch (error) {
      return "";
    }
  }
}
