class CategoriaIdiomaResponseModel {
  final bool sucesso;
  final String mensagem;
  final List<CategoriaIdiomaModel>? idiomas;
  final List<NivelIdiomaModel>? nivel;

  CategoriaIdiomaResponseModel({
    this.sucesso = false,
    this.mensagem = "",
    this.idiomas,
    this.nivel,
  });

  factory CategoriaIdiomaResponseModel.fromJson(Map<String, dynamic> json) {
    return CategoriaIdiomaResponseModel(
      sucesso: json['sucesso'] ?? false,
      mensagem: json['mensagem'] ?? "",
      idiomas: (json['idiomas'] as List<dynamic>?)
          ?.map((e) => CategoriaIdiomaModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      nivel: (json['nivel'] as List<dynamic>?)
          ?.map((e) => NivelIdiomaModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class CategoriaIdiomaModel {
  final int? id;
  final String? nome;

  CategoriaIdiomaModel({this.id, this.nome});

  factory CategoriaIdiomaModel.fromJson(Map<String, dynamic> json) {
    return CategoriaIdiomaModel(
      id: json['id'] as int?,
      nome: json['nome'] as String?,
    );
  }

  @override
  String toString() => nome!;

  @override
  operator ==(other) => other is CategoriaIdiomaModel && other.id == id;

  @override
  int get hashCode => id.hashCode ^ nome.hashCode;
}

class NivelIdiomaModel {
  final String? id;
  final String? nome;

  NivelIdiomaModel({this.id, this.nome});

  factory NivelIdiomaModel.fromJson(Map<String, dynamic> json) {
    return NivelIdiomaModel(
      id: json['id'] as String?,
      nome: json['nome'] as String?,
    );
  }

  @override
  String toString() => nome!;

  @override
  operator ==(other) => other is NivelIdiomaModel && other.id == id;

  @override
  int get hashCode => id.hashCode ^ nome.hashCode;
}
