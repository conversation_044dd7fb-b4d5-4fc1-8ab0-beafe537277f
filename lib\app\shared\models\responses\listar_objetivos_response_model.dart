class ObjetivoResponseModel {
  final bool sucesso;
  final String mensagem;
  final List<ObjetivoModel>? dados;

  ObjetivoResponseModel({this.sucesso = false, this.mensagem = "", this.dados});

  factory ObjetivoResponseModel.fromJson(Map<String, dynamic> json) {
    return ObjetivoResponseModel(
      sucesso: json['sucesso'] ?? false,
      mensagem: json['mensagem'] ?? "",
      dados: (json['dados'] as List<dynamic>?)
          ?.map((e) => ObjetivoModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class ObjetivoModel {
  final int? id;
  final String? nome;

  ObjetivoModel({this.id, this.nome});

  factory ObjetivoModel.fromJson(Map<String, dynamic> json) {
    return ObjetivoModel(
      id: json['id'] as int?,
      nome: json['nome'] as String?,
    );
  }
}
