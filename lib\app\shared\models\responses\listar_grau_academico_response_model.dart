class GrauAcademicoResponseModel {
  final bool sucesso;
  final String mensagem;
  final List<GrauAcademicoModel>? grauAcademico;

  GrauAcademicoResponseModel({
    this.sucesso = false,
    this.mensagem = "",
    this.grauAcademico,
  });

  factory GrauAcademicoResponseModel.fromJson(Map<String, dynamic> json) {
    return GrauAcademicoResponseModel(
      sucesso: json['sucesso'] ?? false,
      mensagem: json['mensagem'] ?? "",
      grauAcademico: (json['grauAcademico'] as List<dynamic>?)
          ?.map((e) => GrauAcademicoModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class GrauAcademicoModel {
  final int? id;
  final String? nome;
  final int? ordem;

  GrauAcademicoModel({this.id, this.nome, this.ordem});
  factory GrauAcademicoModel.fromJson(Map<String, dynamic> json) {
    return GrauAcademicoModel(
      id: json['id'] as int?,
      nome: json['nome'] as String?,
      ordem: json['ordem'] as int?,
    );
  }

  String get nomeSeguro => nome ?? '';

  @override
  String toString() => nomeSeguro;

  @override
  operator ==(other) => other is GrauAcademicoModel && other.id == id;

  @override
  int get hashCode => id.hashCode ^ nomeSeguro.hashCode;
}
