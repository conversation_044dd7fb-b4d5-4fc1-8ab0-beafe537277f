import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

import '../../../shared/core/app_config.dart';
import '../forgot_password_controller.dart';

class WidgetStepQuestion extends StatelessWidget {
  static const route = '/build_step_question';

  final controller = Modular.get<ForgotPasswordController>();

  WidgetStepQuestion({super.key});

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(
              top: 30.0,
              left: 14.0,
            ),
            child: InkWell(
              onTap: () => Navigator.of(context).pop(),
              borderRadius: BorderRadius.circular(20),
              child: const SizedBox(
                height: 50,
                width: 50,
                child: Material(
                  shape: CircleBorder(),
                  color: Colors.transparent,
                  child: Icon(
                    Icons.arrow_back_ios_new,
                    color: Colors.black,
                  ),
                ),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              top: 32.0,
              left: 24.0,
              right: 121.0,
              bottom: 30.0,
            ),
            child: Image.asset(
              'lib/assets/images/logo-empregare-splash.png',
              fit: BoxFit.cover,
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Observer(
                  builder: (_) {
                    var progress = (controller.questionStep) / 6;

                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        LinearProgressIndicator(
                          value: progress,
                          backgroundColor: const Color(0xffc5d7fb),
                          valueColor: const AlwaysStoppedAnimation<Color>(
                            Color(0xff2c6ef2),
                          ),
                          borderRadius: BorderRadius.circular(6),
                          minHeight: 4,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'ETAPA ${controller.questionStep} DE 6',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    );
                  },
                ),
                const SizedBox(height: 20),
                const Text(
                  'Selecione uma opção',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 24,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 4),
                Observer(
                  builder: (_) {
                    return Text(
                      controller.perguntaAtual?.titulo ?? '',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          Expanded(
            child: Observer(
              builder: (_) {
                if (controller.loading) {
                  return const Center(
                    child: SpinKitCircle(
                      color: AppConfig.colorPrimary,
                    ),
                  );
                }

                return Padding(
                  padding: const EdgeInsets.only(left: 14),
                  child: ListView.builder(
                    itemCount:
                        (controller.perguntaAtual?.alternativas ?? []).length,
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                      var alternative =
                          controller.perguntaAtual!.alternativas![index];

                      return InkWell(
                        onTap: () {
                          controller.postVerificarResposta(alternative);
                        },
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 5),
                          child: Row(
                            children: [
                              Checkbox(
                                shape: const CircleBorder(),
                                activeColor: Colors.blue,
                                checkColor: Colors.white,
                                materialTapTargetSize:
                                    MaterialTapTargetSize.shrinkWrap,
                                side: const BorderSide(
                                  color: Color(0xff292929),
                                  width: 2,
                                ),
                                value: controller.perguntaAtual
                                        ?.alternativaSelecionada ==
                                    alternative,
                                onChanged: (value) {
                                  controller.postVerificarResposta(
                                    alternative,
                                  );
                                },
                              ),
                              Expanded(
                                child: Text(
                                  alternative,
                                  style: const TextStyle(
                                    color: Color(0xff292929),
                                    fontSize: 15,
                                    fontFamily: 'Inter',
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
