import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';

import '../../../shared/core/app_config.dart';
import '../../../shared/core/app_translation.i18n.dart';
import '../../../shared/models/job_busca.dart';
import '../../../shared/widgets/app_bar_default.dart';
import '../../../shared/widgets/app_default_button.dart';
import 'jobs_search_controller.dart';

class JobsSearchPage extends StatefulWidget {
  final JobBuscaModel? busca;

  const JobsSearchPage({super.key, required this.busca});

  @override
  _JobsSearchPageState createState() => _JobsSearchPageState();
}

class _JobsSearchPageState extends State<JobsSearchPage> {
  final controller = Modular.get<JobsSearchController>();

  TextEditingController? _cargoOuEmpresaController;
  TextEditingController? _cidadeUfOuPaisController;

  final _cargoFodus = FocusNode();

  @override
  void initState() {
    super.initState();

    controller.load();
    controller.setBuscar(widget.busca ?? JobBuscaModel());

    _cargoOuEmpresaController = TextEditingController(
      text: widget.busca?.cargoOuEmpresa,
    );

    _cidadeUfOuPaisController = TextEditingController(
      text: widget.busca?.cidadeUfOuPais,
    );

    _cargoFodus.requestFocus();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: const AppBarDefault(
        titleText: 'Buscar Vagas',
      ),
      body: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(15),
            child: Column(
              children: [
                TextField(
                  controller: _cargoOuEmpresaController,
                  focusNode: _cargoFodus,
                  decoration: InputDecoration(
                    labelText: 'Cargo ou empresa'.i18n,
                    prefixIcon: const Icon(Icons.work),
                  ),
                  onChanged: controller.setCargoOuEmpresa,
                ),
                TypeAheadField(
                  hideOnEmpty: true,
                  builder: (context, controller, focusNode) {
                    return TextField(
                      controller: _cidadeUfOuPaisController,
                      focusNode: focusNode,
                      autofocus: true,
                      decoration: InputDecoration(
                        prefixIcon: const Icon(Icons.public),
                        labelText: 'Cidade, estado ou país'.i18n,
                      ),
                    );
                  },
                  emptyBuilder: (context) {
                    return const Padding(
                      padding: EdgeInsets.symmetric(vertical: 8.0),
                      child: Text(
                        'Nenhum Item Encontrado!',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.black54,
                        ),
                      ),
                    );
                  },
                  suggestionsCallback: (pattern) async {
                    return (await controller.getCidades(pattern))!;
                  },
                  itemBuilder: (context, dynamic suggestion) {
                    return ListTile(
                      title: Text(suggestion.nome),
                    );
                  },
                  onSelected: (suggestion) {
                    _cidadeUfOuPaisController!.text = suggestion.nome ?? "";
                    controller.setCidadeUfOuPais(suggestion.nome ?? "");
                  },
                ),
                const SizedBox(height: 15),
                AppDefaultButton(
                  title: const Text(
                    'BUSCAR',
                    style: TextStyle(color: Colors.white),
                  ),
                  onPressed: () => controller.buscar(),
                ),
                const SizedBox(height: 15),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(15),
            alignment: Alignment.centerLeft,
            color: AppConfig.grey,
            width: double.infinity,
            child: Text(
              'Últimas Buscas'.i18n,
              textAlign: TextAlign.left,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Flexible(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(15),
              child: Observer(
                builder: (_) => Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    ...controller.buscas.map(
                      (busca) => Column(
                        children: <Widget>[
                          ListTile(
                            leading: const Icon(Icons.search),
                            title: Visibility(
                              visible: busca!.cargoOuEmpresa != null &&
                                  busca.cargoOuEmpresa!.isNotEmpty,
                              child: Text(busca.cargoOuEmpresa ?? ''),
                            ),
                            subtitle: Visibility(
                              visible: busca.cidadeUfOuPais != null &&
                                  busca.cidadeUfOuPais!.isNotEmpty,
                              child: Text(busca.cidadeUfOuPais ?? ''),
                            ),
                            onTap: () {
                              _cargoOuEmpresaController!.text =
                                  busca.cargoOuEmpresa!;
                              _cidadeUfOuPaisController!.text =
                                  busca.cidadeUfOuPais!;
                              controller.setBuscar(busca);
                            },
                          ),
                          const Divider()
                        ],
                      ),
                    )
                  ],
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}
