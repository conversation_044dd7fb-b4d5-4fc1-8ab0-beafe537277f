import 'package:flutter_modular/flutter_modular.dart';
import 'package:mobx/mobx.dart';

import '../../shared/models/job_parameter_model.dart';
import '../../shared/models/responses/inscricoes_response_model.dart';
import '../../shared/repositories/dashboard_repository.dart';
import '../../shared/repositories/job_repository.dart';
import '../notification/notification_module.dart';

part 'tab_panel_controller.g.dart';

class TabPanelController = _TabPanelControllerBase with _$TabPanelController;

abstract class _TabPanelControllerBase with Store {
  final DashboardRepository _drepository = Modular.get();
  final JobRepository _repository = Modular.get();

  @observable
  bool loading = true;

  @observable
  int pagina = 0;

  int? _totalPaginas;
  int? get totalPaginas => _totalPaginas;

  @observable
  List<JobModel> _jobs = <JobModel>[].asObservable();
  List<JobModel> get jobs => _jobs;

  @observable
  bool? _temNotificacao;
  bool? get temNotificacao => _temNotificacao;

  @observable
  int _countNotificacao = 0;
  int get countNotificacao => _countNotificacao;

  @action
  Future<void> load() async {
    var response = await _drepository.getNotificacoes();
    _temNotificacao = response.temNoticacao;

    _countNotificacao = (response.agenda!.length) +
        (response.questionarios!.length) +
        ((response.talentoday ?? false) ? 1 : 0);

    if (_temNotificacao!) Modular.to.pushNamed(NotificationModule.route);
  }

  @action
  Future<void> loadJobs(JobParameterModel filtros) async {
    try {
      loading = true;
      var data = await _repository.getSugestoes(filtros: filtros);

      if (data.dados!.isNotEmpty) {
        jobs.addAll(data.dados!);
        _totalPaginas = data.totalPaginas!.floor();
      }
    } finally {
      loading = false;
    }
  }
}
