class AreasResponseModel {
  final List<AreaModel>? data;

  AreasResponseModel({this.data});

  factory AreasResponseModel.fromJson(Map<String, dynamic> json) {
    return AreasResponseModel(
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => AreaModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class AreaModel {
  final int? id;
  final String? nome;

  AreaModel({this.id, this.nome});

  factory AreaModel.fromJson(Map<String, dynamic> json) {
    return AreaModel(
      id: json['id'] as int?,
      nome: json['nome'] as String?,
    );
  }

  String get nomeSeguro => nome ?? '';

  @override
  String toString() => nomeSeguro;

  @override
  operator ==(other) => other is AreaModel && other.id == id;
  @override
  int get hashCode => id.hashCode ^ nomeSeguro.hashCode;
}
