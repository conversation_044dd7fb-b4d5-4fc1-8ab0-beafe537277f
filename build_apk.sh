#!/bin/bash

CURRENT_DATE=$(date +"%d%m%Y")

CYAN_BOLD='\033[1;36m'
GREEN='\033[0;32m'
RED='\033[0;31m'

RESET='\033[0m'

echo -e "${CYAN_BOLD}~> Informe o nome do aplicativo:${RESET}"
read -r APP_NAME

echo -e "${CYAN_BOLD}~> Deseja adicionar o ambiente no nome do arquivo? (s/n)${RESET}"
read -r response_env
if [[ "$response_env" =~ ^[Ss]$ ]]; then
  echo -e "${CYAN_BOLD}~> Informe o ambiente (dev, prod, hmg):${RESET}"
  read -r ENV
  APK_FILE="${APP_NAME}-${ENV}-${CURRENT_DATE}.apk"
else
  APK_FILE="${APP_NAME}-${CURRENT_DATE}.apk"
fi

DESTINATION="./"

echo -e "\n${CYAN_BOLD}~> flutter clean em execução...${RESET}"
echo -e "${GREEN}~> Recomendado rodar o comando flutter clean antes de gerar o APK.${RESET}"
echo -e "${CYAN_BOLD}~> Deseja rodar o comando flutter clean agora? (s/n)${RESET}"
read -r response_clean
if [[ "$response_clean" =~ ^[Ss]$ ]]; then
  flutter clean
  echo -e "${GREEN}~> flutter clean terminou!${RESET}\n"

  echo -e "${CYAN_BOLD}~> flutter pub get em execução...${RESET}"
  flutter pub get
  echo -e "${GREEN}~> flutter pub get terminou!${RESET}\n"
else
  echo -e "${GREEN}~> flutter clean ignorado.${RESET}\n"
fi

echo -e "${CYAN_BOLD}~> flutter build apk em execução...${RESET}"
echo -e "\n${CYAN_BOLD}~> Informe as flags adicionais para o comando flutter build apk:${RESET}"
echo -e " 1 - --no-sound-null-safety"
echo -e " 2 - --no-tree-shake-icons"
echo -e " 3 - Outra flag customizada"
echo -e " 0 - Nenhuma flag adicional"

read -r OPTION

FLAGS=""
case "$OPTION" in
  1) FLAGS="--no-sound-null-safety" ;;
  2) FLAGS="--no-tree-shake-icons" ;;
  3) echo -e "Informe a flag customizada:"
     read -r CUSTOM_FLAG
     FLAGS="$CUSTOM_FLAG" ;;
  0) FLAGS="" ;;
  *) echo -e "${RED}Opção inválida! Nenhuma flag será utilizada.${RESET}"
     FLAGS="" ;;
esac
flutter build apk $FLAGS
echo -e "${GREEN}~> flutter build apk terminou!${RESET}\n"

if mv ./build/app/outputs/flutter-apk/app-release.apk "$DESTINATION/$APK_FILE" 2>/dev/null; then
  echo -e "${GREEN}Apk gerado com sucesso em $APK_FILE ${RESET}"
else
  echo -e "${RED}Erro ao mover o arquivo APK!${RESET}"
  echo -e "Verifique se o app-release.apk existe em build/app/outputs/flutter-apk/\n"
fi