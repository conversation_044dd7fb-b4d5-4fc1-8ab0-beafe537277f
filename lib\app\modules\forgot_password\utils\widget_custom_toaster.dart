import 'package:flutter/material.dart';

class WidgetCustomToaster {
  final BuildContext context;
  final String message;
  final Duration duration;
  final Color? backgroundColor;
  final double borderRadius;

  WidgetCustomToaster({
    required this.context,
    required this.message,
    this.duration = const Duration(seconds: 3),
    this.backgroundColor,
    this.borderRadius = 4,
  });

  void show() {
    final overlay = Overlay.of(context);
    final GlobalKey<_CustomSnackbarState> key =
        GlobalKey<_CustomSnackbarState>();

    OverlayEntry? overlayEntry;

    overlayEntry = OverlayEntry(
      builder: (context) => _CustomSnackbar(
        key: key,
        message: message,
        backgroundColor: backgroundColor ?? const Color(0xFFF44336),
        borderRadius: borderRadius,
        duration: duration,
        onDismissed: () {
          overlayEntry?.remove();
        },
      ),
    );

    overlay.insert(overlayEntry);

    Future.delayed(duration, () {
      key.currentState?.hideSnackbar();
    });
  }
}

class _CustomSnackbar extends StatefulWidget {
  final String message;
  final Duration duration;
  final Color backgroundColor;
  final double borderRadius;
  final VoidCallback onDismissed;

  const _CustomSnackbar({
    required Key key,
    required this.message,
    required this.duration,
    required this.backgroundColor,
    required this.borderRadius,
    required this.onDismissed,
  }) : super(key: key);

  @override
  _CustomSnackbarState createState() => _CustomSnackbarState();
}

class _CustomSnackbarState extends State<_CustomSnackbar>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Offset> _offsetAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _offsetAnimation = Tween<Offset>(
      begin: const Offset(0, -1),
      end: const Offset(0, 0),
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void hideSnackbar() {
    _animationController.reverse().then((value) {
      widget.onDismissed();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: MediaQuery.of(context).padding.top + 16,
      left: 16,
      right: 16,
      child: SlideTransition(
        position: _offsetAnimation,
        child: Material(
          elevation: 4,
          borderRadius: BorderRadius.circular(widget.borderRadius),
          color: widget.backgroundColor,
          child: GestureDetector(
            onVerticalDragEnd: (details) {
              if (details.primaryVelocity! < 0) {
                hideSnackbar();
              }
            },
            onHorizontalDragEnd: (details) {
              if (details.primaryVelocity! != 0) {
                hideSnackbar();
              }
            },
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Padding(
                    padding: EdgeInsets.only(
                      right: 8,
                    ),
                    child: Icon(
                      Icons.error_outline,
                      color: Colors.white,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      widget.message,
                      style: const TextStyle(
                        fontSize: 14,
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w400,
                        color: Colors.white,
                      ),
                      textAlign: TextAlign.justify,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
