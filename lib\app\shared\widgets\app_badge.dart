import 'package:flutter/material.dart';

import '../core/app_config.dart';

class AppBadge extends StatelessWidget {
  final String? title;
  final Function? onRemove;

  const AppBadge({super.key, this.title, this.onRemove});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onRemove as void Function()?,
      child: FittedBox(
        child: Container(
          padding: const EdgeInsets.all(5),
          margin: const EdgeInsets.all(3),
          decoration: BoxDecoration(
            border: Border.all(
              color: const Color(0xFF9E9EEF),
              width: 1.5,
            ),
            borderRadius: BorderRadius.circular(500),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              if (onRemove != null)
                SizedBox(
                  height: 10,
                  width: 10,
                  child: Image.asset(
                    'lib/assets/images/close.png',
                  ),
                ),
              if (onRemove != null) const SizedBox(width: 5),
              Text(
                title!,
                style: const TextStyle(
                  fontSize: 10.5,
                  color: AppConfig.colorPrimary,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
