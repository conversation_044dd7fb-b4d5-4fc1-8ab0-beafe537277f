class CidadeResponseModel {
  final bool sucesso;
  final String mensagem;
  final List<CidadeModel>? dados;

  CidadeResponseModel({
    this.sucesso = false,
    this.mensagem = "",
    this.dados,
  });

  factory CidadeResponseModel.fromJson(Map<String, dynamic> json) {
    return CidadeResponseModel(
      sucesso: json['sucesso'] ?? false,
      mensagem: json['mensagem'] ?? "",
      dados: (json['dados'] as List<dynamic>?)
          ?.map((e) => CidadeModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class CidadeModel {
  final int? id;
  final String? nome;
  final String? uf;
  final String? paisID;

  CidadeModel({this.id, this.nome, this.uf, this.paisID});

  factory CidadeModel.fromJson(Map<String, dynamic> json) {
    return CidadeModel(
      id: json['id'] as int?,
      nome: json['nome'] as String?,
      uf: json['uf'] as String?,
      paisID: json['paisID'] as String?,
    );
  }

  @override
  String toString() {
    return nome!;
  }
}
