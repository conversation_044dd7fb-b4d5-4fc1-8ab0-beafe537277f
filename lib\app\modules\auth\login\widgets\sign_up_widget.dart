import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../signup/signup_module.dart';

class SignUpWidget extends StatelessWidget {
  const SignUpWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              "Não possui uma conta?".i18n,
              style: const TextStyle(
                fontFamily: 'Inter',
                fontSize: 14,
                color: Color(0xff161519),
              ),
            ),
          ],
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 15.0),
          child: SizedBox(
            height: 45.0,
            width: double.infinity,
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6.0),
                ),
                shadowColor: Colors.transparent,
                elevation: 0,
                side: const BorderSide(
                  color: Color(0xff1b1b63),
                  width: 1.0,
                ),
                backgroundColor: const Color(0xFFFFFFFF),
              ),
              child: Text(
                'Cadastre-se'.i18n,
                style: const TextStyle(
                  color: Color(0xff1b1b63),
                ),
              ),
              onPressed: () async {
                await Modular.to.pushNamed(
                  SignupModule.route,
                );
              },
            ),
          ),
        ),
      ],
    );
  }
}
