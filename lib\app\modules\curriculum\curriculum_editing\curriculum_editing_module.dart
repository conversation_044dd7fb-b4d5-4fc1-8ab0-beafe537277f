import 'package:flutter_modular/flutter_modular.dart';

import 'curriculum_editing_controller.dart';
import 'curriculum_editing_page.dart';

class CurriculumEditingModule extends Module {
  static const route = '/curriculum/editing/';

  static const dadosPessoais = "Dados Pessoais";
  static const redesSociais = "Redes Sociais";
  static const deficiencias = "Deficiências";
  static const objetivoPretensao = "Objetivo e Pretensão Salarial";
  static const cartaApresentacao = "Síntese";
  static const formacaoAcademica = "Formação Acadêmica";
  static const experiencias = "Experiências";
  static const diversidade = "Diversidade";
  static const cursos = "Cursos Extracurriculares";
  static const idiomas = "Idiomas";
  static const informatica = "Informática";
  static const informacoesComplementares = "Informações Complementares";

  @override
  void binds(i) {
    i.addLazySingleton(CurriculumEditingController.new);
  }

  @override
  void routes(r) {
    r.child(
      '/',
      child: (context) => CurriculumEditingPage(
        secao: r.args.data ?? dadosPessoais,
      ),
    );
  }
}
