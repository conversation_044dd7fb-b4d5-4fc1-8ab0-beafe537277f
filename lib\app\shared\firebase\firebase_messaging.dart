// ignore_for_file: prefer_interpolation_to_compose_strings, avoid_print

import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../modules/chat/chat_module.dart';
import '../../modules/curriculum/curriculum_module.dart';
import '../../modules/home/<USER>';
import '../../modules/jobs/job_detail/job_detail_module.dart';
import '../../modules/schedule/schedule_module.dart';
import '../repositories/dashboard_repository.dart';
import '../services/session_service.dart';
import '../services/storage_service.dart';

@pragma('vm:entry-point')
Future _onIOSDidReceiveLocalNotification(
  int id,
  String? title,
  String? body,
  String? jsonStr,
) async {}

@pragma('vm:entry-point')
void _onDidReceiveBackgroundNotificationResponse(
  NotificationResponse notification,
) {
  FirebaseMessagingUtils.instance.onClickNotification(notification.payload);
}

class FirebaseMessagingUtils {
  FirebaseMessagingUtils._();
  static FirebaseMessagingUtils instance = FirebaseMessagingUtils._();

  final flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

  final _firebaseMessaging = FirebaseMessaging.instance;

  static final StorageService _storageService = StorageService();

  Future<void> initialize() async {
    try {
      await _setupLocalNotifications();
      await _setupMessaging();
    } catch (error) {
      log("Não foi possível inicializar o FirebaseMessaging");
    }
  }

  /*
   * Local Notifications
   * Receives local notifications inside the app
   * Used in conjunction with Firebase for internal notifications
   */
  Future<void> _setupLocalNotifications() async {
    var initializationSettings = const InitializationSettings(
      android: AndroidInitializationSettings('ic_notification'),
      iOS: DarwinInitializationSettings(
        onDidReceiveLocalNotification: _onIOSDidReceiveLocalNotification,
      ),
    );

    await flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse:
          _onDidReceiveBackgroundNotificationResponse,
      onDidReceiveBackgroundNotificationResponse:
          _onDidReceiveBackgroundNotificationResponse,
    );
  }

  Future<void> navigateTo({String? pTela}) async {
    pTela = pTela ?? await (_storageService.get('TelaNotificacao'));

    if (pTela != null) {
      var tela = pTela.split("-");

      if (tela[0] == "Painel") {
        Modular.to.pushNamedAndRemoveUntil(HomeModule.route, (_) => false);
      } else if (tela[0] == "Vaga") {
        Modular.to
            .pushNamed(JobDetailModule.route, arguments: int.parse(tela[1]));
      } else if (tela[0] == "Mensagem") {
        Modular.to.pushNamed(ChatModule.route, arguments: tela[1].toString());
      } else if (tela[0] == "Agenda") {
        Modular.to
            .pushNamed(ScheduleModule.route, arguments: tela[1].toString());
      } else if (tela[0] == "Curriculo") {
        Modular.to.pushNamed(CurriculumModule.route);
      }
    }
    await _storageService.remove('TelaNotificacao');
  }

  /*
   * Firebase Cloud Messaging
   * Notifications
   */
  Future<String?> getCurrentToken() async {
    try {
      if (Platform.isIOS) {
        final apnsToken = await _firebaseMessaging.getAPNSToken();
        if (apnsToken == null) {
          // Aguarda um pouco e tenta novamente
          await Future.delayed(const Duration(seconds: 2));
          return await _firebaseMessaging.getToken();
        }
      }
      return await _firebaseMessaging.getToken();
    } catch (error) {
      log("[FIREBASE] Erro ao obter token: $error");
      return null;
    }
  }

  Future<void> updateToken() async {
    try {
      bool isLoggedIn =
          (await Modular.tryGet<SessionService>()?.isLoggedIn()) ?? false;

      if (isLoggedIn) {
        final token = await getCurrentToken();
        if (token != null) {
          try {
            await Modular.get<DashboardRepository>()
                .updateTokenNotificacao(token);
            log("[FIREBASE] Token atualizado com sucesso: $token");
          } catch (e) {
            log("[FIREBASE] Erro ao atualizar token no backend: $e");
          }
        }

        _firebaseMessaging.onTokenRefresh.listen((token) async {
          try {
            await Modular.get<DashboardRepository>()
                .updateTokenNotificacao(token);
            log("[FIREBASE] Token atualizado com sucesso (refresh): $token");
          } catch (e) {
            log("[FIREBASE] Erro ao atualizar token no backend (refresh): $e");
          }
        });
      }
    } catch (error) {
      log("[FIREBASE] Erro ao atualizar token: $error");
    }
  }

  Future<void> _setupMessaging() async {
    // Permission for Notifications
    await _requestIOSPermissions();

    // Update Token on backend, if user is logged
    await updateToken();

    // Subscribe
    await _subscribeNotifications();

    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      _handleMessages(message);
    });

    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      _handleClickAppClosed(message);
    });
  }

  Future<void> _requestIOSPermissions() async {
    await _firebaseMessaging.requestPermission(
        alert: true, badge: true, sound: true);

    /// Update the iOS foreground notification presentation options to allow
    /// heads up notifications.
    _firebaseMessaging.setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );
  }

  Future<void> _subscribeNotifications() async {
    //"debug" | "global"
    await _firebaseMessaging.subscribeToTopic("global");
    print("[FIREBASE] Subscribed to topic Global!");
  }

  Future<void> _handleMessages(RemoteMessage message) async {
    if (message.notification != null) {
      _handleNotifications(message.notification!, message.data);
    }
  }

  Future<void> _handleNotifications(
      RemoteNotification notification, Map<dynamic, dynamic> data) async {
    // Bugfix
    if (notification.title != null) {
      print(
          "[FIREBASE] Notification: [${notification.title} ] / Message: [${notification.body} ]");

      // Create Notification
      var androidPlatformChannelSpecifics = const AndroidNotificationDetails(
          'empregare', 'Empregare',
          channelDescription: 'Empregare',
          importance: Importance.max,
          priority: Priority.high);
      var iOSPlatformChannelSpecifics = const DarwinNotificationDetails();

      var platformChannelSpecifics = NotificationDetails(
          android: androidPlatformChannelSpecifics,
          iOS: iOSPlatformChannelSpecifics);

      await flutterLocalNotificationsPlugin.show(
          0, notification.title, notification.body, platformChannelSpecifics,
          payload: _jsonfyNotificationData(data));
    }
  }

  Future<void> _handleClickAppClosed(RemoteMessage message) async {
    await onClickNotification(_jsonfyNotificationData(message.data),
        appClosed: true);
  }

  String _jsonfyNotificationData(Map<dynamic, dynamic> data) {
    return json.encode(data.map((a, b) => MapEntry(a as String, b.toString())));
  }

  Future<void> onClickNotification(String? jsonStr,
      {bool appClosed = false}) async {
    Map<String, String> parameters =
        Map<String, String>.from(json.decode(jsonStr!));

    // Parameters
    var pTela = parameters['Tela'];

    if (appClosed) {
      _storageService.put('TelaNotificacao', pTela);
    } else {
      navigateTo(pTela: pTela);
    }
  }
}
