import 'package:flutter/material.dart';

import '../core/app_config.dart';
import '../core/app_translation.i18n.dart';

class AppAddInfo extends StatelessWidget {
  final String? title;
  final String? description;
  final String? btnAddTitle;
  final Function? onAdd;
  final Function? onNotAdd;
  final Function? dontHave;

  const AppAddInfo({
    super.key,
    this.title,
    this.description,
    this.btnAddTitle,
    this.onAdd,
    this.onNotAdd,
    this.dontHave,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 7, vertical: 10),
      decoration: BoxDecoration(
        color: const Color(0xFFf3f3fc).withValues(alpha: 0.5),
        border: Border.all(color: const Color(0xFFdee2e6)),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        children: <Widget>[
          Text(
            title!,
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            description!,
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w300,
            ),
          ),
          const SizedBox(height: 10),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              mainAxisAlignment: onNotAdd != null
                  ? MainAxisAlignment.spaceBetween
                  : MainAxisAlignment.center,
              children: <Widget>[
                InkWell(
                  onTap: onAdd as void Function()?,
                  child: DecoratedBox(
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: AppConfig.colorPrimary,
                      ),
                      borderRadius: const BorderRadius.all(
                        Radius.circular(6),
                      ),
                      color: AppConfig.colorPrimary,
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(12),
                      child: Center(
                        child: Row(
                          children: [
                            const Icon(
                              Icons.add,
                              color: Colors.white,
                              size: 18,
                            ),
                            const SizedBox(width: 5),
                            Text(
                              btnAddTitle!,
                              style: const TextStyle(
                                color: AppConfig.white,
                                fontFamily: 'Inter',
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                Visibility(
                  visible: onNotAdd != null,
                  child: InkWell(
                    onTap: dontHave as void Function()?,
                    child: DecoratedBox(
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: AppConfig.colorPrimary,
                        ),
                        borderRadius: const BorderRadius.all(
                          Radius.circular(6),
                        ),
                        color: AppConfig.white,
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(12),
                        child: Center(
                          child: Text(
                            "NÃO POSSUO".i18n,
                            style: const TextStyle(
                              color: AppConfig.colorPrimary,
                              fontFamily: 'Inter',
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
