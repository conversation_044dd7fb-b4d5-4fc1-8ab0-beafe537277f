import 'article_response_model.dart';

class DashArticlesResponseModel {
  final bool sucesso;
  final String mensagem;
  final List<ArticleResponseModel> dados;

  DashArticlesResponseModel({
    required this.sucesso,
    required this.mensagem,
    required this.dados,
  });

  factory DashArticlesResponseModel.fromMap(Map<String, dynamic> map) {
    return DashArticlesResponseModel(
      sucesso: map['sucesso'] ?? false,
      mensagem: map['mensagem'] ?? '',
      dados: List<ArticleResponseModel>.from(
        map['dados']?.map((item) => ArticleResponseModel.fromMap(item)) ?? [],
      ),
    );
  }
}
