import 'package:flutter/material.dart';

import '../../../../shared/core/app_config.dart';

class Custom<PERSON>heck<PERSON>ox extends StatelessWidget {
  final bool value;
  final ValueChanged<bool>? onChanged;
  final double? tamanho;

  const CustomCheckBox({
    super.key,
    this.value = false,
    this.onChanged,
    this.tamanho = 24.0,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => onChanged!(!value),
      child: Container(
        width: tamanho,
        height: tamanho,
        decoration: BoxDecoration(
          color: value ? AppConfig.colorPrimary : AppConfig.white,
          shape: BoxShape.rectangle,
          borderRadius: BorderRadius.circular(2),
          border: Border.all(
            width: 2.0,
            color: AppConfig.colorPrimary,
          ),
        ),
        child: value
            ? Image.asset(
                'lib/assets/images/checkbox.png',
                height: 10,
                fit: BoxFit.cover,
              )
            : null,
      ),
    );
  }
}
