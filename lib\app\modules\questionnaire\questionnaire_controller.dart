// ignore_for_file: unrelated_type_equality_checks

import 'dart:async';
import 'dart:io';

import 'package:flutter_modular/flutter_modular.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mobx/mobx.dart';
import 'package:multi_dropdown/multiselect_dropdown.dart';
import 'package:uuid/uuid.dart';
import 'package:video_compress/video_compress.dart';

import '../../shared/core/app_utils.dart';
import '../../shared/libs/file_picker_lib.dart';
import '../../shared/libs/image_picker_lib.dart';
import '../../shared/models/prepare_metadata_model.dart';
import '../../shared/models/questionario_salvar_pergunta_model.dart';
import '../../shared/models/responses/pergunta_response_model.dart';
import '../../shared/models/responses/questionario_response_model.dart';
import '../../shared/repositories/questionneire_repository.dart';
import 'questionnaire_video/questionnaire_video_module.dart';

part 'questionnaire_controller.g.dart';

enum QuestionnaireState { idle, loading, blocked, guideLines, responding, progress, finishe }

class QuestionnaireController = _QuestionnaireControllerBase with _$QuestionnaireController;

abstract class _QuestionnaireControllerBase with Store {
  final QuestionnaireRepository _repository = Modular.get();

  bool comprimindo = false;

  String? idUpload;

  @observable
  QuestionnaireState state = QuestionnaireState.loading;

  @observable
  int _index = 0;
  int get index => _index;

  @observable
  double _progress = 0;
  double get progress => _progress / 100.0;

  @observable
  String? _progressLegend;
  String get progressLegend => '${_progress.round()}%';

  @observable
  String? _progressMessage;
  String? get progressMessage => _progressMessage;

  @observable
  int? _tempo;
  int? get tempo => _tempo;

  @observable
  String? _tempoRestante;
  String? get tempoRestante => _tempoRestante;

  int get respondias => _index == 0 ? 0 : _index - 1;

  @observable
  String? _conteudo;
  String? get conteudo => _conteudo;

  @observable
  ObservableList<int> alternativasSelecionadas = ObservableList<int>();

  @observable
  int? selectedAlternative;

  @observable
  List<List<ValueItem>>? selectAlter;

  @observable
  MultiSelectController? multiSelectController;

  @observable
  File? video;

  @observable
  File? anexo;

  @observable
  QuestionarioModel? questionario;

  @observable
  PerguntaModel? pergunta;

  int? get _tipo {
    if (pergunta!.tipo == escolhaUnica) {
      return 1;
    } else if (pergunta!.tipo == respostaMultilinha) {
      return 2;
    } else if (pergunta!.tipo == video) {
      return 3;
    } else if (pergunta!.tipo == anexo) {
      return 4;
    } else if (pergunta!.tipo == respostaCurta) {
      return 5;
    } else if (pergunta!.tipo == listaSuspensaMultipla) {
      return 6;
    } else if (pergunta!.tipo == listaSuspensa) {
      return 7;
    } else if (pergunta!.tipo == caixaSelecao) {
      return 8;
    } else if (pergunta!.tipo == escolhaMultipla) {
      return 9;
    }

    return null;
  }

  String? get appBarTitle => questionario != null ? questionario!.titulo : 'Responder Questionário';

  String get appBarSubTitle => questionario != null
      ? state == QuestionnaireState.guideLines
          ? '${questionario!.totalRespondido ?? 0} de ${questionario!.totalPerguntas} respondidas'
          : '$respondias de ${questionario!.totalPerguntas} respondidas'
      : 'Empregare';

  String get totalQuestoes => questionario != null ? "${questionario!.totalPerguntas}" : '0';

  @action
  incIndex() => _index++;

  @action
  setConteudo(String value) => _conteudo = value;

  @observable
  int? alternativa;

  @action
  setAlternativa(int? value) => alternativa = value;

  @action
  setAlternativaUnica(int? value) {
    alternativasSelecionadas.clear();
    setAlternativa(value);
    alternativasSelecionadas.add(value ?? 0);
  }

  @action
  setAlternativasMultiplas(int id) {
    if (alternativasSelecionadas.contains(id)) {
      alternativasSelecionadas.remove(id);
    } else {
      alternativasSelecionadas.add(id);
    }
  }

  @action
  void resetStateForNextQuestion() {
    setAlternativa(null);
    alternativasValueItemEspecial.clear();
    alternativasSelecionadas.clear();
  }

  @action
  setState(QuestionnaireState value) => state = value;

  @action
  startProgress(String message) {
    _progress = 0.0;
    _progressMessage = message;
  }

  @observable
  ObservableList<ValueItem> alternativasValueItemEspecial = ObservableList<ValueItem>();

  @action
  void setAlternativesMultiple(List<ValueItem> value) {
    alternativasSelecionadas.clear();
    for (var element in value) {
      alternativasSelecionadas.add(int.tryParse(element.value.toString()) ?? 0);
    }
  }

  bool get validate =>
      state == QuestionnaireState.guideLines ||
      (_conteudo != null && _conteudo!.trim().isNotEmpty) ||
      alternativasValueItemEspecial.isNotEmpty ||
      alternativasSelecionadas.isNotEmpty;

  @action
  Future<void> load(int? questionarioId) async {
    try {
      state = QuestionnaireState.loading;
      questionario = (await _repository.getQuestionario(questionarioId)).questionario;
      state = questionario == null
          ? QuestionnaireState.finishe
          : questionario!.bloqueado!
              ? QuestionnaireState.blocked
              : QuestionnaireState.guideLines;
    } finally {}
  }

  @action
  Future<void> gravarVideo() async {
    try {
      if (video != null) {
        var dir = Directory(video!.path);
        dir.deleteSync(recursive: true);
        video = null;
      }
      var result = await Modular.to.pushNamed(
        QuestionnaireVideoModule.route,
        arguments: pergunta!.tempo,
      );
      if (result != null) {
        idUpload = const Uuid().v4();
        video = result as File?;
      }
      // ignore: empty_catches
    } catch (e) {}
  }

  @action
  Future<void> selecionarArquivo({required int tipo}) async {
    final picker = ImagePickerLib();
    switch (tipo) {
      case 1:
        anexo = await picker.pickImage(source: ImageSource.camera);
        break;
      case 2:
        anexo = await picker.pickImage(source: ImageSource.gallery);
        break;
      case 3:
        anexo = await FilePickerLib.pickFile();
        break;
    }
    Modular.to.pop();
  }

  @action
  Future<void> enviarArquivo() async {
    try {
      List<File?> files;
      state = QuestionnaireState.progress;

      int fileSize = anexo!.lengthSync();
      String fileName = anexo!.path.split('/').last;

      if (fileSize > (2 * umMb)) {
        files = await splitFile(anexo!);
      } else {
        files = [anexo];
      }

      var metaData = PrepareMetadataModel(
        token: questionario!.token,
        idUpload: const Uuid().v4(),
        blocksCount: files.length,
        contentType: '',
        fileName: fileName,
        fileSize: fileSize,
        tipo: _tipo,
      );

      var responseMetaData = await _repository.postPrepareMetadata(metaData);

      startProgress('Enviando Arquivo');

      if (responseMetaData.sucesso) {
        for (int id = 0; id < files.length; id++) {
          await _repository.postUploadBlock(
              id: id + 1, idUpload: metaData.idUpload, file: files[id]!);
          _progress += 1;
        }
      }

      _progress = 100.0;

      await _repository.postPergunta(
        PerguntaSalvarModel(
          token: questionario!.token,
          id: pergunta!.id,
          totalPerguntas: questionario!.totalPerguntas,
          index: _index,
          tipo: _tipo,
          tempo: _tempo ?? 0,
          alternativas: alternativasSelecionadas,
          conteudo: responseMetaData.arquivo,
        ),
      );

      anexo = null;

      await getPergunta();
    } finally {
      state = _index <= questionario!.totalPerguntas!
          ? QuestionnaireState.responding
          : QuestionnaireState.finishe;
    }
  }

  @action
  Future<void> getPergunta({bool iniciou = false}) async {
    incIndex();
    pergunta =
        (await _repository.getPergunta(token: questionario!.token, index: _index, iniciou: iniciou))
            .pergunta;
  }

  @action
  Future<void> iniciar() async {
    try {
      state = QuestionnaireState.loading;
      _index = questionario!.totalRespondido ?? 0;
      await getPergunta(iniciou: true);
    } finally {
      state = _index <= questionario!.totalPerguntas!
          ? QuestionnaireState.responding
          : QuestionnaireState.finishe;
    }
  }

  @action
  Future<void> finalizar() async {
    try {
      state = QuestionnaireState.loading;
      if (questionario != null) {
        await _repository.postFinalizar(questionario!.token);
      }
      Modular.to.pop();
    } finally {
      state = questionario == null
          ? QuestionnaireState.finishe
          : _index < questionario!.totalPerguntas!
              ? QuestionnaireState.responding
              : QuestionnaireState.finishe;
    }
  }

  @action
  Future<void> responder({int? def}) async {
    try {
      state = QuestionnaireState.loading;

      await _repository.postPergunta(
        PerguntaSalvarModel(
          token: questionario!.token,
          id: pergunta!.id,
          totalPerguntas: questionario!.totalPerguntas,
          index: _index,
          tipo: _tipo,
          tempo: _tempo ?? 0,
          alternativas: alternativasSelecionadas,
          conteudo: _conteudo ?? '',
        ),
      );

      _conteudo = null;
      resetStateForNextQuestion();

      await getPergunta();
    } finally {
      state = _index <= questionario!.totalPerguntas!
          ? QuestionnaireState.responding
          : QuestionnaireState.finishe;
    }
  }

  @action
  Future<void> enviarVideo() async {
    try {
      List<File> files;
      state = QuestionnaireState.progress;

      comprimindo = true;

      startProgress('Comprimindo Vídeo');

      var compressedVideo = (await comprimir())!;

      comprimindo = false;

      if (compressedVideo.lengthSync() > (2 * umMb)) {
        files = await splitFile(compressedVideo);
      } else {
        files = [compressedVideo];
      }

      String fileName = compressedVideo.path.split('/').last;

      var metaData = PrepareMetadataModel(
          token: questionario!.token,
          idUpload: const Uuid().v4(),
          blocksCount: files.length,
          contentType: 'video/mp4',
          fileName: fileName,
          fileSize: compressedVideo.lengthSync(),
          tipo: _tipo);

      var responseMetaData = await _repository.postPrepareMetadata(metaData);

      startProgress('Enviando Vídeo');

      if (responseMetaData.sucesso) {
        for (int id = 0; id < files.length; id++) {
          await _repository.postUploadBlock(
              id: id + 1, idUpload: metaData.idUpload, file: files[id]);
          _progress += 1;
        }
      }

      _progress = 100.0;

      await _repository.postPergunta(
        PerguntaSalvarModel(
          token: questionario!.token,
          id: pergunta!.id,
          totalPerguntas: questionario!.totalPerguntas,
          index: _index,
          tipo: _tipo,
          tempo: _tempo ?? 0,
          alternativas: [],
          conteudo: responseMetaData.arquivo,
        ),
      );

      video = null;

      await getPergunta();
    } finally {
      state = _index <= questionario!.totalPerguntas!
          ? QuestionnaireState.responding
          : QuestionnaireState.finishe;
    }
  }

  Future<File?> comprimir() async {
    try {
      var originalVideInfo = await VideoCompress.getMediaInfo(video!.path);
      final compressedVideoInfo = (await VideoCompress.compressVideo(
        originalVideInfo.path!,
        quality: VideoQuality.MediumQuality,
        deleteOrigin: false,
      ))!;
      return compressedVideoInfo.file;
    } catch (e) {
      return video;
    }
  }
}
