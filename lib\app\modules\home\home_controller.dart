import 'package:flutter_modular/flutter_modular.dart';
import 'package:mobx/mobx.dart';

import '../confirm_subscription/confirm_subscription_controller.dart';
import '../jobs/job_detail/job_detail_controller.dart';

part 'home_controller.g.dart';

class HomeController = _HomeControllerBase with _$HomeController;

abstract class _HomeControllerBase with Store {
  final subscription = Modular.get<ConfirmSubscriptionController>();
  final controllerJob = Modular.get<JobDetailController>();

  @observable
  int? _tabSelected = 0;

  int? get tabSelected => _tabSelected;

  @observable
  int notifications = 0;

  @observable
  bool _loading = false;
  bool get loading => _loading;

  @observable
  bool _closeScreen = false;

  // @observable
  bool get closeScreen => _closeScreen;

  @action
  setCloseScreen(bool value) => _closeScreen = value;

  @action
  setLoading(bool value) => _loading = value;

  @action
  setTabSelected(int? value) => _tabSelected = value;
}
