import 'package:flutter_modular/flutter_modular.dart';
import 'package:get/get.dart';

import '../../shared/mixins/loader_mixin.dart';
import '../confirm_subscription/confirm_subscription_controller.dart';
import '../jobs/job_detail/job_detail_controller.dart';

class HomeController extends GetxController with LoaderMixin {
  final subscription = Modular.get<ConfirmSubscriptionController>();
  final controllerJob = Modular.get<JobDetailController>();

  int? _tabSelected = 0;
  int? get tabSelected => _tabSelected;

  int _notifications = 0;
  int get notifications => _notifications;

  bool _closeScreen = false;
  bool get closeScreen => _closeScreen;

  Future<void> setCloseScreen(bool value) async {
    _closeScreen = value;
    update();
  }

  void setTabSelected(int? value) {
    _tabSelected = value;
    update();
  }

  void setNotifications(int value) {
    _notifications = value;
    update();
  }

  // Setter para compatibilidade com código existente
  set notifications(int value) => setNotifications(value);
}
