class PorteEmpresaModel {
  final int? id;
  final String? descricao;

  PorteEmpresaModel({this.id, this.descricao});

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'descricao': descricao,
    };
  }

  @override
  String toString() => descricao!;

  @override
  operator ==(other) => other is PorteEmpresaModel && other.id == id;

  @override
  int get hashCode => id.hashCode ^ descricao.hashCode;
}
