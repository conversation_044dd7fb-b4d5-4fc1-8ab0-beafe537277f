class EsqueciSenhaResponseModel {
  final bool sucesso;
  final String mensagem;
  final String? email;

  EsqueciSenhaResponseModel({
    this.sucesso = false,
    this.email,
    this.mensagem = "",
  });

  factory EsqueciSenhaResponseModel.fromJson(Map<String, dynamic> json) {
    return EsqueciSenhaResponseModel(
      sucesso: json['sucesso'] ?? false,
      mensagem: json['mensagem'] ?? "",
      email: json['email'] as String?,
    );
  }
}
