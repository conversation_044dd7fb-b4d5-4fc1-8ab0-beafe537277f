<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.empregare.app">
    <!-- io.flutter.app.FlutterApplication is an android.app.Application that
         calls FlutterMain.startInitialization(this); in its onCreate method.
         In most cases you can leave this as-is, but you if you want to provide
         additional functionality it is fine to subclass or reimplement
         FlutterApplication and put your custom class here. -->
    
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

    <!-- Provide required visibility configuration for API level 30 and above -->
    <queries>
        <!-- If your app opens https URLs -->
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="https" />
        </intent>
        <!-- If your app makes calls -->
        <intent>
            <action android:name="android.intent.action.DIAL" />
            <data android:scheme="tel" />
        </intent>
        <!-- If your app emails -->
        <intent>
            <action android:name="android.intent.action.SEND" />
            <data android:mimeType="*/*" />
        </intent>
    </queries>

    <application
        android:label="Empregare"
        android:icon="@mipmap/ic_launcher">

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_notification" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/colorPrimary" />

        <activity
            android:name=".MainActivity"
            android:launchMode="singleTop"
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:exported="true"
            android:windowSoftInputMode="adjustResize">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
            <intent-filter>
                <action android:name="FLUTTER_NOTIFICATION_CLICK" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https" android:host="empregare.com" android:pathPattern="/.*/vagas" />
                <data android:scheme="https" android:host="www.empregare.com" android:pathPattern="/.*/vagas" />

                <data android:scheme="https" android:host="empregare.com" android:pathPattern="/.*/vaga-.*" />
                <data android:scheme="https" android:host="www.empregare.com" android:pathPattern="/.*/vaga-.*" />

                <data android:scheme="https" android:host="empregare.com" android:pathPattern="/.*/candidato/painel" />
                <data android:scheme="https" android:host="www.empregare.com" android:pathPattern="/.*/candidato/painel" />

                <data android:scheme="https" android:host="empregare.com" android:pathPattern="/.*/candidato/questionario/.*" />
                <data android:scheme="https" android:host="www.empregare.com" android:pathPattern="/.*/candidato/questionario/.*" />

                <data android:scheme="https" android:host="empregare.com" android:pathPattern="/.*/candidato/talentoday" />
                <data android:scheme="https" android:host="www.empregare.com" android:pathPattern="/.*/candidato/talentoday" />

                <data android:scheme="https" android:host="empregare.com" android:pathPattern="/.*/candidato/agenda-publica.*" />
                <data android:scheme="https" android:host="www.empregare.com" android:pathPattern="/.*/candidato/agenda-publica.*" />

                <data android:scheme="https" android:host="empregare.com" android:pathPattern="/.*/candidato/inscricao_.*" />
                <data android:scheme="https" android:host="www.empregare.com" android:pathPattern="/.*/candidato/inscricao_.*" />

                <data android:scheme="https" android:host="empregare.com" android:pathPattern="/.*/candidato/curriculo" />
                <data android:scheme="https" android:host="www.empregare.com" android:pathPattern="/.*/candidato/curriculo" />

                <data android:scheme="https" android:host="empregare.com" android:pathPattern="/.*/candidato/mensagens" />
                <data android:scheme="https" android:host="www.empregare.com" android:pathPattern="/.*/candidato/mensagens" />
            </intent-filter>
        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-6762183698372341~6546297405"/>
    </application>
</manifest>