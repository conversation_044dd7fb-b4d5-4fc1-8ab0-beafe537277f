import 'package:flutter/material.dart';

class AppContainer extends StatelessWidget {
  final Widget? child;
  final Color color;
  final EdgeInsetsGeometry padding;
  final double? width;
  final double? height;

  const AppContainer({
    super.key,
    this.child,
    this.color = Colors.white,
    this.padding = const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      padding: padding,
      decoration: BoxDecoration(
        color: color,
        boxShadow: const [
          BoxShadow(
            color: Colors.grey,
            offset: Offset(0.0, 0.0),
            blurRadius: 0.3,
            spreadRadius: 0.0,
          )
        ],
      ),
      child: child,
    );
  }
}
