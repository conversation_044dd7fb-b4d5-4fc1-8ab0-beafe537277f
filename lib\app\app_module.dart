import 'package:flutter_modular/flutter_modular.dart';

import 'app_controller.dart';
import 'modules/auth/change_password/change_password_module.dart';
import 'modules/auth/login/login_module.dart';
import 'modules/auth/signup/signup_module.dart';
import 'modules/chat/chat_module.dart';
import 'modules/confirm_subscription/confirm_subscription_module.dart';
import 'modules/curriculum/curriculum_editing/curriculum_editing_module.dart';
import 'modules/curriculum/curriculum_help/curriculum_help_module.dart';
import 'modules/curriculum/curriculum_module.dart';
import 'modules/forgot_password/forgot_password_module.dart';
import 'modules/forgot_password/utils/widget_access_recovery.dart';
import 'modules/forgot_password/utils/widget_reset_by_email.dart';
import 'modules/forgot_password/utils/widget_select_method_of_recovery.dart';
import 'modules/forgot_password/utils/widget_step_question.dart';
import 'modules/forgot_password/utils/widget_validation_failed.dart';
import 'modules/home/<USER>';
import 'modules/home/<USER>/utils/behavioral_profile.dart';
import 'modules/imagecrop/imagecrop_module.dart';
import 'modules/jobs/job_detail/job_detail_module.dart';
import 'modules/jobs/jobs_filter/jobs_filter_module.dart';
import 'modules/jobs/jobs_module.dart';
import 'modules/jobs/jobs_search/jobs_search_module.dart';
import 'modules/notification/notification_module.dart';
import 'modules/questionnaire/questionnaire_module.dart';
import 'modules/recommendations/recommendations_module.dart';
import 'modules/questionnaire/questionnaire_video/questionnaire_video_module.dart';
import 'modules/schedule/schedule_module.dart';
import 'modules/settings/language_and_timezone/language_and_timezone_module.dart';
import 'modules/settings/security_and_lgpd/security_and_lgpd_module.dart';
import 'modules/settings/settings_module.dart';
import 'modules/splash/splash_module.dart';
import 'modules/subscription/subscription_module.dart';
import 'modules/weblink/weblink_module.dart';
import 'modules/welcome/welcome_page.dart';
import 'shared/core/app_rest.dart';
import 'shared/core/app_rest_upload.dart';
import 'shared/repositories/academic_degree_repository.dart';
import 'shared/repositories/cities_repository.dart';
import 'shared/repositories/curriculum_repository.dart';
import 'shared/repositories/dashboard_repository.dart';
import 'shared/repositories/inscription_repository.dart';
import 'shared/repositories/job_repository.dart';
import 'shared/repositories/login_repository.dart';
import 'shared/repositories/message_repository.dart';
import 'shared/repositories/personality_test_repository.dart';
import 'shared/repositories/positions_repository.dart';
import 'shared/repositories/questionneire_repository.dart';
import 'shared/repositories/schedule_repository.dart';
import 'shared/repositories/settings_repository.dart';
import 'shared/services/session_service.dart';
import 'shared/services/storage_service.dart';

class AppModule extends Module {
  @override
  void binds(i) {
    i.addLazySingleton(AppController.new);
    i.addLazySingleton(StorageService.new);
    i.addLazySingleton(SessionService.new);
    i.addLazySingleton(AppRest.new);
    i.addLazySingleton(AppRestUpload.new);
    i.addLazySingleton(CurriculumRepository.new);
    i.addLazySingleton(LoginRepository.new);
    i.addLazySingleton(QuestionnaireRepository.new);
    i.addLazySingleton(JobRepository.new);
    i.addLazySingleton(CitiesRepository.new);
    i.addLazySingleton(InscriptionRepository.new);
    i.addLazySingleton(DashboardRepository.new);
    i.addLazySingleton(MessageRepository.new);
    i.addLazySingleton(ScheduleRepository.new);
    i.addLazySingleton(SettingsRepository.new);
    i.addLazySingleton(PositionsRepository.new);
    i.addLazySingleton(AcademicDegreeRepository.new);
    i.addLazySingleton(PersonalityTestRepository.new);
  }

  @override
  void routes(r) {
    r.add(ModuleRoute(Modular.initialRoute, module: SplashModule()));
    r.child(WelcomePage.route, child: (context) => const WelcomePage());

    r.add(ModuleRoute(LoginModule.routeName, module: LoginModule()));
    r.add(ModuleRoute(
      ForgotPasswordModule.route,
      module: ForgotPasswordModule(),
    ));
    r.child(WidgetResetByEmail.route,
        child: (context) => const WidgetResetByEmail());
    r.child(WidgetAccessRecovery.route,
        child: (context) => const WidgetAccessRecovery());
    r.child(WidgetStepQuestion.route, child: (context) => WidgetStepQuestion());
    r.child(WidgetValidationFailed.route,
        child: (context) => const WidgetValidationFailed());
    r.child(WidgetSelectMethodOfRecovery.route,
        child: (context) => const WidgetSelectMethodOfRecovery());
    r.child(BehavioralProfile.route,
        child: (context) => const BehavioralProfile());

    r.add(ModuleRoute(SignupModule.route, module: SignupModule()));
    r.add(ModuleRoute(HomeModule.route, module: HomeModule()));
    r.add(ModuleRoute(SettingsModule.route, module: SettingsModule()));
    r.add(ModuleRoute(JobDetailModule.route, module: JobDetailModule()));
    r.add(ModuleRoute(CurriculumModule.route, module: CurriculumModule()));
    r.add(ModuleRoute(JobsModule.route, module: JobsModule()));

    r.add(ModuleRoute(
      NotificationModule.route,
      module: NotificationModule(),
    ));

    r.add(ModuleRoute(
      ConfirmSubscriptionModule.route,
      module: ConfirmSubscriptionModule(),
    ));

    r.add(ModuleRoute(
      SubscriptionModule.route,
      module: SubscriptionModule(),
    ));
    r.add(ModuleRoute(JobsFilterModule.route, module: JobsFilterModule()));
    r.add(ModuleRoute(RecommendationsModule.route,
        module: RecommendationsModule()));
    r.add(ModuleRoute(ChatModule.route, module: ChatModule()));
    r.add(ModuleRoute(
      CurriculumHelpModule.route,
      module: CurriculumHelpModule(),
    ));

    r.add(ModuleRoute(ScheduleModule.route, module: ScheduleModule()));
    r.add(ModuleRoute(
      ChangePasswordModule.route,
      module: ChangePasswordModule(),
    ));

    r.add(ModuleRoute(
      QuestionnaireModule.route,
      module: QuestionnaireModule(),
    ));

    r.add(ModuleRoute(
      QuestionnaireVideoModule.route,
      module: QuestionnaireVideoModule(),
    ));

    r.add(ModuleRoute(WeblinkModule.route, module: WeblinkModule()));

    r.add(ModuleRoute(
      CurriculumEditingModule.route,
      module: CurriculumEditingModule(),
    ));

    r.add(ModuleRoute(ImagecropModule.route, module: ImagecropModule()));
    r.add(ModuleRoute(JobsSearchModule.route, module: JobsSearchModule()));

    r.add(
      ModuleRoute(
        SecurityAndLGPDModule.route,
        module: SecurityAndLGPDModule(),
      ),
    );

    r.add(
      ModuleRoute(
        LanguageAndTimezoneModule.route,
        module: LanguageAndTimezoneModule(),
      ),
    );
  }
}
