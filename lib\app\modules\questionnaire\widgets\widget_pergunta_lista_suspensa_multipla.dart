import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:multi_dropdown/multiselect_dropdown.dart';

import '../questionnaire_controller.dart';

class WidgetPerguntaListaSuspensaMultipla extends StatefulWidget {
  const WidgetPerguntaListaSuspensaMultipla({
    super.key,
    required this.onOptionSelected,
  });

  final Function(List<ValueItem>)? onOptionSelected;

  @override
  State<WidgetPerguntaListaSuspensaMultipla> createState() =>
      _WidgetPerguntaListaSuspensaMultiplaState();
}

class _WidgetPerguntaListaSuspensaMultiplaState
    extends State<WidgetPerguntaListaSuspensaMultipla> {
  final controller = Modular.get<QuestionnaireController>();

  @override
  Widget build(BuildContext context) {
    List<ValueItem> multiSelectItems =
        controller.pergunta!.alternativas!.map((item) {
      return ValueItem(
        label: HtmlParser.parseHTML(item.descricao.toString()).text,
        value: item.id.toString(),
      );
    }).toList();

    double tamanho = 200;

    if (multiSelectItems.length < 4) {
      tamanho = multiSelectItems.length * 48.05;
    }

    return SingleChildScrollView(
      child: Container(
        alignment: Alignment.center,
        padding: const EdgeInsets.all(5),
        child: Column(
          children: [
            MultiSelectDropDown(
              hint: 'ESCOLHA AS ALTERNATIVAS: ',
              options: multiSelectItems,
              onOptionSelected: widget.onOptionSelected,
              controller: controller.multiSelectController,
              borderWidth: 1,
              dropdownHeight: tamanho,
            ),
          ],
        ),
      ),
    );
  }
}
