import 'package:flutter/material.dart';

class AppButtonDefault extends StatelessWidget {
  final VoidCallback? onTap;
  final String text;
  final bool isLoading;
  final bool usingJustPadding;
  final double paddingVertical;
  final double? radius;
  final double width;
  final bool? isValid;
  final Color? buttonColor;
  final Color? textColor;

  const AppButtonDefault({
    super.key,
    required this.onTap,
    required this.text,
    this.isLoading = false,
    this.usingJustPadding = false,
    this.width = 200,
    this.paddingVertical = 8,
    this.radius,
    this.isValid,
    this.buttonColor,
    this.textColor = Colors.white,
  });

  @override
  Widget build(BuildContext context) {
    bool isValidButton = isValid ?? false;

    return GestureDetector(
      onTap: isLoading || !isValidButton ? null : onTap,
      child: Container(
        width: usingJustPadding ? null : width,
        padding: EdgeInsets.symmetric(
          horizontal: usingJustPadding ? 10 : 0,
          vertical: paddingVertical,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(radius ?? 8),
          color: isValidButton ? buttonColor : Colors.grey,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (isLoading)
              SizedBox(
                height: 30,
                width: 30,
                child: CircularProgressIndicator.adaptive(
                  strokeWidth: 3.5,
                  backgroundColor: Colors.white.withValues(alpha: 0.5),
                  valueColor: const AlwaysStoppedAnimation(Colors.white),
                ),
              ),
            if (!isLoading)
              Text(
                text,
                style: TextStyle(
                  fontFamily: 'Roboto',
                  color: isValidButton ? textColor : Colors.grey[400],
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
          ],
        ),
      ),
    );
  }
}
