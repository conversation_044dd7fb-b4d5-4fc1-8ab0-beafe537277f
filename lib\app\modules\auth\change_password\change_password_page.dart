import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

import '../../../shared/core/app_config.dart';
import '../../../shared/core/app_translation.i18n.dart';
import '../../../shared/models/responses/simple_response_model.dart';
import '../../../shared/repositories/settings_repository.dart';
import '../../../shared/widgets/app_default_button.dart';
import '../../../shared/widgets/app_text_form_field.dart';

class ChangePasswordPage extends StatefulWidget {
  const ChangePasswordPage({
    super.key,
  });

  @override
  State<ChangePasswordPage> createState() => _ChangePasswordPageState();
}

class _ChangePasswordPageState extends State<ChangePasswordPage> {
  final _repository = Modular.get<SettingsRepository>();

  final _formKey = GlobalKey<FormState>();
  final _senhaNovaFocusNode = FocusNode();

  bool loading = false;
  void changeLoading(bool value) {
    loading = value;
    setState(() {});
  }

  String? senhaAtual;
  void setSenhaAtual(String value) => senhaAtual = value;

  String? senhaNova;
  void setSenhaNova(String value) => senhaNova = value;

  Future<SimpleResponseModel> alterarSenha() async {
    try {
      changeLoading(true);
      var response = await _repository.postAlterarSenha(
        senhaAtual: senhaAtual,
        senhaNova: senhaNova,
      );

      return response;
    } finally {
      changeLoading(false);
    }
  }

  @override
  void dispose() {
    _formKey.currentState?.dispose();
    _senhaNovaFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConfig.white,
      appBar: AppBar(
        backgroundColor: AppConfig.white,
        elevation: 0.0,
        title: Text(
          'Alterar Senha'.i18n,
          style: const TextStyle(
            color: Colors.black,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(15),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Text(
                'Senha Atual',
                style: TextStyle(
                  fontSize: 14,
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w400,
                  color: Color(0xff161519),
                ),
              ),
              const SizedBox(height: 10),
              AppTextFormField(
                isPassword: true,
                textInputAction: TextInputAction.next,
                hintText: 'Digite sua senha atual'.i18n,
                onChanged: setSenhaAtual,
                validator: (value) {
                  if (value!.isEmpty) return 'Campo obrigatório'.i18n;
                  return null;
                },
                onFieldSubmitted: (_) => _senhaNovaFocusNode.nextFocus(),
              ),
              const SizedBox(height: 20),
              const Text(
                'Nova senha',
                style: TextStyle(
                  fontSize: 14,
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w400,
                  color: Color(0xff161519),
                ),
              ),
              const SizedBox(height: 10),
              AppTextFormField(
                isPassword: true,
                textInputAction: TextInputAction.next,
                hintText: 'Digite uma nova senha'.i18n,
                onChanged: setSenhaNova,
                validator: (value) {
                  if (value!.isEmpty) return 'Campo obrigatório'.i18n;
                  return null;
                },
              ),
              const SizedBox(height: 20),
              AppDefaultButton(
                title: loading
                    ? const SpinKitCircle(color: AppConfig.white)
                    : Text(
                        'Alterar senha'.i18n,
                        style: const TextStyle(
                          color: AppConfig.white,
                        ),
                      ),
                onPressed: () async {
                  if (!_formKey.currentState!.validate()) return;

                  var response = await alterarSenha();

                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      backgroundColor: response.sucesso ? AppConfig.green : AppConfig.red,
                      content: Text(response.mensagem.i18n),
                    ),
                  );

                  if (response.sucesso) {
                    Modular.to.pop();
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
