class IdiomaResponseModel {
  final bool sucesso;
  final String mensagem;
  final List<IdiomaModel>? dados;

  IdiomaResponseModel({this.sucesso = false, this.mensagem = "", this.dados});

  factory IdiomaResponseModel.fromJson(Map<String, dynamic> json) =>
      IdiomaResponseModel(
        sucesso: json['sucesso'] ?? false,
        mensagem: json['mensagem'] ?? "",
        dados: (json['dados'] as List<dynamic>?)
            ?.map((e) => IdiomaModel.fromJson(e as Map<String, dynamic>))
            .toList(),
      );
}

class IdiomaModel {
  final int? id;
  int? idiomaID;
  String? descricao;
  int? nivel;
  String? nivelString;
  String? nome;

  IdiomaModel({
    this.id,
    this.idiomaID,
    this.descricao,
    this.nivel,
    this.nivelString,
    this.nome,
  });

  factory IdiomaModel.fromJson(Map<String, dynamic> json) => IdiomaModel(
        id: json['id'] as int?,
        idiomaID: json['idiomaID'] as int?,
        descricao: json['descricao'] as String?,
        nivel: json['nivel'] as int?,
        nivelString: json['nivelString'] as String?,
        nome: json['nome'] as String?,
      );

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'idiomaID': idiomaID,
        'descricao': descricao,
        'nivel': nivel,
        'nivelString': nivelString,
        'nome': nome,
      };

  IdiomaModel copy() {
    return IdiomaModel(
      id: id,
      idiomaID: idiomaID,
      descricao: descricao,
      nivel: nivel,
      nivelString: nivelString,
      nome: nome,
    );
  }
}
