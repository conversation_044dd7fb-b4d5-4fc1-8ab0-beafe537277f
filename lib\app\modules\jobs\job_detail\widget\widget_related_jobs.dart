import 'package:bottom_sheet/bottom_sheet.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

import '../job_detail_controller.dart';
import 'app_body_builder_job_detaisl.dart';
import 'app_header_builder_job_details.dart';

class WidgetRelatedJobs extends StatefulWidget {
  final VoidCallback onUpdate;
  const WidgetRelatedJobs({super.key, required this.onUpdate});

  @override
  State<WidgetRelatedJobs> createState() => _WidgetRelatedJobsState();
}

class _WidgetRelatedJobsState extends State<WidgetRelatedJobs> {
  @override
  Widget build(BuildContext context) {
    final controller = Modular.get<JobDetailController>();
    final jobRelacionado = controller.jobDetails?.vagasRelacionadas;

    if (jobRelacionado == null || jobRelacionado.isEmpty) {
      return const SizedBox.shrink();
    }

    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      scrollDirection: Axis.vertical,
      itemCount: jobRelacionado.length,
      itemBuilder: (BuildContext context, int i) {
        return Column(
          children: [
            ListTile(
              onTap: () async {
                final item = jobRelacionado[i];
                final idVagaS = item.url?.replaceAll(RegExp(r'\D'), '') ?? '';
                final idVagaI = int.tryParse(idVagaS);

                if (idVagaI != null) {
                  await showStickyFlexibleBottomSheet(
                    minHeight: .82,
                    initHeight: .82,
                    maxHeight: .92,
                    maxHeaderHeight: 118,
                    minHeaderHeight: 100,
                    isSafeArea: true,
                    bottomSheetBorderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(25),
                      topRight: Radius.circular(25),
                    ),
                    anchors: [0.8],
                    decoration: const BoxDecoration(
                      color: Colors.white,
                    ),
                    context: context,
                    isDismissible: true,
                    duration: const Duration(milliseconds: 500),
                    headerBuilder: (BuildContext context, double offset) {
                      return AppHeaderBuildeJobDetails(
                        title: item.titulo ?? "",
                        image: item.logo ?? "",
                        company: item.empresa ?? "",
                        date: "",
                        offset: offset,
                      );
                    },
                    bodyBuilder: (context, bottomSheetOffset) {
                      return SliverChildListDelegate(
                        [
                          Padding(
                            padding: const EdgeInsets.only(left: 24, right: 24),
                            child: AppBodyBuilderJobDetails(jobId: idVagaI),
                          )
                        ],
                      );
                    },
                  );

                  controller.removeJob(idVagaI);
                }
              },
              isThreeLine: true,
              style: ListTileStyle.drawer,
              leading: SizedBox(
                width: 60,
                child: CachedNetworkImage(
                  imageUrl: jobRelacionado[i].logo ?? '',
                  imageBuilder: (context, imageProvider) => Container(
                    decoration: BoxDecoration(
                      image: DecorationImage(
                          image: imageProvider,
                          fit: BoxFit.fitWidth,
                          alignment: Alignment.center),
                    ),
                  ),
                  placeholder: (context, url) => SpinKitCircle(
                    color: Colors.blue[300],
                  ),
                  errorWidget: (context, url, error) => const Icon(
                    Icons.broken_image,
                    size: 60,
                  ),
                ),
              ),
              title: Padding(
                padding: const EdgeInsets.only(left: 5),
                child: Text(
                  jobRelacionado[i].titulo!,
                  style: const TextStyle(
                    fontSize: 16,
                  ),
                ),
              ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  const SizedBox(
                    height: 5,
                  ),
                  Padding(
                    padding: const EdgeInsets.only(left: 5),
                    child: Text(
                      jobRelacionado[i].empresa ?? "",
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.black.withValues(alpha: 0.7),
                      ),
                    ),
                  ),
                  const SizedBox(
                    height: 5,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: <Widget>[
                      const Icon(
                        Icons.location_on,
                        color: Colors.grey,
                        size: 18,
                      ),
                      Expanded(
                        child: Text(
                          jobRelacionado[i].cidades ?? '',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  )
                ],
              ),
            ),
            const Divider()
          ],
        );
      },
    );
  }
}
