import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';

import '../../../../../shared/core/app_utils.dart';
import 'benefits_dialog.dart';

class BenefitsDetails extends StatelessWidget {
  const BenefitsDetails({
    super.key,
    required this.widget,
  });

  final BenefitsDialog widget;

  @override
  Widget build(BuildContext context) {
    final promotions = widget.modelPromo?.dados ?? [];

    if (promotions.isEmpty) {
      return const Text(
        'Não há promoções.',
        style: TextStyle(fontSize: 16),
      );
    }

    return Column(
      children: promotions.map((promotion) {
        return ExpansionTile(
          title: Text(promotion.titulo ?? 'Promoção'),
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Html(
                data: removeAllHtmlTags(promotion.descricao ?? ''),
              ),
            ),
          ],
        );
      }).toList(),
    );
  }
}
