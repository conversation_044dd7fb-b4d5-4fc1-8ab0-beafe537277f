import 'package:dio/dio.dart';
import 'package:empregare_app/app/shared/mixins/loader_mixin.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:get/get.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../../shared/models/responses/listar_diversidade_response_model.dart';
import '../../../shared/models/responses/simple_response_model.dart';
import '../../../shared/models/salvar_diversidade_model.dart';
import '../../../shared/repositories/curriculum_repository.dart';

class DiversitiesController extends GetxController with LoaderMixin {
  final CurriculumRepository _repository = Modular.get();

  Future load() async {
    try {
      changeLoading(true);
      await Future.wait([
        loadDiversidade(),
      ]);
    } finally {
      changeLoading(false);
    }
  }

  final pronomeDiversidades = [
    DiversidadeModel(pronome: 1, pronomeTexto: 'Ele'),
    DiversidadeModel(pronome: 2, pronomeTexto: 'Ela'),
    DiversidadeModel(pronome: 3, pronomeTexto: 'Outro'),
  ];

  final identidadeDiversidades = [
    DiversidadeModel(
        identidadeGenero: 1, identidadeGeneroTexto: 'Cisgênero'.i18n),
    DiversidadeModel(
        identidadeGenero: 2, identidadeGeneroTexto: 'Transgênero'.i18n),
    DiversidadeModel(
      identidadeGenero: 3,
      identidadeGeneroTexto: 'Prefiro não responder'.i18n,
    ),
  ];

  final orientacaoDiversidades = [
    DiversidadeModel(
        orientacaoSexual: 1, orientacaoSexualTexto: 'Heterossexual'.i18n),
    DiversidadeModel(
        orientacaoSexual: 2, orientacaoSexualTexto: 'Homossexual'.i18n),
    DiversidadeModel(
        orientacaoSexual: 3, orientacaoSexualTexto: 'Bissexual'.i18n),
    DiversidadeModel(
        orientacaoSexual: 4, orientacaoSexualTexto: 'Assexual'.i18n),
    DiversidadeModel(
        orientacaoSexual: 5, orientacaoSexualTexto: 'Pansexual'.i18n),
    DiversidadeModel(
        orientacaoSexual: 6,
        orientacaoSexualTexto: 'Prefiro não responder'.i18n),
  ];

  void updateOrientacao(int value) {
    diversidadeToSave = diversidadeToSave?.copyWith(orientacaoSexual: value);
    update();
  }

  final racaDiversidades = [
    DiversidadeModel(racaCor: 1, racaCorTexto: 'Branco(a)'.i18n),
    DiversidadeModel(racaCor: 2, racaCorTexto: 'Preto(a)'.i18n),
    DiversidadeModel(racaCor: 3, racaCorTexto: 'Pardo(a)'.i18n),
    DiversidadeModel(racaCor: 4, racaCorTexto: 'Amarelo(a)'.i18n),
    DiversidadeModel(racaCor: 5, racaCorTexto: 'Indígena'.i18n),
    DiversidadeModel(racaCor: 6, racaCorTexto: 'Prefiro não responder'.i18n),
  ];

  SalvarDiversidadeModel? diversidadeToSave;

  void updatePronome(int value) {
    diversidadeToSave = diversidadeToSave?.copyWith(pronome: value);
    update();
  }

  void updateIdentidade(int value) {
    diversidadeToSave = diversidadeToSave?.copyWith(identidadeGenero: value);
    update();
  }

  Future<SimpleResponseModel> saveDiversidade() async {
    SimpleResponseModel response;
    try {
      changeLoading(true);
      response = (await _repository.saveDiversidade(diversidadeToSave!));
    } on DioException catch (err) {
      throw err.message ?? '';
    } finally {
      changeLoading(false);
    }
    return response;
  }

  DiversidadeModel? diversidade;

  void updateRaca(int value) {
    diversidadeToSave = diversidadeToSave?.copyWith(racaCor: value);
    update();
  }

  Future loadDiversidade() async {
    final response = await _repository.getDiversidade();
    if (response.diversidade != null) {
      diversidade = response.diversidade;
    }
    update();
  }

  String? toolTip(String? pronomeTexto) {
    if (pronomeTexto == 'Cisgênero') {
      return diversidade?.cisGeneroTooltip;
    }
    if (pronomeTexto == 'Transgênero') {
      return diversidade?.transGeneroTooltip;
    }
    if (pronomeTexto == 'Heterossexual') {
      return diversidade?.heterossexualTooltip;
    }
    if (pronomeTexto == 'Homossexual') {
      return diversidade?.homossexualTooltip;
    }
    if (pronomeTexto == 'Bissexual') {
      return diversidade?.bissexualTooltip;
    }
    if (pronomeTexto == 'Pansexual') {
      return diversidade?.pansexualTooltip;
    }
    if (pronomeTexto == 'Assexual') {
      return diversidade?.assexualTooltip;
    }

    return null;
  }
}
