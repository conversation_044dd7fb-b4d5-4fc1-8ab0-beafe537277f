import 'package:flutter_modular/flutter_modular.dart';
import 'package:mobx/mobx.dart';

import '../../../shared/models/job_busca.dart';
import '../../../shared/models/job_parameter_model.dart';
import '../../../shared/models/responses/inscricoes_response_model.dart';
import '../../../shared/models/responses/job_sugestoes_response_model.dart';
import '../../../shared/repositories/job_repository.dart';
import '../../jobs/jobs_filter/jobs_filter_module.dart';

part 'tab_jobs_controller.g.dart';

enum TabJobsState {
  idle,
  loadingPaises,
  loadingEstados,
  loadingCidades,
}

class TabJobsController = _TabJobsControllerBase with _$TabJobsController;

abstract class _TabJobsControllerBase with Store {
  final JobRepository _repository = Modular.get();

  @observable
  bool loading = true;

  int? _totalPaginas;
  int? get totalPaginas => _totalPaginas;

  @observable
  List<JobModel>? _jobs = <JobModel>[].asObservable();
  List<JobModel> get jobs => _jobs ?? [];

  @observable
  JobParameterModel _filtros = JobParameterModel();
  JobParameterModel get filtros => _filtros;

  @observable
  JobBuscaModel _busca = JobBuscaModel();
  JobBuscaModel get busca => _busca;

  @observable
  FacetsModel _facets = FacetsModel.empty();
  FacetsModel get facets => _facets;

  String get titulo {
    if (!_busca.isValid()) return 'Buscar...';

    return "${_busca.cargoOuEmpresa != null && _busca.cargoOuEmpresa!.isNotEmpty ? _busca.cargoOuEmpresa : ''} ${_busca.cidadeUfOuPais != null && _busca.cidadeUfOuPais!.isNotEmpty && _busca.cargoOuEmpresa != null && _busca.cargoOuEmpresa!.isNotEmpty ? '\\ ' : ''} ${_busca.cidadeUfOuPais != null && _busca.cidadeUfOuPais!.isNotEmpty ? _busca.cidadeUfOuPais : ''}";
  }

  @action
  setFiltros(JobParameterModel value) => _filtros = value;

  @action
  clearFiltros() {
    _filtros = JobParameterModel();
    _busca = JobBuscaModel();
    _facets = FacetsModel.empty();
    reload();
  }

  List<String> get paises => _filtros.pais ?? <String>[];
  List<String> get cidades => _filtros.cidade ?? <String>[];
  List<String> get estados => _filtros.uf ?? <String>[];
  List<String> get niveis => _filtros.nivel ?? <String>[];
  List<String> get regimes => _filtros.regime ?? [];

  bool? get isAsCegas => _filtros.selecaoCega;
  bool? get isPcd => _filtros.pcd;

  @action
  Future<void> load({bool reload = false}) async {
    try {
      loading = true;

      var data = await _repository.getJobs(filtros: filtros);

      if (reload) {
        _jobs = data.dados;
        _totalPaginas = data.totalPaginas?.floor();
      } else if (data.dados?.isNotEmpty ?? false) {
        jobs.addAll(data.dados ?? []);
        _totalPaginas = data.totalPaginas?.floor();
      }

      if (_filtros.carregarFiltro == true) {
        _facets = data.facets ?? FacetsModel.empty();
      }
    } finally {
      loading = false;
    }
  }

  @action
  Future<void> reload() async {
    _jobs = <JobModel>[].asObservable();
    _filtros.pag = 1;
    return load(reload: true);
  }

  @action
  Future<void> filter() async {
    var result = await Modular.to.pushNamed(
      JobsFilterModule.route,
      arguments: _facets,
    );

    if (result != null) {
      _filtros = (result as JobParameterModel);
      _filtros.carregarFiltro = true;

      await reload();
    }
  }
}
