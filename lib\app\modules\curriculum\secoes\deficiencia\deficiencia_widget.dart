import 'package:flutter/material.dart';
// import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:get/get.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../../../shared/core/app_config.dart';
import '../../../../shared/helpers/snack_bar_helper.dart';
import '../../../../shared/models/responses/listar_deficiencias_response_model.dart';
import '../../../../shared/models/responses/pessoa_response_model.dart';
import '../../controllers/deficiencies_controller.dart';
import '../../curriculum_controller.dart';
import '../../curriculum_editing/curriculum_editing_controller.dart';
import '../../curriculum_editing/curriculum_editing_module.dart';
import '../../widgets/curriculum_default_field.dart';
import '../../widgets/dialog_confirm_delete.dart';
import '../secao/secao_widget.dart';
import 'widgets/deficiencia_display.dart';

class DeficienciaWidget extends StatefulWidget {
  const DeficienciaWidget({super.key});

  @override
  _DeficienciaWidgetState createState() => _DeficienciaWidgetState();
}

class _DeficienciaWidgetState extends State<DeficienciaWidget> {
  final DeficienciesController controller = Modular.get();
  final controllerEditing = Modular.get<CurriculumEditingController>();

  PessoaResponseModel? get pessoa => Modular.get<CurriculumController>().pessoa;

  List<Map<String, String?>> joinDeficiencias() {
    List<Map<String, String?>> result = [];

    String? categoria = '';
    String deficiencias = '';

    controller.deficiencias?.sort((a, b) => a.tipo!.compareTo(b.tipo!));
    controller.deficiencias?.forEach((d) {
      if (categoria != '' && categoria != d.categoria) {
        result.add({
          'categoria': categoria,
          'deficiencias': deficiencias,
        });
        categoria = d.categoria;
        deficiencias = '';
      }
      categoria = d.categoria;
      deficiencias += deficiencias == '' ? d.nome! : (', ${d.nome!}');
    });

    result.add({
      'categoria': categoria,
      'deficiencias': deficiencias,
    });

    if (Modular.get<CurriculumController>().pessoaSeguro.deficienciaTexto !=
        null) {
      result.add({
        'categoria': 'Informações Adicionais',
        'deficiencias':
            Modular.get<CurriculumController>().pessoaSeguro.deficienciaTexto
      });
    }

    return result;
  }

  void changeSelectedOption(String type) async {
    controller.setLoadingDeficiencia(true);
    if (controllerEditing.selectedOption == type) {
      controllerEditing.selectedOption = null;
    } else {
      controllerEditing.selectedOption = type;
    }

    controllerEditing.naoPossuoDeficiencia =
        controllerEditing.selectedOption == "naoPossuo";

    if (controllerEditing.naoPossuoDeficiencia) {
      controller.deficienciasToSave.clear();
      // ignore: unused_local_variable
      var response = await controller.saveDeficiencias(
        remover: true,
      );
    }

    controller.setLoadingDeficiencia(false);

    setState(() {});
  }

  void onAddOrEdit() async {
    var result = await Modular.to.pushNamed(
      CurriculumEditingModule.route,
      arguments: CurriculumEditingModule.deficiencias,
    );

    if (!mounted) return;

    if (result == true) {
      SnackbarHelper.showSnackbarSucesso(
        context,
        'Deficiências salvas com sucesso',
      );
      Modular.get<CurriculumController>().loadDadosPessoais();
      controller.loadDeficiencias();
    }
  }

  void onRemoveLaudo(DeficienciaLaudoModel toRemove) async {
    bool? confirmDelete = await showDialog(
      context: context,
      builder: (_) => const ConfirmDeleteDialog(secao: 'Laudo'),
    );

    if (confirmDelete == true) {
      var result = await controller.deleteLaudo(toRemove.id);

      if (!mounted) return;

      if (result == true) {
        SnackbarHelper.showSnackbarSucesso(
          context,
          'Laudo removido com sucesso',
        );
        Modular.get<CurriculumController>().loadDadosPessoais();
        controller.loadDeficiencias();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CurriculumController>(
      builder: (_) => pessoa != null
          ? SecaoWidget(
              header: 'Deficiência'.i18n,
              hasContent: controller.deficiencias!.isNotEmpty,
              replaceEditWithAdd: true,
              onEdit: onAddOrEdit,
              showEdit: Modular.get<CurriculumController>()
                          .pessoaSeguro
                          .semDeficiencia ==
                      true
                  ? true
                  : null,
              content: ColoredBox(
                color: Colors.white,
                child: SizedBox(
                  width: MediaQuery.of(context).size.width,
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      const SizedBox(
                        height: 15,
                      ),
                      DeficienciaDisplay(
                        controller: controller,
                        onRemoveLaudo: onRemoveLaudo,
                      ),
                      const SizedBox(
                        height: 15,
                      ),
                      if (pessoa?.semDeficiencia != true &&
                          // ignore: prefer_is_empty
                          controller.deficiencias!.length < 0)
                        ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppConfig.colorPrimary,
                            padding: const EdgeInsets.symmetric(
                              vertical: 10,
                            ),
                            elevation: 0,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(6),
                              side: const BorderSide(
                                color: AppConfig.colorPrimary,
                                width: 1.8,
                              ),
                            ),
                          ),
                          onPressed: () async {
                            var result = await Modular.to.pushNamed(
                              CurriculumEditingModule.route,
                              arguments: CurriculumEditingModule.deficiencias,
                            );
                            if (result == true) {
                              SnackbarHelper.showSnackbarSucesso(
                                context,
                                'Deficiências salvas com sucesso',
                              );
                              Modular.get<CurriculumController>()
                                  .loadDadosPessoais();
                              controller.loadDeficiencias();
                            }
                          },
                          child: Row(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(
                                Icons.add,
                                color: Colors.white,
                              ),
                              Text(
                                'Adicionar deficiência'.i18n,
                                textAlign: TextAlign.center,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontFamily: 'Inter',
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      const SizedBox(
                        height: 7.5,
                      ),
                      if (Modular.get<CurriculumController>()
                                  .pessoa
                                  ?.semDeficiencia ==
                              false &&
                          // ignore: prefer_is_empty
                          controller.deficiencias!.length < 0)
                        ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppConfig.white,
                            padding: const EdgeInsets.symmetric(
                              vertical: 12.5,
                            ),
                            elevation: 0,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(6),
                              side: const BorderSide(
                                color: AppConfig.colorPrimary,
                                width: 1.8,
                              ),
                            ),
                          ),
                          onPressed: () async {
                            changeSelectedOption('naoPossuo');
                          },
                          child: Row(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              GetBuilder(
                                init: controller,
                                builder: (_) => Visibility(
                                  visible:
                                      controller.loadingDeficiencia == false,
                                  replacement: const SizedBox(
                                    height: 18,
                                    width: 18,
                                    child: CircularProgressIndicator(
                                      color: AppConfig.colorPrimary,
                                      backgroundColor: AppConfig.grey,
                                    ),
                                  ),
                                  child: Text(
                                    'Não possuo deficiência'.i18n,
                                    textAlign: TextAlign.center,
                                    style: const TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ),
              noContent: Modular.get<CurriculumController>()
                          .pessoaSeguro
                          .semDeficiencia ==
                      true
                  ? const ColoredBox(
                      color: Colors.white,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          Padding(
                            padding: EdgeInsets.symmetric(vertical: 20),
                            child: Text(
                              'Sem deficiência',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    )
                  : CurriculumDefaultField(
                      bottomAdd: () => onAddOrEdit(),
                      noContentButtom: () async {
                        changeSelectedOption('naoPossuo');
                      },
                      noContent: true,
                      textBottom: 'Adicionar deficiência',
                      noContentText: 'Não possuo',
                      description:
                          'Inclua para seu currículo ser classificado como PCD.',
                    ),
            )
          : Container(),
    );
  }
}
