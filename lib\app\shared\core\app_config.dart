import 'dart:io';

import 'package:flutter/material.dart';

abstract class AppConfig {
  static const production = true;
  static const appName = "Empregare";
  static const brasilId = "BR";

  static const colorPrimary = Color(0xFF1B1B63);
  static const colorBackground = Color(0xFF58595B);
  static const colorIcon = Color(0xFF58595B);
  static const white = Color(0xFFFAFAFA);
  static const grey = Color(0xFFEEEEEE);
  static const red = Color(0xFFD9534F);
  static const green = Color(0xFF3FB861);

  static const sentryDns =
      'https://<EMAIL>/4509113387384832';
  static const sampleRate = 0.1;
  static const termsOfUseUrl =
      "https://www.empregare.com/pagina/termos-de-uso?app=1";
  static const privacyPolicyUrl =
      "https://www.empregare.com/pagina/privacidade?app=1";
  static const helpUrl = "https://ajuda.empregare.com/support/home";
  //"https://empregare.zendesk.com/hc/pt-br/categories/202549467-Ajuda-para-Candidatos";

  static const siteUrl = "https://www.empregare.com/";

  static const apiUrl = production
      ? "https://www.empregare.com/api/pt-br"
      : "https://www.empregare.com/api/pt-br";

  static const connectionTimeout = 60000;

  static const admobAppIdAndroid = "ca-app-pub-6762183698372341~6546297405";
  static const admobAppIdIos = "ca-app-pub-6762183698372341~4383168493";

  static const kAppIos = "1511085791";

  static String get getAdmobAppId =>
      Platform.isIOS ? admobAppIdIos : admobAppIdAndroid;

  static const admobListagemVagasAdIdAndroid =
      "ca-app-pub-6762183698372341/8596432593";
  static const admobListagemVagasAdIdIos =
      "ca-app-pub-6762183698372341/6433303684";

  static String getAdmobBannerListagemVagasId() => Platform.isIOS
      ? admobListagemVagasAdIdIos
      : admobListagemVagasAdIdAndroid;

  static const admobDetalhesVagasAdIdAndroid =
      "ca-app-pub-6762183698372341/8596432593";
  static const admobDetalhesVagasAdIdIos =
      "ca-app-pub-6762183698372341/6433303684";

  static String getAdmobBannerDetalhesVagasId() => Platform.isIOS
      ? admobDetalhesVagasAdIdIos
      : admobDetalhesVagasAdIdAndroid;
}
