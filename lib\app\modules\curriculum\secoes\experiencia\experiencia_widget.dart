import 'package:get/get.dart';

import '../../controllers/experience_controller.dart';
import 'package:flutter/material.dart';

import 'package:flutter_modular/flutter_modular.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../../../shared/helpers/snack_bar_helper.dart';
import '../../../../shared/models/responses/listar_experiencias_response_model.dart';
import '../../../../shared/models/salvar_experiencia_model.dart';
import '../../curriculum_controller.dart';
import '../../curriculum_editing/curriculum_editing_module.dart';
import '../../widgets/add_button.dart';
import '../../widgets/curriculum_default_field.dart';
import '../../widgets/dialog_confirm_delete.dart';
import '../secao/secao_widget.dart';

class ExperienciaWidget extends StatefulWidget {
  const ExperienciaWidget({super.key});

  @override
  _ExperienciaWidgetState createState() => _ExperienciaWidgetState();
}

class _ExperienciaWidgetState extends State<ExperienciaWidget> {
  final ExperienceController controller = Modular.get();

  Future<void> onAddOrEdit(ExperienciaModel? toSave) async {
    if (toSave != null) {
      controller.experienciaToSave = SalvarExperienciaModel(
        id: toSave.id,
        cargo: toSave.cargo,
        empresa: toSave.empresa,
        cidade: toSave.cidade,
        porte: toSave.porteID,
        mesI: toSave.mesInicio,
        mesF: toSave.mesFim,
        anoI: toSave.anoInicio,
        anoF: toSave.anoFim,
        atual: toSave.atual,
        periodoPorExtenso: toSave.periodoPorExtenso,
        voluntario: toSave.voluntario,
        descricao: toSave.descricao,
        resultado: toSave.resultado,
        salario: toSave.salario,
      );
    }

    var result = await Modular.to.pushNamed(
      CurriculumEditingModule.route,
      arguments: CurriculumEditingModule.experiencias,
    );
    if (result == true) {
      SnackbarHelper.showSnackbarSucesso(
        context,
        'Experiência salva com sucesso',
      );
      Modular.get<CurriculumController>().loadDadosPessoais();
      controller.loadExperiencias();
    }
  }

  Future<void> noContent() async {
    await controller.saveExperiencia(remover: true);
    Modular.get<CurriculumController>().loadDadosPessoais();
    controller.loadExperiencias();
  }

  void onRemove(ExperienciaModel toRemove) async {
    bool? confirmDelete = await showDialog(
      context: context,
      builder: (_) => const ConfirmDeleteDialog(secao: 'Experiência'),
    );

    if (confirmDelete == true) {
      var result = await controller.deleteExperiencia(toRemove.id);
      if (result.sucesso) {
        SnackbarHelper.showSnackbarSucesso(context, result.mensagem);
        Modular.get<CurriculumController>().loadDadosPessoais();
        controller.loadExperiencias();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ExperienceController>(
      init: controller,
      builder: (_) => Modular.get<CurriculumController>().pessoa != null
          ? SecaoWidget(
              header: 'Experiência'.i18n,
              replaceEditWithAdd: Modular.get<CurriculumController>()
                          .pessoaSeguro
                          .semExperiencia ==
                      true
                  ? true
                  : false,
              onEdit: Modular.get<CurriculumController>()
                          .pessoaSeguro
                          .semExperiencia ==
                      true
                  ? () => onAddOrEdit(ExperienciaModel())
                  : null,
              hasContent: controller.experiencias!.isNotEmpty,
              content: ColoredBox(
                color: Colors.white,
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 15,
                  ),
                  child: Column(
                    children: [
                      ...controller.experiencias!.map(
                        (e) => Column(
                          children: [
                            DecoratedBox(
                              decoration: BoxDecoration(
                                color: const Color(0xFFf3f3fc)
                                    .withValues(alpha: 0.5),
                                borderRadius: const BorderRadius.all(
                                  Radius.circular(4),
                                ),
                                border: Border.all(
                                  color: const Color(0xFFdee2e6),
                                ),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Expanded(
                                          child: Text(
                                            e.cargo!,
                                            style: const TextStyle(
                                              fontSize: 14,
                                              fontWeight: FontWeight.w500,
                                            ),
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                        Padding(
                                          padding: const EdgeInsets.only(
                                            left: 5.0,
                                          ),
                                          child: Material(
                                            child: InkWell(
                                              onTap: () =>
                                                  onAddOrEdit(e.copy()),
                                              child: Image.asset(
                                                'lib/assets/icons/pencil-square.png',
                                                scale: 1.75,
                                              ),
                                            ),
                                          ),
                                        ),
                                        const SizedBox(width: 25),
                                        Material(
                                          child: InkWell(
                                            onTap: () => onRemove(e),
                                            child: const Icon(
                                              Icons.delete,
                                              color: Colors.red,
                                              size: 24,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 5),
                                    Text(
                                      '${e.empresa!} (${e.porte!}) ',
                                      style: const TextStyle(
                                        fontSize: 12,
                                      ),
                                    ),
                                    const SizedBox(height: 5),
                                    Text(
                                      e.periodoPorExtenso!,
                                      style: const TextStyle(
                                        color: Colors.grey,
                                        fontSize: 12,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            const SizedBox(
                              height: 5,
                            )
                          ],
                        ),
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      AddButton(
                        onTap: () => onAddOrEdit(ExperienciaModel()),
                        btnTitle: 'Adicionar experiência',
                      )
                    ],
                  ),
                ),
              ),
              noContent: Modular.get<CurriculumController>()
                          .pessoaSeguro
                          .semExperiencia ==
                      true
                  ? const ColoredBox(
                      color: Colors.white,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          Padding(
                            padding: EdgeInsets.symmetric(vertical: 20),
                            child: Text(
                              'Sem experiência',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    )
                  : CurriculumDefaultField(
                      bottomAdd: () => onAddOrEdit(ExperienciaModel()),
                      noContentButtom: () => noContent(),
                      noContent: true,
                      noContentText: 'Não possuo experiência',
                      textBottom: 'Adicionar experiência',
                      description:
                          'Inclua seu histórico profissional, estágios, voluntariado, etc.',
                    ),
            )
          : Container(),
    );
  }
}
