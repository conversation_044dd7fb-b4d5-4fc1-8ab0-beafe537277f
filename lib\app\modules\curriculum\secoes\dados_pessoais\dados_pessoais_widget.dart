import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:get/get.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';

import '../../../../shared/core/app_config.dart';
import '../../../../shared/helpers/snack_bar_helper.dart';
import '../../../../shared/models/salvar_pessoa_model.dart';
import '../../../../shared/widgets/app_image_network_widget.dart';
import '../../../../shared/widgets/app_usuario_card/app_usuario_card_controller.dart';
import '../../curriculum_controller.dart';
import '../../curriculum_editing/curriculum_editing_module.dart';
import '../../curriculum_editing/widgets/row_default.dart';
import '../../widgets/alerta_endereco.dart';
import '../../widgets/update_photo_dialog.dart';

class DadosPessoaisWidget extends StatefulWidget {
  const DadosPessoaisWidget({
    super.key,
  });
  @override
  _DadosPessoaisWidgetState createState() => _DadosPessoaisWidgetState();
}

class _DadosPessoaisWidgetState extends State<DadosPessoaisWidget> {
  final CurriculumController controller = Modular.get();
  final controllerUsuario = Modular.get<AppUsuarioCardController>();
  final maskFormatterBrazil = MaskTextInputFormatter(
    mask: "(##) #####-####",
  );

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 20,
      ),
      color: Colors.white,
      child: GetBuilder<CurriculumController>(
        init: controller,
        builder: (_) => Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Padding(
                  padding: const EdgeInsets.only(
                    bottom: 13.0,
                    top: 14.0,
                    right: 8,
                  ),
                  child: Stack(
                    children: [
                      ClipOval(
                        child: AppImageNetworkWidget(
                          height: 71,
                          width: 71,
                          fit: BoxFit.cover,
                          controller.pessoa?.foto ?? '',
                          errorImage: 'lib/assets/images/person-filled.png',
                          scale: 2,
                        ),
                      ),
                      Positioned(
                        bottom: 0,
                        right: 1,
                        child: InkWell(
                          onTap: () {
                            showDialog(
                              context: context,
                              builder: (context) {
                                return AlterarFotoDialog(
                                  controller: controller,
                                  controllerUsuario: controllerUsuario,
                                );
                              },
                            );
                          },
                          child: Container(
                            width: 25,
                            height: 25,
                            padding: const EdgeInsets.all(5),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(500),
                              color: const Color(0xFF3E3EDF),
                              border: Border.all(
                                color: Colors.white,
                                width: 2,
                              ),
                            ),
                            child: Image.asset(
                              'lib/assets/icons/editing.png',
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        controller.pessoa?.nome ?? '',
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(
                          fontSize: 22,
                          color: AppConfig.colorPrimary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      RichText(
                        text: TextSpan(
                          style: const TextStyle(
                            color: Color(0xFF444455),
                            fontFamily: "Inter",
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                          children: [
                            if (controller.pessoa?.estadoCivil != '')
                              TextSpan(
                                text: '${controller.pessoa?.estadoCivil}, ',
                              ),
                            TextSpan(text: '${controller.pessoa?.nascimento} '),
                            TextSpan(
                                text: '(${controller.pessoa?.idade} anos)'),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(
                    bottom: 45,
                  ),
                  child: Material(
                    child: InkWell(
                      customBorder: const CircleBorder(),
                      splashColor: Colors.grey.withValues(alpha: 0.9),
                      onTap: () async {
                        controller.pessoaToSave =
                            SalvarPessoaModel.copyFromResponse(
                          controller.pessoaSeguro,
                        );

                        var result = await Modular.to.pushNamed(
                          CurriculumEditingModule.route,
                          arguments: CurriculumEditingModule.dadosPessoais,
                        );

                        if (result == true) {
                          SnackbarHelper.showSnackbarSucesso(
                            context,
                            'Dados Pessoais salvos com sucesso',
                          );
                          controller.loadDadosPessoais();
                        }
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Image.asset(
                          'lib/assets/icons/pencil-square.png',
                          scale: 2,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            if ((controller.pessoa?.logradouro != null &&
                    controller.pessoa?.logradouro?.isEmpty == true) ||
                (controller.pessoa?.bairro != null &&
                    controller.pessoa?.bairro?.isEmpty == true))
              AlertaEndereco(
                controller: controller,
              ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: RowDefault(
                icon: const Icon(
                  Icons.location_on,
                  color: Colors.black,
                ),
                title: Expanded(
                  child: RichText(
                    text: TextSpan(
                      style: const TextStyle(
                        color: Color(0xFF444455),
                        fontFamily: "Inter",
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                      children: [
                        const TextSpan(text: 'Endereço: '),
                        if (controller.pessoa?.bairro?.isNotEmpty ?? false)
                          TextSpan(text: '${controller.pessoa?.bairro}, '),
                        if (controller.pessoa?.logradouro?.isNotEmpty ?? false)
                          TextSpan(text: '${controller.pessoa?.logradouro}, '),
                        if (controller.pessoa?.estado?.isNotEmpty ?? false)
                          TextSpan(text: '${controller.pessoa?.estado}, '),
                        if (controller.pessoa?.paisID?.isNotEmpty ?? false)
                          TextSpan(text: '${controller.pessoa?.paisID} - '),
                        if (controller.pessoa?.cepFormmater.isNotEmpty ?? false)
                          TextSpan(text: controller.pessoa?.cepFormmater),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: RowDefault(
                icon: const Icon(
                  Icons.phone,
                  color: Colors.black,
                ),
                title: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      _formatPhone(
                        controller.pessoa?.celular,
                        controller.pessoa?.celularPaisCode,
                        maskFormatterBrazil,
                      ),
                      textAlign: TextAlign.left,
                      style: const TextStyle(
                        color: Color(0xFF444455),
                        fontSize: 14,
                        fontFamily: "Inter",
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    if (controller.pessoa?.celular != null &&
                        controller.pessoa?.telefone != null)
                      const SizedBox(height: 8),
                    Text(
                      _formatPhone(
                        controller.pessoa?.telefone,
                        controller.pessoa?.telefonePaisCode,
                        maskFormatterBrazil,
                      ),
                      textAlign: TextAlign.left,
                      style: const TextStyle(
                        color: Color(0xFF444455),
                        fontSize: 14,
                        fontFamily: "Inter",
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: RowDefault(
                icon: const Icon(
                  Icons.email,
                  color: Colors.black,
                ),
                title: Text(
                  '${controller.pessoa?.email}',
                  style: const TextStyle(
                    color: Color(0xFF444455),
                    fontSize: 14,
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

String _formatPhone(
  String? phone,
  String? countryCode,
  dynamic maskFormatterBrazil,
) {
  if (phone == null) return 'Não informado';
  if (countryCode == '+55') {
    return '$countryCode ${maskFormatterBrazil.maskText(phone)}';
  }
  return '$countryCode $phone';
}
