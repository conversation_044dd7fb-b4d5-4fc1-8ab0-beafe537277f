import 'dart:async';

import 'package:device_preview/device_preview.dart';
import 'package:empregare_app/app/shared/core/app_config.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:timezone/data/latest.dart' as tz;

import 'app/app_module.dart';
import 'app/app_widget.dart';
import 'app/shared/firebase/firebase_messaging.dart';
import 'app/shared/helpers/timezone_helper.dart';
import 'app/shared/widgets/app_error_widget.dart';

/*

Esse é um usuário somente com uma conta vinculada:
usuário: 41810839858
senha: empregare2024

Usuário com várias contas vinculadas:
Usuário: <EMAIL>
Senha: 123456

*/
Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await SentryFlutter.init(
    (options) => options
      ..dsn = AppConfig.sentryDns
      ..sampleRate = AppConfig.sampleRate,
    appRunner: () async {
      if (kDebugMode || kProfileMode) {
        ErrorWidget.builder = (details) => AppErrorWidget(details);
      }
      await Firebase.initializeApp();

      await FirebaseMessagingUtils.instance.initialize();

      tz.initializeTimeZones();
      await TimezoneHelper.instance.getTimezone();

      runApp(
        DevicePreview(
          enabled: kDebugMode,
          tools: const [...DevicePreview.defaultTools],
          builder: (context) => ModularApp(
            module: AppModule(),
            child: const AppWidget(),
          ),
        ),
      );
    },
  );
}
