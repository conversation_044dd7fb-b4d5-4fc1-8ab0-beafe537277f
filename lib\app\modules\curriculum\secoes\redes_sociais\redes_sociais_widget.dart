import '../../controllers/social_media_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../curriculum_editing/curriculum_editing_module.dart';
import '../../../../shared/helpers/snack_bar_helper.dart';
import '../../widgets/social_network_line.dart';
import '../secao/secao_widget.dart';

class RedesSociaisWidget extends StatefulWidget {
  const RedesSociaisWidget({super.key});

  @override
  _RedesSociaisWidgetState createState() => _RedesSociaisWidgetState();
}

class _RedesSociaisWidgetState extends State<RedesSociaisWidget> {
  final SocialMediaController controller = Modular.get();

  void onAddOrEdit() async {
    var result = await Modular.to.pushNamed(
      CurriculumEditingModule.route,
      arguments: CurriculumEditingModule.redesSociais,
    );

    if (result == true) {
      SnackbarHelper.showSnackbarSucesso(
        context,
        'Redes Sociais salvas com sucesso',
      );
    }

    controller.loadRedesSociais();
  }

  bool verificar() {
    ///TODO VERIFICAR
    // if (controller.redesSociaisMap.name.contains(
    //   'ObservableList<Map<String, String?>>',
    // )) {}
    return true;
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SocialMediaController>(
      init: controller,
      builder: (_) {
        return SecaoWidget(
          header: 'Redes sociais e links'.i18n,
          replaceEditWithAdd: true,
          onEdit: onAddOrEdit,
          hasContent: true,
          content: Column(
            children: [
              ColoredBox(
                color: Colors.white,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const SizedBox(
                      height: 10,
                    ),
                    SocialNetworkLine(
                      controller: controller,
                      socialNetworkName: 'linkedin',
                      iconData: FontAwesomeIcons.linkedin,
                      title: verificar()
                          ? 'linkedin.com/in/url'
                          : controller.getRedeSocial('linkedin') ?? '',
                    ),
                    Divider(color: Colors.grey.shade300),
                    SocialNetworkLine(
                      controller: controller,
                      socialNetworkName: 'whatsapp',
                      iconData: FontAwesomeIcons.whatsapp,
                      title: verificar()
                          ? 'Selecione o número de telefone'
                          : controller.getRedeSocial('whatsapp') ?? '',
                    ),
                    Divider(color: Colors.grey.shade300),
                    SocialNetworkLine(
                      controller: controller,
                      socialNetworkName: 'facebook',
                      iconData: FontAwesomeIcons.facebook,
                      title: verificar()
                          ? 'facebook.com/seu-usuario'
                          : controller.getRedeSocial('facebook') ?? '',
                    ),
                    Divider(color: Colors.grey.shade300),
                    SocialNetworkLine(
                      controller: controller,
                      socialNetworkName: 'instagram',
                      iconData: FontAwesomeIcons.instagram,
                      title: verificar()
                          ? '@seu-perfil'
                          : controller.getRedeSocial('instagram') ?? '',
                    ),
                    Divider(color: Colors.grey.shade300),
                    SocialNetworkLine(
                      controller: controller,
                      socialNetworkName: 'github',
                      iconData: FontAwesomeIcons.github,
                      title: verificar()
                          ? '@seu-perfil'
                          : controller.getRedeSocial('github') ?? '',
                    ),
                    const SizedBox(
                      height: 10,
                    )
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
