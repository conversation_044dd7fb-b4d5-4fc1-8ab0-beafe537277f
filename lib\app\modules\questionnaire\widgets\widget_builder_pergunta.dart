import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../shared/models/questionario_salvar_pergunta_model.dart';
import '../questionnaire_controller.dart';
import 'widget_pergunta_anexo.dart';
import 'widget_pergunta_caixa_de_selecao_unica.dart';
import 'widget_pergunta_escolha_unica.dart';
import 'widget_pergunta_lista_suspensa.dart';
import 'widget_pergunta_lista_suspensa_multipla.dart';
import 'widget_pergunta_multipla_escolha.dart';
import 'widget_pergunta_texto_curta.dart';
import 'widget_pergunta_texto_longa.dart';
import 'widget_pergunta_titulo.dart';
import 'widget_pergunta_video.dart';

class WidgetPergunta extends StatefulWidget {
  const WidgetPergunta({
    super.key,
  });

  @override
  State<WidgetPergunta> createState() => _WidgetPerguntaState();
}

class _WidgetPerguntaState extends State<WidgetPergunta> {
  final controller = Modular.get<QuestionnaireController>();
  @override
  Widget build(BuildContext context) {
    return Observer(
      builder: (context) => Visibility(
        replacement: Container(),
        visible: controller.pergunta != null,
        child: Column(
          children: [
            Visibility(
              visible: controller.pergunta?.tipo != caixaSelecao,
              child: WidgetPerguntaTitulo(
                descricao: controller.pergunta?.descricao!.trim(),
                index: controller.index,
              ),
            ),
            if (controller.pergunta?.tipo == escolhaUnica)
              WidgetPerguntaEscolhaUnica(
                onChanged: (id) {
                  controller.setAlternativaUnica(id);
                  setState(() {});
                },
              )
            else if (controller.pergunta?.tipo == respostaMultilinha)
              PerguntaTextoLonga(
                context: context,
                controller: controller,
              )
            else if (controller.pergunta?.tipo == anexo)
              const WidgetPerguntaAnexo()
            else if (controller.pergunta?.tipo == video)
              WidgetPerguntaVideo(context: context, controller: controller)
            else if (controller.pergunta?.tipo == respostaCurta)
              PerguntaTextoCurta(
                controller: controller,
              )
            else if (controller.pergunta?.tipo == listaSuspensaMultipla)
              WidgetPerguntaListaSuspensaMultipla(
                onOptionSelected: (idOptions) {
                  controller.setAlternativesMultiple(idOptions);
                },
              )
            else if (controller.pergunta?.tipo == listaSuspensa)
              WidgetPerguntaListaSuspensa(
                onChanged: (idValue) {
                  controller.setAlternativaUnica(idValue);
                  setState(() {});
                },
              )
            else if (controller.pergunta?.tipo == caixaSelecao)
              const WidgetPerguntaCaixaDeSelecaoUnica()
            else if (controller.pergunta?.tipo == escolhaMultipla)
              WidgetPerguntaMultiplaEscolha(
                onTap: (id) {
                  controller.setAlternativasMultiplas(id);
                  setState(() {});
                },
              )
            else
              Container(
                color: const Color.fromARGB(255, 27, 24, 24),
                height: 80,
              )
          ],
        ),
      ),
    );
  }
}
