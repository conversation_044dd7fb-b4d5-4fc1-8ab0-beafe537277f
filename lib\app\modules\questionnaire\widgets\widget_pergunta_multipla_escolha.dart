import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../shared/app_container.dart';
import '../../../shared/core/app_config.dart';
import '../../../shared/widgets/app_html.dart';
import '../questionnaire_controller.dart';

class WidgetPerguntaMultiplaEscolha extends StatefulWidget {
  const WidgetPerguntaMultiplaEscolha({
    super.key,
    required this.onTap,
  });

  final Function(int id) onTap;

  @override
  State<WidgetPerguntaMultiplaEscolha> createState() =>
      _WidgetPerguntaMultiplaEscolhaState();
}

class _WidgetPerguntaMultiplaEscolhaState
    extends State<WidgetPerguntaMultiplaEscolha> {
  final controller = Modular.get<QuestionnaireController>();

  @override
  Widget build(BuildContext context) {
    var alternativa = controller.pergunta!.alternativas;
    if (controller.pergunta?.alternativas == null) {
      return const SizedBox.shrink();
    }

    return AppContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 6, vertical: 10),
            child: Text(
              'SELECIONE UMA, OU MAIS ALTERNATIVAS:',
              style: TextStyle(fontSize: 14),
              textAlign: TextAlign.left,
            ),
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: List.generate(
              controller.pergunta!.alternativas!.length,
              (i) {
                return Column(
                  children: [
                    InkWell(
                      onTap: () {
                        if (alternativa[i].id != null) {
                          widget.onTap(alternativa[i].id!);
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 5),
                        child: ColoredBox(
                          color: Colors.grey.shade100,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Checkbox(
                                value: controller.alternativasSelecionadas
                                    .contains(alternativa?[i].id),
                                onChanged: (value) {
                                  if (alternativa?[i].id != null) {
                                    widget.onTap(alternativa![i].id!);
                                  }
                                  setState(() {});
                                },
                                activeColor: AppConfig.colorPrimary,
                              ),
                              Expanded(
                                child: AppHtml(
                                  data: alternativa![i].descricao,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
