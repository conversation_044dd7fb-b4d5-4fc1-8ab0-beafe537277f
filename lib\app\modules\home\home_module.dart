import 'package:flutter_modular/flutter_modular.dart';

import '../../shared/widgets/app_usuario_card/app_usuario_card_controller.dart';
import '../confirm_subscription/confirm_subscription_controller.dart';
import '../curriculum/controllers/complementary_controller.dart';
import '../curriculum/controllers/computing_controller.dart';
import '../curriculum/controllers/deficiencies_controller.dart';
import '../curriculum/controllers/diversities_controller.dart';
import '../curriculum/controllers/experience_controller.dart';
import '../curriculum/controllers/languages_controller.dart';
import '../curriculum/controllers/objective_controller.dart';
import '../curriculum/controllers/social_media_controller.dart';
import '../curriculum/curriculum_controller.dart';
import '../jobs/job_detail/job_detail_controller.dart';
import '../questionnaire/questionnaire_controller.dart';
import '../settings/settings_controller.dart';
import '../subscription/subscription_controller.dart';
import 'home_controller.dart';
import 'home_page.dart';
import 'tab_jobs/tab_jobs_controller.dart';
import 'tab_jobs/tab_jobs_page.dart';
import 'tab_messages/tab_messages_controller.dart';
import 'tab_messages/tab_messages_page.dart';
import 'tab_notifications/tab_notifications_controller.dart';
import 'tab_notifications/tab_notifications_page.dart';
import 'tab_profile/tab_profile_controller.dart';
import 'tab_profile/tab_profile_page.dart';
import 'tab_subscriptions/tab_subscriptions_controller.dart';
import 'tab_subscriptions/tab_subscriptions_page.dart';

class HomeModule extends Module {
  static const route = '/home';
  static const tabNotifications = '/home/<USER>';
  static const tabJobs = '/home/<USER>';
  static const tabMessages = '/home/<USER>';
  static const tabSubscriptions = '/home/<USER>';
  static const tabProfile = '/home/<USER>';

  @override
  void binds(i) {
    i.addLazySingleton(HomeController.new);
    i.addLazySingleton(TabNotificationsController.new);
    i.addLazySingleton(TabMessagesController.new);
    i.addSingleton(TabSubscriptionsController.new);
    i.addLazySingleton(TabProfileController.new);
    i.addSingleton(JobDetailController.new);
    i.addLazySingleton(TabJobsController.new);
    i.addLazySingleton(QuestionnaireController.new);
    i.addLazySingleton(AppUsuarioCardController.new);
    i.addLazySingleton(SettingsController.new);
    i.addLazySingleton(ConfirmSubscriptionController.new);
    i.addSingleton(SubscriptionController.new);
    i.addLazySingleton(DeficienciesController.new);
    i.addLazySingleton(DiversitiesController.new);
    i.addLazySingleton(LanguagesController.new);
    i.addLazySingleton(ObjectiveController.new);
    i.addLazySingleton(SocialMediaController.new);
    i.addLazySingleton(ComputingController.new);
    i.addLazySingleton(ComplementaryController.new);
    i.addLazySingleton(ExperienceController.new);
    i.addLazySingleton(CurriculumController.new);
  }

  @override
  void routes(r) {
    r.child(
      '/',
      child: (context) => const HomePage(),
      children: [
        ChildRoute(
          '/tab_notifications',
          transition: TransitionType.fadeIn,
          duration: const Duration(milliseconds: 200),
          child: (context) => TabNotificationsPage(
            onSearchTap: r.args.data,
          ),
        ),
        ChildRoute(
          '/tab_jobs',
          transition: TransitionType.fadeIn,
          duration: const Duration(milliseconds: 200),
          child: (context) => const TabJobsPage(),
        ),
        ChildRoute(
          '/tab_messages',
          transition: TransitionType.fadeIn,
          duration: const Duration(milliseconds: 200),
          child: (context) => const TabMessagesPage(),
        ),
        ChildRoute(
          '/tab_subscriptions',
          transition: TransitionType.fadeIn,
          duration: const Duration(milliseconds: 200),
          child: (context) => const TabSubscriptionsPage(),
        ),
        ChildRoute(
          '/tab_profile',
          transition: TransitionType.fadeIn,
          duration: const Duration(milliseconds: 200),
          child: (context) => const TabProfilePage(),
        ),
      ],
    );
  }
}
