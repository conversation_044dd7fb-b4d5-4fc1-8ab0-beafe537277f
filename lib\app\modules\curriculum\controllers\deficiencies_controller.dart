import 'package:empregare_app/app/shared/mixins/loader_mixin.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:get/get.dart';

import '../../../shared/models/responses/categorias_deficiencias_response_model.dart';
import '../../../shared/models/responses/listar_deficiencias_response_model.dart';
import '../../../shared/models/responses/simple_response_model.dart';
import '../../../shared/models/salvar_deficiencias_model.dart';
import '../../../shared/repositories/curriculum_repository.dart';
import '../curriculum_controller.dart';

class DeficienciesController extends GetxController with LoaderMixin {
  final CurriculumRepository _repository = Modular.get();

  String? deficienciaTexto = '';

  Future load() async {
    try {
      changeLoading(true);
      await Future.wait([
        loadCategoriasDeficiencias(),
        loadDeficiencias(),
      ]);
    } finally {
      changeLoading(false);
    }
  }

  Future<bool> deleteLaudo(int? id) async {
    bool response = false;
    changeLoading(true);
    try {
      response = (await _repository.deleteLaudo(id));
    } finally {
      changeLoading(false);
      update();
    }
    return response;
  }

  Future<String?> downloadLaudo(String arquivo) async {
    try {
      loadingLaudo = true;
      update();
      await _repository.downloadLaudo(arquivo);
      await Future.delayed(const Duration(seconds: 2));
    } catch (e) {
      return "$e";
    } finally {
      loadingLaudo = false;
      update();
    }
    return null;
  }

  bool loadingDeficiencia = false;

  setLoadingDeficiencia(bool index) {
    loadingDeficiencia = index;
    update();
  }

  bool? loadingLaudo;

  List<DeficienciaLaudoModel>? laudos = <DeficienciaLaudoModel>[];

  List<SalvarDeficienciaModel> deficienciasToSave = <SalvarDeficienciaModel>[];

  List<CategoriaDeficienciaModel> categoriasDeficiencias =
      <CategoriaDeficienciaModel>[];

  List<DeficienciaModel>? deficiencias = <DeficienciaModel>[];

  List<Map<String, String?>> joinDeficiencias() {
    List<Map<String, String?>> result = [];

    String? categoria = '';
    String deficienciasStr = '';

    deficiencias?.sort((a, b) => (a.tipo ?? 0).compareTo(b.tipo ?? 0));
    deficiencias?.forEach((d) {
      if (categoria != '' && categoria != d.categoria) {
        result.add({
          'categoria': categoria,
          'deficiencias': deficienciasStr,
        });
        categoria = d.categoria;
        deficienciasStr = '';
      }
      categoria = d.categoria;
      deficienciasStr +=
          deficienciasStr == '' ? (d.nome ?? '') : (', ${d.nome ?? ''}');
    });

    result.add({
      'categoria': categoria,
      'deficiencias': deficienciasStr,
    });
    if (Modular.get<CurriculumController>().pessoaSeguro.deficienciaTexto !=
        null) {
      result.add({
        'categoria': 'Informações Adicionais',
        'deficiencias':
            Modular.get<CurriculumController>().pessoaSeguro.deficienciaTexto
      });
    }

    return result;
  }

  Future<SimpleResponseModel> saveDeficiencias({bool remover = false}) async {
    SimpleResponseModel response;
    loadingDeficiencia = true;
    update();
    try {
      SalvarDeficienciasModel model;
      if (remover) {
        model = SalvarDeficienciasModel(remover: 1);
      } else {
        model = SalvarDeficienciasModel(
          deficiencias: deficienciasToSave
              .where(
                (e) => e.deficienciaID != null,
              )
              .toList(),
          descricao: deficienciaTexto,
        );
      }
      response = (await _repository.saveDeficiencias(model));

      if (response.sucesso) {
        await loadDeficiencias();
      }
    } finally {
      loadingDeficiencia = false;
      update();
    }
    return response;
  }

  void addDeficienciaToSave(
    CategoriaDeficienciaModel model,
    String categoriaNome,
  ) {
    deficienciasToSave.add(
      SalvarDeficienciaModel(
        deficienciaID: model.id,
        nome: model.nome,
        tipo: model.tipo,
        categoriaNome: categoriaNome,
      ),
    );
    update();
  }

  void removeDeficienciaToSave(int? deficienciaID) {
    deficienciasToSave.removeWhere((d) => d.deficienciaID == deficienciaID);
    update();
  }

  Future loadDeficiencias() async {
    DeficienciaResponseModel response = await _repository.getDeficiencias();
    deficiencias = response.dados;
    deficienciasToSave = <SalvarDeficienciaModel>[];
    if (deficiencias != null) {
      deficiencias!
          .map((d) => SalvarDeficienciaModel(
                deficienciaID: d.deficienciaID,
                tipo: d.tipo,
                nome: d.nome,
                categoriaNome: d.categoria,
              ))
          .forEach((d) => deficienciasToSave.add(d));
    }
    laudos = response.laudo;
    update();
  }

  Future loadCategoriasDeficiencias() async {
    categoriasDeficiencias = [
      CategoriaDeficienciaModel(id: null, nome: 'Não possuo', tipo: null),
      ...(await _repository.getCategoriasDeficiencias()).dados ??
          <CategoriaDeficienciaModel>[]
    ];
    update();
  }

  CategoriaDeficienciaModel? get deficienciaAuditiva => deficienciaFilter(1);

  CategoriaDeficienciaModel? get deficienciaFisica => deficienciaFilter(3);

  CategoriaDeficienciaModel? get deficienciaVisual => deficienciaFilter(5);

  CategoriaDeficienciaModel? deficienciaIntelectual() => deficienciaFilter(4);

  CategoriaDeficienciaModel? deficienciaMental() => deficienciaFilter(6);

  CategoriaDeficienciaModel? deficienciaFilter(int tipo) {
    DeficienciaModel? result = deficiencias?.firstWhereOrNull((d) {
      return d.tipo == tipo;
    });

    if (result == null) return null;

    return CategoriaDeficienciaModel(
      id: result.deficienciaID,
      tipo: tipo,
      nome: result.nome,
    );
  }
}
