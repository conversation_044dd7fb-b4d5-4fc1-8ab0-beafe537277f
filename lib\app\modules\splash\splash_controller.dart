import 'dart:async';

import 'package:app_links/app_links.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

// import 'package:uni_links/uni_links.dart';

import '../../shared/firebase/firebase_messaging.dart';
import '../../shared/services/session_service.dart';
import '../curriculum/curriculum_module.dart';
import '../home/<USER>';
import '../home/<USER>';
import '../jobs/job_detail/job_detail_module.dart';
import '../questionnaire/questionnaire_module.dart';
import '../welcome/welcome_page.dart';

class SplashController {
  final SessionService sessionService = Modular.get();
  bool isDeepLink = false;

  void startTimer({num seconds = 3}) async {
    await MobileAds.instance.initialize();
    await MobileAds.instance.updateRequestConfiguration(RequestConfiguration(
      testDeviceIds: ['0867323924671AE423CE35CD4A2B2650'],
    ));

    if (!isDeepLink) {
      bool isLoggedIn = (await sessionService.isLoggedIn());
      if (isLoggedIn) {
        Modular.to.pushReplacementNamed(HomeModule.route);
        FirebaseMessagingUtils.instance.navigateTo();
      } else {
        Modular.to.pushReplacementNamed(WelcomePage.route);
      }
    }
    initUniLinks();
  }

  Future initUniLinks() async {
    final appLinks = AppLinks();

    Uri? initialUri = await appLinks.getInitialLink();
    if (initialUri != null) {
      isDeepLink = true;
      navigateByUri(initialUri);
    }

    appLinks.uriLinkStream.listen((linkUri) {
      isDeepLink = true;
      navigateByUri(linkUri);
    });
  }

  void navigateByUri(Uri? uri) async {
    bool isLoggedIn = (await sessionService.isLoggedIn());

    if (isLoggedIn) {
      if (Modular.to.canPop()) {
        Modular.to.pop();
      }

      Modular.to.pushReplacementNamed(HomeModule.route);
      List<String> segments = uri!.pathSegments;

      if (segments.contains("vagas")) {
        Modular.get<HomeController>().setTabSelected(1);
      } else if (segments.contains('questionario')) {
        String questionnaireId = segments.last.split("_")[0];
        Modular.to.pushNamed(QuestionnaireModule.route, arguments: int.parse(questionnaireId));
      } else if (segments.contains("mensagens")) {
        Modular.get<HomeController>().setTabSelected(2);
      } else if (segments.any((e) => e.contains("inscricao"))) {
        Modular.get<HomeController>().setTabSelected(3);
      } else if (segments.any((e) => e.contains("vaga-"))) {
        String jobIdentifier = segments.firstWhere((e) => e.contains("vaga-"));
        String jobId = jobIdentifier.substring(jobIdentifier.lastIndexOf('_') + 1);
        Modular.to.pushNamed(JobDetailModule.route, arguments: int.parse(jobId));
      } else if (segments.contains("curriculo")) {
        Modular.to.pushNamed(CurriculumModule.route);
      }
    } else {
      Modular.to.pushReplacementNamed(WelcomePage.route);
    }
  }
}
