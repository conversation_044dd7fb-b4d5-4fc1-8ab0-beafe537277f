import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../../controllers/deficiencies_controller.dart';
import '../../../curriculum_editing_controller.dart';

class DeficienciasCheckBoxes extends StatefulWidget {
  const DeficienciasCheckBoxes({
    super.key,
    required this.tipo,
    required this.categoriaNome,
  });

  final int tipo;
  final String categoriaNome;

  @override
  State<DeficienciasCheckBoxes> createState() => _DeficienciasCheckBoxesState();
}

class _DeficienciasCheckBoxesState extends State<DeficienciasCheckBoxes> {
  final DeficienciesController _deficienciesController = Modular.get();
  final controller = Modular.get<CurriculumEditingController>();

  @override
  Widget build(BuildContext context) {
    return Observer(
      builder: (_) => Column(
        children: _deficienciesController.categoriasDeficiencias
            .where((c) => c.tipo == widget.tipo)
            .map(
          (c) {
            return InkWell(
              onTap: () {
                if (_deficienciesController.deficienciasToSave
                    .where((d) => d.deficienciaID == c.id)
                    .isNotEmpty) {
                  _deficienciesController.removeDeficienciaToSave(c.id);
                } else {
                  _deficienciesController.addDeficienciaToSave(
                    c,
                    widget.categoriaNome,
                  );
                }
                setState(() {});
              },
              child: SizedBox(
                height: 60,
                child: Row(
                  children: [
                    Checkbox(
                      value: _deficienciesController.deficienciasToSave
                          .where((d) => d.deficienciaID == c.id)
                          .isNotEmpty,
                      onChanged: (value) {
                        if (value!) {
                          _deficienciesController.addDeficienciaToSave(
                            c,
                            widget.categoriaNome,
                          );
                        } else {
                          _deficienciesController.removeDeficienciaToSave(c.id);
                        }
                      },
                    ),
                    Text(
                      c.nome!,
                      style: const TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                        color: Colors.black,
                      ),
                    ),

                  ],
                ),
              ),
            );
          },
        ).toList(),
      ),
    );
  }
}
