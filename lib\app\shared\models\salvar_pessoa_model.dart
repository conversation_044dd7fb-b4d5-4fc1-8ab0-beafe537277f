import 'responses/pessoa_response_model.dart';

class SalvarPessoaModel {
  String? nome;
  String? sexo;
  int? cidadeID;
  final int? tipoCurriculo;
  String? paisID;
  int? estadoCivil;
  String? telefone;
  String? telefonePaisCode;
  String? celular;
  String? celularPaisCode;
  String? email;
  String? cep;
  String? logradouro;
  String? bairro;
  final bool? maeDesconhecida;
  final String? nomeMae;
  final bool? paiDesconhecido;
  final String? nomePai;
  DateTime? dataNascimento;
  int? estadoID;

  SalvarPessoaModel({
    this.nome,
    this.sexo,
    this.cidadeID,
    this.tipoCurriculo = 0,
    this.paisID,
    this.estadoCivil,
    this.telefone,
    this.telefonePaisCode,
    this.celular,
    this.celularPaisCode,
    this.email,
    this.cep,
    this.logradouro,
    this.bairro,
    this.maeDesconhecida = true,
    this.nomeMae,
    this.paiDesconhecido = true,
    this.nomePai,
    this.dataNascimento,
    this.estadoID,
  });

  Map<String, dynamic> toJson() {
    return {
      'nome': nome,
      'sexo': sexo,
      'cidadeID': cidadeID,
      'tipoCurriculo': tipoCurriculo,
      'paisID': paisID,
      'estadoCivil': estadoCivil,
      'telefone': telefone,
      'telefonePaisCode': telefonePaisCode,
      'celular': celular,
      'celularPaisCode': celularPaisCode,
      'email': email,
      'cep': cep,
      'logradouro': logradouro,
      'bairro': bairro,
      'maeDesconhecida': maeDesconhecida,
      'nomeMae': nomeMae,
      'paiDesconhecido': paiDesconhecido,
      'nomePai': nomePai,
      'dataNascimento': dataNascimento?.toIso8601String(),
      'estadoID': estadoID,
    };
  }

  static SalvarPessoaModel copyFromResponse(PessoaResponseModel response) {
    return SalvarPessoaModel(
      nome: response.nome,
      sexo: response.sexo,
      cidadeID: response.cidadeID,
      paisID: response.paisID,
      estadoCivil: response.estadoCivilID,
      telefone: response.telefone,
      telefonePaisCode: response.telefonePaisCode ?? '+55',
      celular: response.celular,
      celularPaisCode: response.celularPaisCode ?? '+55',
      email: response.email,
      cep: response.cep,
      logradouro: response.logradouro,
      bairro: response.bairro,
      dataNascimento:
          (response.ano != null && response.mes != null && response.dia != null)
              ? DateTime(response.ano!, response.mes!, response.dia!)
              : null,
      estadoID: response.estadoID,
    );
  }
}
