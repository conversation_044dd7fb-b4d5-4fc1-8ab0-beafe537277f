import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../shared/core/app_config.dart';
import '../questionnaire_controller.dart';

void showBottomSheetPhotoQuestionnaire(
  GlobalKey<ScaffoldState> sca<PERSON><PERSON><PERSON><PERSON>,
  QuestionnaireController controller,
  BuildContext context,
) {
  showBottomSheet(
    context: context,
    builder: (contextB) => Container(
      padding: const EdgeInsets.all(15),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Container(
            decoration: BoxDecoration(
                color: Colors.white, borderRadius: BorderRadius.circular(10)),
            child: Column(
              children: [
                TextButton(
                  child: const SizedBox(
                    height: 50,
                    child: Center(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text('Tirar Foto'),
                          Icon(Icons.camera_alt, color: AppConfig.colorPrimary),
                        ],
                      ),
                    ),
                  ),
                  onPressed: () async {
                    await controller.selecionarArquivo(tipo: 1);
                  },
                ),
                const Divider(),
                TextButton(
                  child: const SizedBox(
                    height: 50,
                    child: Center(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text('Galeria'),
                          Icon(
                            Icons.image,
                            color: AppConfig.colorPrimary,
                          )
                        ],
                      ),
                    ),
                  ),
                  onPressed: () async {
                    await controller.selecionarArquivo(tipo: 2);
                  },
                ),
                const Divider(),
                TextButton(
                  child: const SizedBox(
                    height: 50,
                    child: Center(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text('Explorar'),
                          Icon(Icons.more_horiz, color: AppConfig.colorPrimary)
                        ],
                      ),
                    ),
                  ),
                  onPressed: () async {
                    await controller.selecionarArquivo(tipo: 3);
                  },
                )
              ],
            ),
          ),
          const SizedBox(
            height: 10,
          ),
          TextButton(
            style: ButtonStyle(
              backgroundColor: WidgetStateProperty.all(Colors.white),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10.0),
                  side: const BorderSide(
                    color: Colors.red,
                  ),
                ),
              ),
            ),
            child: const SizedBox(
              height: 60,
              child: Center(
                child: Text(
                  'Cancelar',
                  style: TextStyle(
                    color: AppConfig.colorPrimary,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
            onPressed: () => Modular.to.pop(),
          )
        ],
      ),
    ),
    backgroundColor: Colors.black.withValues(alpha: 0.1),
  );
}
