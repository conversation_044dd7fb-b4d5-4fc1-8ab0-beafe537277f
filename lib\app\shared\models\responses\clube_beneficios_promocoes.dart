class ClubeBeneficiosPromocoesModel {
  final bool? sucesso;
  final List<BeneficiosPromocoesModel>? dados;

  ClubeBeneficiosPromocoesModel({
    this.sucesso,
    this.dados,
  });

  factory ClubeBeneficiosPromocoesModel.fromJson(Map<String, dynamic> json) {
    return ClubeBeneficiosPromocoesModel(
      sucesso: json['sucesso'] as bool?,
      dados: (json['dados'] as List<dynamic>?)
          ?.map((e) =>
              BeneficiosPromocoesModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class BeneficiosPromocoesModel {
  final int? id;
  final String? titulo;
  final String? descricao;
  final String? dataInicial;
  final String? dataFinal;
  final int? parceiroID;
  final String? nomeParceiro;
  final String? logoParceiro;

  BeneficiosPromocoesModel({
    this.id,
    this.titulo,
    this.descricao,
    this.dataInicial,
    this.dataFinal,
    this.parceiroID,
    this.nomeParceiro,
    this.logoParceiro,
  });

  factory BeneficiosPromocoesModel.fromJson(Map<String, dynamic> json) {
    return BeneficiosPromocoesModel(
      id: json['id'] as int?,
      titulo: json['titulo'] as String?,
      descricao: json['descricao'] as String?,
      dataInicial: json['dataInicial'] as String?,
      dataFinal: json['dataFinal'] as String?,
      parceiroID: json['parceiroID'] as int?,
      nomeParceiro: json['nomeParceiro'] as String?,
      logoParceiro: json['logoParceiro'] as String?,
    );
  }
}
