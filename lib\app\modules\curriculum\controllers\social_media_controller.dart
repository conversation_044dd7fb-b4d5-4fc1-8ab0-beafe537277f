import 'package:flutter_modular/flutter_modular.dart';
import 'package:get/get.dart';

import '../../../shared/models/responses/listar_redes_sociais_response_model.dart';
import '../../../shared/models/responses/simple_response_model.dart';
import '../../../shared/repositories/curriculum_repository.dart';
import '../curriculum_controller.dart';

class SocialMediaController extends GetxController {
  final CurriculumRepository _repository = Modular.get();

  bool loading = false;

  Future load() async {
    try {
      loading = true;
      update();
      await Future.wait([
        loadRedesSociais(),
      ]);
    } finally {
      loading = false;
      update();
    }
  }

  List<RedeSocialModel>? redesSociais = <RedeSocialModel>[]; //TODO VERIFICAR

  List<Map<String, String?>> redesSociaisMap = [
    {"key": "social-linkedin", "value": ""},
    {"key": "social-whatsapp", "value": ""},
    {"key": "social-facebook", "value": ""},
    {"key": "social-instagram", "value": ""},
    {"key": "social-skype", "value": ""},
    {"key": "social-twitter", "value": ""},
    {"key": "social-youtube", "value": ""},
    {"key": "social-google-plus", "value": ""},
    {"key": "social-tumblr", "value": ""},
    {"key": "social-soundcloud", "value": ""},
    {"key": "social-github", "value": ""},
    {"key": "social-lattes", "value": ""},
    {"key": "social-stack-overflow", "value": ""},
    {"key": "social-slideshare", "value": ""},
    {"key": "social-site", "value": ""}
  ];

  void updateMapRedesSociais(String? nome, String? valor) {
    if (nome != null) {
      for (var r2 in redesSociaisMap) {
        if (r2.containsValue("social-$nome")) {
          r2.update("value", (v) => valor);
        }
      }
      update();
    }
  }

  Future loadRedesSociais() async {
    redesSociais = (await _repository.getRedesSociais()).dados;
    if (redesSociais != null) {
      for (var r in redesSociais!) {
        updateMapRedesSociais(r.fonte?.replaceAll('globe', 'lattes'), r.url);
      }
    }
    update();
  }

  String? getRedeSocial(String nome) {
    String? value = '';
    for (var r2 in redesSociaisMap) {
      if (r2.containsValue("social-$nome")) {
        value = r2["value"];
      }
    }
    return value;
  }

  Future<void> reloadFromCurriculumController() async {
    try {
      final curriculumController = Modular.get<CurriculumController>();
      await curriculumController.reloadSection('redes-sociais');
    } catch (e) {
      await loadRedesSociais();
    }
  }

  Future<SimpleResponseModel> saveRedesSociais() async {
    SimpleResponseModel response;
    loading = true;
    update();
    try {
      response = (await _repository.saveRedesSociais(redesSociaisMap));
      if (response.sucesso) {
        await reloadFromCurriculumController();
      }
    } finally {
      loading = false;
      update();
    }
    return response;
  }
}
