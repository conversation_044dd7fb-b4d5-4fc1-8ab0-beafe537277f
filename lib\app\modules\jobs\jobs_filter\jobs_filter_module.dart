import 'package:flutter_modular/flutter_modular.dart';

import 'jobs_filter_controller.dart';
import 'jobs_filter_page.dart';

class JobsFilterModule extends Module {
  static const route = '/jobs_filter';

  @override
  void binds(i) {
    i.addLazySingleton(JobsFilterController.new);
  }

  @override
  void routes(r) {
    r.child(
      Modular.initialRoute,
      child: (context) => const JobsFilterPage(),
    );
  }
}
