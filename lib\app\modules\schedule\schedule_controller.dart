import 'package:flutter_modular/flutter_modular.dart';
import 'package:mobx/mobx.dart';

import '../../shared/models/agenda_salvar_model.dart';
import '../../shared/models/responses/agenda_response_model.dart';
import '../../shared/repositories/schedule_repository.dart';

part 'schedule_controller.g.dart';

class ScheduleController = _ScheduleControllerBase with _$ScheduleController;

abstract class _ScheduleControllerBase with Store {
  final ScheduleRepository _repository = Modular.get();

  final sPendente = 1;
  final sConfirmado = 2;
  final sRecusado = 3;

  @observable
  bool loading = false;

  @observable
  bool confirmando = false;

  @observable
  bool recusando = false;

  @observable
  String? _mensagem;
  String? get mensagem => _mensagem;

  @action
  setMensagem(String value) => _mensagem = value;

  @observable
  AgendaResponseModel? _compromisso;
  AgendaResponseModel? get compromisso => _compromisso;

  @action
  Future<void> load(String? token) async {
    try {
      loading = true;
      _compromisso = (await _repository.getCompromisso(token: token));
    } finally {
      loading = false;
    }
  }

  @action
  Future<void> confirmar() async {
    try {
      confirmando = true;
      await _repository.postCompromisso(AgendaSalvarModel(
          justificativa: mensagem, situacaoNova: sConfirmado, token: compromisso!.agenda!.token));
      Modular.to.pop();
    } finally {
      confirmando = false;
    }
  }

  @action
  Future<void> recusar() async {
    try {
      recusando = true;
      await _repository.postCompromisso(AgendaSalvarModel(
          justificativa: mensagem, situacaoNova: sRecusado, token: compromisso!.agenda!.token));
      Modular.to.pop();
    } finally {
      recusando = false;
    }
  }
}
