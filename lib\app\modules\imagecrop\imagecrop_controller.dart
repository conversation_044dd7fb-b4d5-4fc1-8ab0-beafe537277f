import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:image_crop_plus/image_crop_plus.dart';
import 'package:mobx/mobx.dart';

part 'imagecrop_controller.g.dart';

class ImagecropController = _ImagecropControllerBase with _$ImagecropController;

abstract class _ImagecropControllerBase with Store {
  final cropKey = GlobalKey<CropState>();

  @observable
  bool loading = true;

  @action
  void start() {
    Timer(const Duration(seconds: 3), () {
      loading = false;
    });
  }

  @action
  Future<void> cropImage(File? image) async {
    try {
      loading = true;
      final scale = cropKey.currentState!.scale;
      final area = cropKey.currentState!.area;
      if (area == null) {
        return;
      }

      final sample = await ImageCrop.sampleImage(
        file: image!,
        preferredSize: (2000 / scale).round(),
      );

      final file = await ImageCrop.cropImage(
        file: sample,
        area: area,
      );

      sample.delete();

      Modular.to.pop(file);
    } finally {
      loading = false;
    }
  }
}
