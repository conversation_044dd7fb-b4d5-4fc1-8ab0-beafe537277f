import 'dart:async';

import 'package:flutter_modular/flutter_modular.dart';
import 'package:mobx/mobx.dart';

import '../../../shared/models/job_busca.dart';
import '../../../shared/models/responses/cidade_response_model.dart';
import '../../../shared/repositories/cities_repository.dart';
import '../../../shared/services/storage_service.dart';

part 'jobs_search_controller.g.dart';

class JobsSearchController = _JobsSearchControllerBase
    with _$JobsSearchController;

abstract class _JobsSearchControllerBase with Store {
  final StorageService _storageService = Modular.get();
  final CitiesRepository _citiesRepository = Modular.get();

  @observable
  List<JobBuscaModel?> _ultimasBuscas = <JobBuscaModel>[].asObservable();
  List<JobBuscaModel?> get buscas => _ultimasBuscas;

  @observable
  JobBuscaModel _busca = JobBuscaModel();
  JobBuscaModel get busca => _busca;

  @observable
  List<CidadeModel>? _cidades = <CidadeModel>[].asObservable();
  List<CidadeModel>? get cidades => _cidades;

  Timer? debounceTimer;

  @action
  setBuscar(JobBuscaModel value) => _busca = value;

  @action
  setCargoOuEmpresa(String value) => _busca.cargoOuEmpresa = value.trim();

  @action
  setCidadeUfOuPais(String value) => _busca.cidadeUfOuPais = value.trim();

  @action
  Future<void> load() async {
    var result = (await _storageService.get("ULTIMASBUSCAS",
        construct: (value) => (value as List<dynamic>)
            .map((e) => JobBuscaModel(
                cargoOuEmpresa: e['cargoOuEmpresa'],
                cidadeUfOuPais: e['cidadeUfOuPais'],
                ordem: e['ordem']))
            .toList()));

    if (result != null) {
      _ultimasBuscas = (result as List<JobBuscaModel?>);
      _ultimasBuscas.sort((a, b) => b!.ordem!.compareTo(a!.ordem!));
    }
  }

  @action
  Future<void> buscar() async {
    if (_busca.isValid()) {
      _busca.ordem = _ultimasBuscas.length + 1;
      _ultimasBuscas.add(_busca);
      await _storageService.put(
        "ULTIMASBUSCAS",
        _ultimasBuscas.map((e) => e!.toJson()).toList(),
      );
    }

    Modular.to.pop(_busca);
  }

  @action
  Future<List<CidadeModel>?> getCidades(String query) async {
    try {
      if (debounceTimer != null) debounceTimer!.cancel();

      debounceTimer = Timer(const Duration(milliseconds: 500), () async {
        _cidades =
            (await _citiesRepository.getCidadeSugestions(query: query)).dados;
      });

      return _cidades;
    } catch (e) {
      return <CidadeModel>[].asObservable();
    }
  }
}
