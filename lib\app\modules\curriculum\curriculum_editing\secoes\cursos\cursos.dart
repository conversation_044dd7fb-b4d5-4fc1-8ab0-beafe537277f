import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../../../../shared/widgets/app_text_form_field.dart';
import '../../../curriculum_controller.dart';
import '../../curriculum_editing_controller.dart';
import '../../widgets/secao.dart';

class Cursos extends StatefulWidget {
  const Cursos({super.key, required this.formKey});

  final GlobalKey<FormState> formKey;

  @override
  State<Cursos> createState() => _CursosState();
}

class _CursosState extends State<Cursos> {
  final CurriculumController _curriculumController = Modular.get();
  final controller = Modular.get<CurriculumEditingController>();
  DateTime dataHoraAtual = DateTime.now();

  @override
  void initState() {
    super.initState();
    _curriculumController.cursoToSave!.anoI =
        int.tryParse(_curriculumController.cursoToSave!.anoInicio.toString()) ??
            0;
    _curriculumController.cursoToSave!.mesI =
        int.tryParse(_curriculumController.cursoToSave!.mesInicio.toString()) ??
            0;
  }

  @override
  Widget build(BuildContext context) {
    return Secao(
      fields: Form(
        autovalidateMode: AutovalidateMode.onUserInteraction,
        key: widget.formKey,
        child: Observer(builder: (_) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(bottom: 5),
                child: Text(
                  'CURSO/CERTIFICAÇÃO'.i18n,
                  style: const TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w400,
                    fontSize: 14,
                  ),
                ),
              ),
              AppTextFormField(
                hintText: 'CURSO/CERTIFICAÇÃO',
                controller: TextEditingController(
                  text: _curriculumController.cursoToSave!.nome,
                ),
                onChanged: (value) {
                  _curriculumController.cursoToSave!.nome = value;
                },
                validator: (value) {
                  if (value!.isEmpty) return 'Campo obrigatório'.i18n;

                  return null;
                },
              ),
              const SizedBox(height: 10),
              Padding(
                padding: const EdgeInsets.only(bottom: 5),
                child: Text(
                  'INSTITUIÇÃO'.i18n,
                  style: const TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w400,
                    fontSize: 14,
                  ),
                ),
              ),
              AppTextFormField(
                hintText: 'INSTITUIÇÃO',
                controller: TextEditingController(
                  text: _curriculumController.cursoToSave!.instituicao,
                ),
                onChanged: (value) {
                  _curriculumController.cursoToSave!.instituicao = value;
                },
                validator: (value) {
                  if (value!.isEmpty) return 'Campo obrigatório'.i18n;

                  return null;
                },
              ),
              const SizedBox(height: 10),
              Padding(
                padding: const EdgeInsets.only(bottom: 5),
                child: Text(
                  'CONTE-NOS SOBRE O CURSO'.i18n,
                  style: const TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w400,
                    fontSize: 14,
                  ),
                ),
              ),
              AppTextFormField(
                hintText: 'Informe o que você aprendeu com esse curso...',
                minLines: 5,
                maxLines: 15,
                controller: TextEditingController(
                  text: _curriculumController.cursoToSave!.descricao,
                ),
                onChanged: (value) {
                  _curriculumController.cursoToSave!.descricao = value;
                },
              ),
              const SizedBox(height: 10),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Expanded(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        AppTextFormField(
                          hintText: 'Ano do curso',
                          controller: TextEditingController(
                            text: _curriculumController.cursoToSave!.anoInicio,
                          ),
                          onTap: () async {
                            DateTime? newDate = await controller.selectDate(
                              context,
                              'ANO DO CURSO'.i18n,
                              ((_curriculumController.cursoToSave?.anoInicio ??
                                          '') !=
                                      '')
                                  ? DateTime(int.parse(
                                      _curriculumController
                                          .cursoToSave!.anoInicio!,
                                    ))
                                  : DateTime.now(),
                              format: 'yyyy',
                            );
                            if (newDate != null) {
                              setState(() {
                                _curriculumController.cursoToSave!.mesInicio =
                                    newDate.month.toString();
                                _curriculumController.cursoToSave!.anoInicio =
                                    newDate.year.toString();
                                _curriculumController.cursoToSave!.anoI =
                                    newDate.year;
                                _curriculumController.cursoToSave!.mesI =
                                    newDate.month;
                              });
                            }
                          },
                          readOnly: true,
                          textInputAction: TextInputAction.next,
                          validator: (value) {
                            if (value!.isEmpty) return 'Campo obrigatório'.i18n;

                            int valorString = int.parse(value);

                            int dataHoraAtual = DateTime.now().year;

                            if (valorString > dataHoraAtual) {
                              return 'Ano inválido';
                            }

                            return null;
                          },
                        ),
                        const Padding(
                          padding: EdgeInsets.only(top: 5.0, left: 5),
                          child: Text(
                            'Ano de inicio',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        AppTextFormField(
                          hintText: 'Carga horária',
                          keyboardType: TextInputType.number,
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(
                              RegExp(r'[0-9]'),
                            ),
                          ],
                          controller: TextEditingController(
                            text:
                                _curriculumController.cursoToSave!.cargaHoraria,
                          ),
                          onChanged: (value) {
                            _curriculumController.cursoToSave!.cargaHoraria =
                                value;
                          },
                          validator: (value) {
                            if (value!.isEmpty) {
                              return 'Campo obrigatório';
                            }
                            return null;
                          },
                        ),
                        const Padding(
                          padding: EdgeInsets.only(top: 5.0, left: 5),
                          child: Text(
                            'Hora(s)',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontWeight: FontWeight.w500,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          );
        }),
      ),
    );
  }
}
