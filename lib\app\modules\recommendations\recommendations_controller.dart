import 'package:flutter_modular/flutter_modular.dart';
import 'package:mobx/mobx.dart';

import '../../shared/models/responses/inscricoes_response_model.dart';
import '../../shared/repositories/job_repository.dart';

part 'recommendations_controller.g.dart';

class RecommendationsController = _RecommendationsControllerBase
    with _$RecommendationsController;

abstract class _RecommendationsControllerBase with Store {
  final JobRepository _repository = Modular.get();

  @observable
  bool loading = false;

  @observable
  bool loadingMore = false;

  @observable
  List<JobModel> recomendacoes = <JobModel>[].asObservable();

  @observable
  int currentPage = 1;

  @observable
  int? totalPaginas;

  @observable
  bool hasError = false;

  @observable
  String? errorMessage;

  @action
  Future<void> loadRecomendacoes({bool reload = false}) async {
    try {
      if (reload) {
        loading = true;
        currentPage = 1;
        recomendacoes.clear();
      } else {
        loadingMore = true;
      }

      hasError = false;
      errorMessage = null;

      final response = await _repository.getRecomendacoes(pagina: currentPage);

      if (response.dados != null && response.dados!.isNotEmpty) {
        if (reload) {
          recomendacoes = response.dados!.asObservable();
        } else {
          recomendacoes.addAll(response.dados!);
        }

        totalPaginas = response.totalPaginas?.floor();
        currentPage++;
      }
    } catch (exception) {
      hasError = true;
      errorMessage = 'Erro ao carregar recomendações: $exception';
    } finally {
      loading = false;
      loadingMore = false;
    }
  }

  @computed
  bool get canLoadMore =>
      totalPaginas != null && currentPage <= totalPaginas! && !loadingMore;

  @action
  Future<void> refresh() async {
    await loadRecomendacoes(reload: true);
  }
}
