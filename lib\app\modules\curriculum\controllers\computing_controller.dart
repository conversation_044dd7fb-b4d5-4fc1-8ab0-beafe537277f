import 'package:flutter_modular/flutter_modular.dart';
import 'package:get/get.dart';
import '../../../shared/mixins/loader_mixin.dart';

import '../../../shared/models/responses/listar_informatica_response_model.dart';
import '../../../shared/models/salvar_informatica_model.dart';
import '../../../shared/repositories/curriculum_repository.dart';
import '../curriculum_controller.dart';

class ComputingController extends GetxController with LoaderMixin {
  final CurriculumRepository _repository = Modular.get();

  List<InformaticaModel> informaticaToSave = <InformaticaModel>[];

  String informaticaBasico = '';
  String informaticaIntermediario = '';
  String informaticaAvancado = '';

  List<InformaticaModel>? informatica = <InformaticaModel>[];

  Future loadInformatica() async {
    try {
      changeLoading(true);
      final response = await _repository.getInformatica();
      informatica = response.dados;
      update();
    } finally {
      changeLoading(false);
    }
  }

  bool get hasChangeInformatica {
    var oldData = informatica?.map((e) => '${e.nivel} - ${e.nome}') ?? [];
    var newData = informaticaToSave.map((e) => '${e.nivel} - ${e.nome}');

    if (oldData.length != newData.length) {
      return true;
    }

    for (var item in newData) {
      if (oldData.contains(item) == false) {
        return true;
      }
    }

    return false;
  }

  Future load() async {
    try {
      changeLoading(true);
      await Future.wait([loadInformatica()]);
    } finally {
      changeLoading(false);
    }
  }

  Future<void> reloadFromCurriculumController() async {
    try {
      final curriculumController = Modular.get<CurriculumController>();
      await curriculumController.reloadSection('informatica');
    } catch (e) {
      await loadInformatica();
    }
  }

  Future<bool> saveInformatica() async {
    changeLoading(true);
    try {
      SalvarInformaticaModel salvarModel = SalvarInformaticaModel(
        avancado: informaticaToSave
            .where((i) => i.nivel == InformaticaModel.avancado)
            .map((i) => i.nome)
            .toList(),
        intermediario: informaticaToSave
            .where((i) => i.nivel == InformaticaModel.intermediario)
            .map((i) => i.nome)
            .toList(),
        basico: informaticaToSave
            .where((i) => i.nivel == InformaticaModel.basico)
            .map((i) => i.nome)
            .toList(),
      );

      bool response = await _repository.saveInformatica(salvarModel);

      if (response) {
        await reloadFromCurriculumController();
      }

      return response;
    } finally {
      changeLoading(false);
    }
  }
}
