import 'package:flutter_modular/flutter_modular.dart';
import 'package:mobx/mobx.dart';

import '../../shared/models/esqueci_senha_model.dart';
import '../../shared/models/responses/alterar_senha_email_response_model.dart';
import '../../shared/models/responses/iniciar_questionario_response_model.dart';
import '../../shared/models/responses/login_response_model.dart';
import '../../shared/models/responses/pais_response_model.dart';
import '../../shared/models/responses/verificar_senha_response_model.dart';
import '../../shared/models/session_model.dart';
import '../../shared/models/verificar_resposta_model.dart';
import '../../shared/repositories/cities_repository.dart';
import '../../shared/repositories/forgot_password_repository.dart';
import '../../shared/services/session_service.dart';
import 'utils/widget_access_recovery.dart';
import 'utils/widget_validation_failed.dart';

part 'forgot_password_controller.g.dart';

class ForgotPasswordController = _ForgotPasswordControllerBase
    with _$ForgotPasswordController;

abstract class _ForgotPasswordControllerBase with Store {
  final CitiesRepository _citiesRepository = Modular.get();
  final ForgotPasswordRepository _repository = Modular.get();

  @observable
  bool loading = false;
  @observable
  int currentStep = 1;

  @observable
  int questionStep = 1;

  @observable
  List<PaisModel> _paises = <PaisModel>[].asObservable();
  List<PaisModel> get paises => _paises;

  @observable
  PaisModel? _pais;
  PaisModel? get pais => _pais;
  setPais(value) => _pais = value;

  String _cpf = '';
  String get cpf => _cpf;
  setCpf(value) => _cpf = value;

  String _email = '';
  String get email => _email;
  setEmail(value) => _email = value;

  String? _token;
  String? _passwordRecoveryHash;

  @observable
  PerguntaModel? perguntaAtual;

  @action
  void setAlternativaSelecionada(String? alternativa) {
    if (perguntaAtual != null) {
      perguntaAtual = PerguntaModel(
        titulo: perguntaAtual!.titulo,
        tipo: perguntaAtual!.tipo,
        alternativas: perguntaAtual!.alternativas,
        alternativaSelecionada: alternativa,
      );
    }
  }

  _ForgotPasswordControllerBase() {
    init();
  }

  @action
  Future init() async {
    final paisResponseModel = await _citiesRepository.getPaises();
    _paises = (paisResponseModel.dados ?? []).asObservable();
  }

  @action
  void nextStep() => currentStep++;

  @action
  void changeStep(int step) => currentStep = step;

  @action
  void nextQuestStep() => questionStep++;

  @action
  Future<VerificarSenhaResponseModel> postVerificarSenha() async {
    loading = true;
    final response = await _repository.postVerificarSenha(
      EsqueciSenhaModel(
        cpf: _cpf,
        email: _email,
        paisID: _pais?.id,
      ),
    );
    loading = false;

    _token = response.token;

    return response;
  }

  @action
  Future<AlterarSenhaEmailResponseModel> postAlterarSenhaEmail() async {
    loading = true;

    final response = await _repository.postAlterarSenhaEmail(
      _token,
    );

    loading = false;
    return response;
  }

  @action
  Future<LoginResponseModel> newPassword(
    String newPassword,
    String confirmNewPassword,
  ) async {
    loading = true;

    final response = await _repository.newPassword(
      newPassword,
      confirmNewPassword,
      _passwordRecoveryHash ?? "",
    );

    if (response.sucesso) {
      final SessionService session = Modular.get();
      await session.set(SessionModel(token: response.token));
    }

    loading = false;
    return response;
  }

  bool get isFinalized {
    return perguntaAtual?.tipo?.toLowerCase() == "finalizou";
  }

  @action
  Future<IniciarQuestionarioResponseModel> postIniciarQuestionario() async {
    loading = true;

    final response = await _repository.postIniciarQuestionario(_token);
    perguntaAtual = response.pergunta;

    loading = false;
    return response;
  }

  @action
  void reset() {
    // _token = null;
    // _email = '';

    perguntaAtual = null;
    _passwordRecoveryHash = null;
    currentStep = 1;
    questionStep = 1;
  }

  @action
  Future<void> postVerificarResposta(
    String resposta,
  ) async {
    if (loading == true) return;

    loading = true;

    setAlternativaSelecionada(resposta);

    final model = VerificarRespostaModel(
      token: _token,
      tipoPergunta: perguntaAtual?.tipo,
      resposta: resposta,
    );

    final response = await _repository.postVerificarResposta(model);
    if (response.acertou == false) {
      reset();
      Modular.to.pushNamed(WidgetValidationFailed.route);
    } else {
      perguntaAtual = response.pergunta;
      _passwordRecoveryHash = response.link?.split('/').lastOrNull;

      if (isFinalized) {
        Modular.to.pushNamed(WidgetAccessRecovery.route);
      }

      nextQuestStep();
      nextStep();
    }

    loading = false;
  }
}
