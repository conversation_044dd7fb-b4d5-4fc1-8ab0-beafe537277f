import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:international_phone_input/international_phone_input.dart';

import '../../../shared/core/app_config.dart';
import '../../../shared/core/app_translation.i18n.dart';
import '../../../shared/helpers/snack_bar_helper.dart';
import '../../../shared/widgets/app_usuario_card/app_usuario_card_controller.dart';
import '../../curriculum/curriculum_controller.dart';
import 'curriculum_editing_controller.dart';
import 'curriculum_editing_module.dart';
import 'secoes/carta_apresentacao/carta_apresentacao.dart';
import 'secoes/cursos/cursos.dart';
import 'secoes/dados_pessoais/dados_pessoais.dart';
import 'secoes/deficiencias/deficiencias.dart';
import 'secoes/diversidade/diversidade.dart';
import 'secoes/experiencia/experiencia.dart';
import 'secoes/formacao_academica/formacao_academica.dart';
import 'secoes/idiomas/idiomas.dart';
import 'secoes/informacoes_complementares/informacoes_complementares.dart';
import 'secoes/informatica/informatica.dart';
import 'secoes/objetivo_pretensao/objetivo_pretensao.dart';
import 'secoes/redes_sociais/redes_sociais.dart';
import 'widgets/content_dialog_change_photo.dart';
import 'widgets/content_dialog_confirm_leave.dart';

class CurriculumEditingPage extends StatefulWidget {
  final String? secao;

  const CurriculumEditingPage({super.key, this.secao});

  @override
  _CurriculumEditingPageState createState() => _CurriculumEditingPageState();
}

class _CurriculumEditingPageState extends State<CurriculumEditingPage> {
  final CurriculumController _curriculumController = Modular.get();
  final controller = Modular.get<CurriculumEditingController>();

  final _scaffoldKey = GlobalKey<ScaffoldState>();
  final _dadosPessoaisFormKey = GlobalKey<FormState>();
  final _formacaoAcademicaFormKey = GlobalKey<FormState>();
  final _experienciaFormKey = GlobalKey<FormState>();
  final _cursoFormKey = GlobalKey<FormState>();
  final _informaticaAvancadoFormKey = GlobalKey<FormState>();
  final _informaticaIntermediarioFormKey = GlobalKey<FormState>();
  final _informaticaBasicoFormKey = GlobalKey<FormState>();

  late Function onSave;
  Function? onCancel;

  @override
  void dispose() {
    super.dispose();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      Modular.get<AppUsuarioCardController>().load();
    });
  }

  @override
  void initState() {
    super.initState();
    if (widget.secao == CurriculumEditingModule.dadosPessoais) {
      // Adiamos o carregamento para após o build atual
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _curriculumController.getPaises(clean: false);
        _curriculumController.getEstados(clean: false);
        _curriculumController.getCidades(clean: false);
      });

      onSave = () async {
        if (_curriculumController.pessoaToSave?.sexo == null) {
          controller.showSnackBarRequiredField('Gênero'.i18n, context);
          return;
        }
        if (_curriculumController.pessoaToSave?.paisID == null) {
          controller.showSnackBarRequiredField('País'.i18n, context);
          return;
        }
        if (_curriculumController.pessoaToSave?.estadoID == null) {
          controller.showSnackBarRequiredField('Estado'.i18n, context);
          return;
        }
        if (_curriculumController.pessoaToSave?.cidadeID == null) {
          controller.showSnackBarRequiredField('Cidade'.i18n, context);
          return;
        }
        if (_curriculumController.pessoaToSave?.cep == null) {
          controller.showSnackBarRequiredField('CEP'.i18n, context);
          return;
        }

        if (_dadosPessoaisFormKey.currentState != null &&
            !_dadosPessoaisFormKey.currentState!.validate()) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              backgroundColor: AppConfig.red,
              content: Text('Preencha o formulário corretamente'),
            ),
          );
          return;
        }

        var response = await _curriculumController.saveDadosPessoais();

        if (!mounted) return;

        if (response.sucesso) {
          Modular.to.pop(true);
        } else {
          SnackbarHelper.showSnackbarSucesso(context, response.mensagem);
        }
      };
    }

    if (widget.secao == CurriculumEditingModule.diversidade) {
      onSave = () async {
        var response =
            (await _curriculumController.diversities.saveDiversidade());

        if (_curriculumController
                .diversities.diversidadeToSave?.compartilharDadosDiversidade !=
            true) {
          return ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              backgroundColor: AppConfig.red,
              content: Text(
                'Por favor, marque a caixa para concordar em disponibilizar seus dados de diversidade!',
              ),
            ),
          );
        }

        if (response.sucesso) {
          Modular.to.pop(true);
        } else {
          SnackbarHelper.showSnackbarSucesso(context, response.mensagem);
        }
      };
    }

    if (widget.secao == CurriculumEditingModule.redesSociais) {
      onSave = () async {
        var response =
            (await _curriculumController.socialMedia.saveRedesSociais());

        var split = _curriculumController.socialMedia.redesSociaisMap
            .firstWhereOrNull((map) {
          return map.containsValue('social-whatsapp');
        })?["value"]?.split(" ");

        var number = split?.sublist(1).join() ?? "";
        var iso =
            Modular.get<CurriculumEditingController>().codeCountryWhatsapp;

        var numberValid = iso == null
            ? true
            : await PhoneService.parsePhoneNumber(
                number,
                iso,
              );

        if (!mounted) return;

        if (numberValid != true) {
          SnackbarHelper.showSnackbarSucesso(context, response.mensagem);
          return;
        }

        if (response.sucesso) {
          Modular.to.pop(true);
        } else {
          SnackbarHelper.showSnackbarSucesso(context, response.mensagem);
        }
      };
    }

    if (widget.secao == CurriculumEditingModule.deficiencias) {
      onSave = () async {
        if (controller.naoPossuoDeficiencia ||
            controller.selectedOption == null) {
          var response =
              await _curriculumController.deficiencies.saveDeficiencias(
            remover: true,
          );

          if (!mounted) return;

          if (response.sucesso) {
            Modular.to.pop(true);
          } else {
            controller.showSnackbarErro(context, response.mensagem);
          }
        } else if (controller.diretrizesLeiCotasPcd) {
          var response =
              await _curriculumController.deficiencies.saveDeficiencias();

          if (!mounted) return;

          if (response.sucesso) {
            Modular.to.pop(true);
          } else {
            controller.showSnackbarErro(context, response.mensagem);
          }
        } else {
          controller.showSnackbarErro(
            context,
            'Aceite os termos para continuar',
          );
        }
      };
    }

    if (widget.secao == CurriculumEditingModule.objetivoPretensao) {
      onSave = () async {
        final response = (await _curriculumController.objective.saveObjetivo());

        if (response.sucesso) {
          _curriculumController.loadDadosPessoais();
          _curriculumController.objective.load();
          Modular.to.pop(response.sucesso);
        } else {
          controller.showSnackbarErro(context, response.mensagem);
        }
      };
    }

    if (widget.secao == CurriculumEditingModule.cartaApresentacao) {
      onSave = () async {
        bool sucesso = (await _curriculumController.saveSintese());
        Modular.to.pop(sucesso);
      };
      onCancel = () {
        onCancelPressed(custom: () => _curriculumController.sintese = '');
      };
    }

    if (widget.secao == CurriculumEditingModule.formacaoAcademica) {
      onSave = () async {
        if (_curriculumController.formacaoToSave?.grauID == null) {
          controller.showSnackbarErro(context, 'Selecione um grau acadêmico');
          return null;
        }

        if (_formacaoAcademicaFormKey.currentState != null &&
            _formacaoAcademicaFormKey.currentState!.validate()) {
          var response = await _curriculumController.saveFormacaoAcademica();

          if (!mounted) return;

          if (response.sucesso) {
            Modular.to.pop(true);
          } else {
            controller.showSnackbarErro(context, response.mensagem);
          }
        } else {
          controller.showSnackbarErro(
            context,
            'Preencha os campos necessários.',
          );
        }
      };
    }

    if (widget.secao == CurriculumEditingModule.experiencias) {
      onSave = () async {
        if ((_experienciaFormKey.currentState == null ||
                !_experienciaFormKey.currentState!.validate()) ||
            _curriculumController.experience.experienciaToSave?.anoI == null ||
            _curriculumController.experience.experienciaToSave?.descricao ==
                null) {
          controller.showSnackbarErro(
            context,
            'Preencha os campos obrigatórios',
          );
          return;
        }

        if (_curriculumController.experience.experienciaToSave?.porte == null) {
          controller.showSnackBarRequiredField('Porte'.i18n, context);
          return;
        }

        var response =
            (await _curriculumController.experience.saveExperiencia());

        if (!mounted) return;

        if (response.sucesso) {
          Modular.to.pop(true);
        } else {
          SnackbarHelper.showSnackbarSucesso(context, response.mensagem);
        }
      };
    }

    if (widget.secao == CurriculumEditingModule.cursos) {
      onSave = () async {
        if (_cursoFormKey.currentState != null &&
            !_cursoFormKey.currentState!.validate()) {
          controller.showSnackbarErro(
            context,
            'Preencha os campos obrigatórios',
          );
          return;
        }
        var response = await _curriculumController.saveCurso();

        if (!mounted) return;

        if (response.sucesso) {
          return Modular.to.pop(true);
        } else {
          SnackbarHelper.showSnackbarSucesso(context, response.mensagem);
        }
      };
    }

    if (widget.secao == CurriculumEditingModule.idiomas) {
      onSave = () async {
        if (_curriculumController.languages.idiomaToSave?.idiomaID == null) {
          controller.showSnackBarRequiredField('IDIOMA'.i18n, context);
          return;
        }

        if (_curriculumController.languages.idiomaToSave?.nivel == null) {
          controller.showSnackBarRequiredField('NÍVEL'.i18n, context);
          return;
        }

        bool sucesso = (await _curriculumController.languages.saveIdioma());
        Modular.to.pop(sucesso);
      };
    }

    if (widget.secao == CurriculumEditingModule.informatica) {
      onSave = () async {
        if (_curriculumController.computing.hasChangeInformatica == false) {
          Modular.to.pop(false);
        } else {
          bool sucesso =
              await _curriculumController.computing.saveInformatica();
          Modular.to.pop(sucesso);
        }
      };
    }

    if (widget.secao == CurriculumEditingModule.informacoesComplementares) {
      onSave = () async {
        bool sucesso =
            (await _curriculumController.complementary.saveComplementar());
        Modular.to.pop(sucesso);
      };
    }
  }

  bool confirmLeave = false;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: confirmLeave,
      onPopInvokedWithResult: (leavee, _) async {
        if (confirmLeave) {
          return;
        }

        bool? leave = await showDialog(
          context: context,
          builder: (_) => const ContentDialogConfirmLeave(),
        );

        confirmLeave = leave ?? false;
        if (confirmLeave) Modular.to.pop();
      },
      child: Container(
        color: const Color(0xFFF2F6F7),
        child: SafeArea(
          top: false,
          child: Scaffold(
            key: _scaffoldKey,
            appBar: AppBar(
              backgroundColor: Colors.white,
              title: Text(
                '${widget.secao}',
                style: const TextStyle(
                  color: Colors.black,
                ),
              ),
              elevation: 0.5,
              leading: InkWell(
                onTap: () async {
                  bool? leave = await showDialog(
                    context: context,
                    builder: (_) => const ContentDialogConfirmLeave(),
                  );

                  confirmLeave = leave ?? false;
                  if (confirmLeave) {
                    Modular.to.pop();
                  }
                },
                customBorder: const CircleBorder(),
                splashColor: Colors.grey.withValues(alpha: 0.6),
                child: const Icon(
                  Icons.arrow_back_rounded,
                  color: Colors.black,
                  size: 28,
                ),
              ),
            ),
            backgroundColor: Colors.white,
            bottomSheet: Container(
              decoration: const BoxDecoration(
                color: Color(0xFFF2F6F7),
                borderRadius: BorderRadius.vertical(
                  top: Radius.circular(20),
                ),
              ),
              padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 30),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppConfig.white,
                      padding: const EdgeInsets.symmetric(
                        vertical: 15,
                        horizontal: 30,
                      ),
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                        side: const BorderSide(
                          color: AppConfig.colorPrimary,
                          width: 1.8,
                        ),
                      ),
                    ),
                    onPressed: () async {
                      bool? leave = await showDialog(
                        context: context,
                        builder: (_) => const ContentDialogConfirmLeave(),
                      );

                      confirmLeave = leave ?? false;
                      if (confirmLeave) {
                        Modular.to.pop();
                      }
                    },
                    child: Text(
                      'Cancelar'.i18n,
                      style: const TextStyle(
                        fontFamily: 'Inter',
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppConfig.colorPrimary,
                      padding: const EdgeInsets.symmetric(
                        vertical: 15,
                        horizontal: 60,
                      ),
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                        side: const BorderSide(
                          color: AppConfig.colorPrimary,
                          width: 1.8,
                        ),
                      ),
                    ),
                    onPressed: () {
                      confirmLeave = true;
                      onSave();
                    },
                    child: Text(
                      'Salvar'.i18n,
                      style: const TextStyle(
                        color: Colors.white,
                        fontFamily: 'Inter',
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            body: GetBuilder<CurriculumEditingController>(
              init: controller,
              autoRemove: false,
              builder: (editingController) {
                bool isLoading = (editingController.uploadingLaudo ?? false) ||
                    (_curriculumController.deficiencies.loadingLaudo ??
                        false) ||
                    (_curriculumController.loading);

                if (isLoading) {
                  return Material(
                    child: Container(
                      height: double.infinity,
                      color: Colors.white,
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            if (editingController.uploadingLaudo ?? false)
                              const Text(
                                'Enviando laudo.',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontSize: 22,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            const Text(
                              'Aguarde...',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: 22,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(height: 20),
                            const SpinKitCircle(
                              color: AppConfig.colorPrimary,
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                }

                return SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  child: Column(
                    children: [
                      if (widget.secao == CurriculumEditingModule.dadosPessoais)
                        DadosPessoais(
                          formKey: _dadosPessoaisFormKey,
                        ),
                      if (widget.secao == CurriculumEditingModule.redesSociais)
                        const RedesSociais(),
                      if (widget.secao == CurriculumEditingModule.deficiencias)
                        const Deficiencias(),
                      if (widget.secao ==
                          CurriculumEditingModule.objetivoPretensao)
                        const ObjetivoPretensao(),
                      if (widget.secao == CurriculumEditingModule.diversidade)
                        const Diversidade(),
                      if (widget.secao ==
                          CurriculumEditingModule.cartaApresentacao)
                        const CartaApresentacao(),
                      if (widget.secao ==
                          CurriculumEditingModule.formacaoAcademica)
                        FormacaoAcademica(
                          formKey: _formacaoAcademicaFormKey,
                        ),
                      if (widget.secao == CurriculumEditingModule.experiencias)
                        Experiencia(
                          formKey: _experienciaFormKey,
                        ),
                      if (widget.secao == CurriculumEditingModule.cursos)
                        Cursos(
                          formKey: _cursoFormKey,
                        ),
                      if (widget.secao == CurriculumEditingModule.idiomas)
                        const Idiomas(),
                      if (widget.secao == CurriculumEditingModule.informatica)
                        Informatica(
                          avancadoFormKey: _informaticaAvancadoFormKey,
                          intermediarioFormKey:
                              _informaticaIntermediarioFormKey,
                          basicoFormKey: _informaticaBasicoFormKey,
                        ),
                      if (widget.secao ==
                          CurriculumEditingModule.informacoesComplementares)
                        const InformacoesComplementares(),
                      const SizedBox(height: 100),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  void onCancelPressed({Function? custom}) async {
    bool leave = await showDialog(
      context: context,
      builder: (_) => const ContentDialogConfirmLeave(),
    );
    if (leave) {
      if (custom != null) custom();
      Modular.to.pop();
    }
  }

  Widget semFoto() {
    return InkWell(
      onTap: () {
        showDialog(
          context: context,
          builder: (_) {
            return const ContentDialogChangePhoto();
          },
        );
      },
      child: SizedBox(
        width: 133,
        height: 130,
        child: Card(
          elevation: 5,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.perm_identity,
                color: Colors.grey,
                size: 80,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.photo_camera,
                    color: Colors.blue,
                    size: 20,
                  ),
                  const SizedBox(width: 10),
                  Text(
                    'ADICIONAR FOTO'.i18n,
                    style: const TextStyle(
                      color: Colors.blue,
                      fontSize: 10,
                    ),
                  ),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}
