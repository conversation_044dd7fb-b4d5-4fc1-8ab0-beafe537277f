import 'package:flutter/material.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../../../shared/core/app_config.dart';

class AddButton extends StatelessWidget {
  const AddButton({
    super.key,
    required this.onTap,
  });

  final Function onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap as void Function()?,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.add,
            color: AppConfig.colorPrimary,
          ),
          Text(
            'ADICIONAR'.i18n,
            style: const TextStyle(
              color: AppConfig.colorPrimary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
