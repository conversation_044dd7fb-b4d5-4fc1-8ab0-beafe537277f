class ExperienciaResponseModel {
  final bool sucesso;
  final String mensagem;
  final List<ExperienciaModel>? dados;

  ExperienciaResponseModel({
    this.sucesso = false,
    this.mensagem = "",
    this.dados,
  });

  factory ExperienciaResponseModel.fromJson(Map<String, dynamic> json) =>
      ExperienciaResponseModel(
        sucesso: json['sucesso'] ?? false,
        mensagem: json['mensagem'] ?? "",
        dados: (json['dados'] as List<dynamic>?)
            ?.map((e) => ExperienciaModel.fromJson(e as Map<String, dynamic>))
            .toList(),
      );
}

class ExperienciaModel {
  final int? id;
  String? cargo;
  String? empresa;
  String? cidade;
  String? porte;
  int? porteID;
  String? mesInicio;
  String? mesFim;
  String? anoInicio;
  String? anoFim;
  bool? atual;
  final String? periodoPorExtenso;
  bool? voluntario;
  String? descricao;
  String? resultado;
  String? salario;

  ExperienciaModel({
    this.id,
    this.cargo,
    this.empresa,
    this.cidade,
    this.porte,
    this.porteID,
    this.mesInicio,
    this.mesFim,
    this.anoInicio,
    this.anoFim,
    this.atual = false,
    this.periodoPorExtenso,
    this.voluntario = false,
    this.descricao,
    this.resultado,
    this.salario,
  });

  factory ExperienciaModel.fromJson(Map<String, dynamic> json) =>
      ExperienciaModel(
        id: json['id'] as int?,
        cargo: json['cargo'] as String?,
        empresa: json['empresa'] as String?,
        cidade: json['cidade'] as String?,
        porte: json['porte'] as String?,
        porteID: json['porteID'] as int?,
        mesInicio: json['mesInicio'] as String?,
        mesFim: json['mesFim'] as String?,
        anoInicio: json['anoInicio'] as String?,
        anoFim: json['anoFim'] as String?,
        atual: json['atual'] as bool? ?? false,
        periodoPorExtenso: json['periodoPorExtenso'] as String?,
        voluntario: json['voluntario'] as bool? ?? false,
        descricao: json['descricao'] as String?,
        resultado: json['resultado'] as String?,
        salario: json['salario'] as String?,
      );

  ExperienciaModel copy() {
    return ExperienciaModel(
      id: id,
      cargo: cargo,
      empresa: empresa,
      cidade: cidade,
      porte: porte,
      porteID: porteID,
      mesInicio: mesInicio,
      mesFim: mesFim,
      anoInicio: anoInicio,
      anoFim: anoFim,
      atual: atual,
      periodoPorExtenso: periodoPorExtenso,
      voluntario: voluntario,
      descricao: descricao,
      resultado: resultado,
      salario: salario,
    );
  }
}
