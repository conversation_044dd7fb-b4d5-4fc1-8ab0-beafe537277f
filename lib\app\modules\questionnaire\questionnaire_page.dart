import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../shared/models/questionario_salvar_pergunta_model.dart';
import '../../shared/models/responses/questionario_response_model.dart';
import '../../shared/widgets/app_bar_default.dart';
import 'questionnaire_controller.dart';
import 'widgets/widget_builder_body.dart';
import 'widgets/widget_fixed_buttom.dart';
import 'widgets/widget_pergunta_tempo.dart';

class QuestionnairePage extends StatefulWidget {
  final int? questionarioId;

  const QuestionnairePage({super.key, required this.questionarioId});

  @override
  _QuestionnairePageState createState() => _QuestionnairePageState();
}

class _QuestionnairePageState extends State<QuestionnairePage> {
  final controller = Modular.get<QuestionnaireController>();
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    controller.load(widget.questionarioId);
  }

  QuestionarioModel? get questionario => controller.questionario;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: SafeArea(
        top: false,
        child: Scaffold(
          key: _scaffoldKey,
          backgroundColor: Colors.white,
          appBar: AppBarDefault(
            title: Observer(
              builder: (_) => Text(controller.appBarTitle!),
            ),
            subTitle: Observer(
              builder: (_) => Text(
                controller.appBarSubTitle,
                style: const TextStyle(fontSize: 14, color: Colors.white),
              ),
            ),
          ),
          body: GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              FocusScope.of(context).requestFocus(FocusNode());
            },
            child: Column(
              children: [
                Observer(
                  builder: (_) => Visibility(
                    visible: controller.pergunta != null &&
                        controller.pergunta!.tempo! > 0 &&
                        controller.pergunta!.tipo != video,
                    replacement: Container(),
                    child: WidgetPerguntaTempo(
                      key: Key(controller.pergunta?.id ?? '0'),
                      tempo: controller.pergunta?.tempo ?? 0,
                      onFinished: () {
                        controller.responder(def: 1);
                      },
                    ),
                  ),
                ),
                Observer(
                  builder: (_) => Expanded(
                    child: SingleChildScrollView(
                      child: WidgetBuilderBody(
                        controller: controller,
                        scaffoldKey: _scaffoldKey,
                        questionario: questionario,
                        context: context,
                      ),
                    ),
                  ),
                )
              ],
            ),
          ),
          bottomNavigationBar: Observer(
            builder: (_) => WidgetFixedButtom(controller: controller),
          ),
        ),
      ),
    );
  }
}
