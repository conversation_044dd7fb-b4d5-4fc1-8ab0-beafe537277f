class PretensoesResponseModel {
  final bool sucesso;
  final String mensagem;
  final List<PretensaoModel>? dados;

  PretensoesResponseModel(
      {this.sucesso = false, this.mensagem = "", this.dados});

  factory PretensoesResponseModel.fromJson(Map<String, dynamic> json) {
    return PretensoesResponseModel(
      sucesso: json['sucesso'] ?? false,
      mensagem: json['mensagem'] ?? "",
      dados: (json['dados'] as List<dynamic>?)
          ?.map((e) => PretensaoModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class PretensaoModel {
  final String? id;
  final String? titulo;

  PretensaoModel({this.id, this.titulo});
  factory PretensaoModel.fromJson(Map<String, dynamic> json) {
    return PretensaoModel(
      id: json['id'] as String?,
      titulo: json['titulo'] as String?,
    );
  }

  String get tituloSeguro => titulo ?? '';

  @override
  String toString() => tituloSeguro;

  @override
  operator ==(other) => other is PretensaoModel && other.id == id;

  @override
  int get hashCode => id.hashCode ^ tituloSeguro.hashCode;
}
