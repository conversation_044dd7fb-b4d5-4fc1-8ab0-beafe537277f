import 'package:flutter/material.dart';

class InfoColumn extends StatelessWidget {
  const InfoColumn({
    super.key,
    this.time,
    this.workModel,
    this.typeContract,
    this.level,
  });

  final String? time;

  final String? workModel;

  final String? typeContract;

  final String? level;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Horário',
          style: TextStyle(
            color: Color(0xFF252525),
            fontSize: 14,
            fontWeight: FontWeight.w700,
            fontFamily: 'Inter',
          ),
        ),
        const SizedBox(height: 4),
        Text(
          time ?? '',
          style: const TextStyle(
            color: Color(0xFF252525),
            fontFamily: 'Inter',
            fontWeight: FontWeight.w400,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 16),
        const Text(
          'Modelo de tabalho',
          style: TextStyle(
            color: Color(0xFF252525),
            fontSize: 14,
            fontWeight: FontWeight.w700,
            fontFamily: 'Inter',
          ),
        ),
        const SizedBox(height: 4),
        Text(
          workModel ?? '',
          style: const TextStyle(
            color: Color(0xFF252525),
            fontFamily: 'Inter',
            fontWeight: FontWeight.w400,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 16),
        const Text(
          'Tipo de contratação',
          style: TextStyle(
            color: Color(0xFF252525),
            fontSize: 14,
            fontWeight: FontWeight.w700,
            fontFamily: 'Inter',
          ),
        ),
        const SizedBox(height: 4),
        Text(
          typeContract ?? '',
          style: const TextStyle(
            color: Color(0xFF252525),
            fontFamily: 'Inter',
            fontWeight: FontWeight.w400,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 16),
        const Text(
          'Nível',
          style: TextStyle(
            color: Color(0xFF252525),
            fontSize: 14,
            fontWeight: FontWeight.w700,
            fontFamily: 'Inter',
          ),
        ),
        const SizedBox(height: 4),
        Text(
          level ?? '',
          style: const TextStyle(
            color: Color(0xFF252525),
            fontFamily: 'Inter',
            fontWeight: FontWeight.w400,
            fontSize: 14,
          ),
        ),
      ],
    );
  }
}
