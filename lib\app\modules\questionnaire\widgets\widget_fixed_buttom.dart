import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../shared/core/app_translation.i18n.dart';
import '../../../shared/models/questionario_salvar_pergunta_model.dart';
import '../../../shared/widgets/app_default_button.dart';
import '../questionnaire_controller.dart';

class WidgetFixedButtom extends StatefulWidget {
  const WidgetFixedButtom({
    super.key,
    required this.controller,
  });

  final QuestionnaireController controller;

  @override
  State<WidgetFixedButtom> createState() => _WidgetFixedButtomState();
}

class _WidgetFixedButtomState extends State<WidgetFixedButtom> {
  final controller = Modular.get<QuestionnaireController>();

  @override
  Widget build(BuildContext context) {
    return Observer(
      builder: (context) {
        final bool isButtonEnabled = widget.controller.validate;
        String title() {
          if (widget.controller.state == QuestionnaireState.guideLines) {
            return 'INICIAR QUESTIONÁRIO';
          } else if (widget.controller.state == QuestionnaireState.responding) {
            return 'SALVAR E AVANÇAR';
          }
          return '';
        }

        if (!(widget.controller.state == QuestionnaireState.guideLines ||
            (widget.controller.pergunta != null &&
                tiposDePerguntas.contains(widget.controller.pergunta!.tipo)))) {
          return const SizedBox(height: 1);
        }

        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  offset: const Offset(0.0, -1.0),
                  blurRadius: 10.0,
                  spreadRadius: 0.0)
            ],
          ),
          padding: const EdgeInsets.symmetric(vertical: 7, horizontal: 15),
          child: SizedBox(
            height: 45,
            width: double.infinity,
            child: AppDefaultButton(
              onPressed: isButtonEnabled
                  ? () async {
                      if (widget.controller.state ==
                          QuestionnaireState.guideLines) {
                        await widget.controller.iniciar();
                      } else if (widget.controller.state ==
                          QuestionnaireState.responding) {
                        await widget.controller.responder();
                      }
                    }
                  : null,
              title: Text(
                title().i18n,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
