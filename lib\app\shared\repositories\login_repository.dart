import 'package:dio/dio.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../core/app_rest.dart';
import '../models/login_model.dart';
import '../models/responses/login_response_model.dart';

class LoginRepository {
  final AppRest _rest = Modular.get();

  Future<LoginResponseModel> postLogin(LoginModel model) async {
    try {
      final response = await _rest.post(
        '/candidato/login',
        data: model.toJson(),
      );

      return LoginResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return LoginResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return LoginResponseModel.fromJson(err.response!.data);
    }
  }
}
