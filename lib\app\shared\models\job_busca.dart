class JobBuscaModel {
  String? cargoOuEmpresa;

  String? cidadeUfOuPais;

  int? ordem;

  bool isValid() =>
      (cargoOuEmpresa != null && cargoOuEmpresa!.trim().isNotEmpty) ||
      (cidadeUfOuPais != null && cidadeUfOuPais!.trim().isNotEmpty);

  JobBuscaModel({this.cargoOuEmpresa, this.cidadeUfOuPais, this.ordem = 1});

  Map<String, dynamic> toJson() {
    return {
      'cargoOuEmpresa': cargoOuEmpresa,
      'cidadeUfOuPais': cidadeUfOuPais,
      'ordem': ordem,
    };
  }
}
