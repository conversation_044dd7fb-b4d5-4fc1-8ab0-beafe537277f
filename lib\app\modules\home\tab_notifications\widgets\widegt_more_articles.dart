import 'package:flutter/material.dart';

import '../tab_notifications_controller.dart';
import 'widget_our_articles.dart';

class WidgetMoreArticles extends StatelessWidget {
  const WidgetMoreArticles({
    super.key,
    this.controller,
  });

  final TabNotificationsController? controller;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        leadingWidth: 0,
        leading: const SizedBox.shrink(),
        elevation: 1,
        shadowColor: Colors.black45,
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  Navigator.of(context).pop();
                },
                customBorder: const CircleBorder(),
                splashColor: Colors.grey.withValues(alpha: 0.6),
                child: Container(
                  padding: const EdgeInsets.all(8.0),
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.close,
                    color: Colors.black,
                    size: 36,
                  ),
                ),
              ),
            ),
          ),
        ],
        title: const Text(
          'Nossos Artigos',
          style: TextStyle(
            color: Colors.black,
            fontFamily: 'Inter',
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      body: ListView(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Padding(
                  padding: EdgeInsets.symmetric(
                    vertical: 10.0,
                  ),
                  child: Text(
                    'Leia nossos artigos especialmente preparados para você',
                    style: TextStyle(
                      color: Colors.black,
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w300,
                    ),
                  ),
                ),
                Flexible(
                  fit: FlexFit.loose,
                  child: ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 5,
                      vertical: 10,
                    ),
                    itemCount: controller?.articles?.dados.length ?? 0,
                    itemBuilder: (context, index) {
                      final articles = controller?.articles?.dados[index];
                      return OurArticles(
                        path: articles?.image,
                        title: articles?.title.rendered,
                        link: articles?.link,
                        descripition: articles?.excerpt.rendered,
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
