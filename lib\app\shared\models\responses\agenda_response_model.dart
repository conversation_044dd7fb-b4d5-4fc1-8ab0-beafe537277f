class AgendaResponseModel {
  final bool sucesso;
  final String mensagem;
  final String? token;
  final AgendaModel? agenda;
  final SalaModel? sala;
  final List<ResponsavelModel>? responsaveis;

  AgendaResponseModel({
    this.agenda,
    this.sala,
    this.responsaveis,
    this.sucesso = false,
    this.mensagem = "",
    this.token,
  });

  factory AgendaResponseModel.fromJson(Map<String, dynamic> json) {
    return AgendaResponseModel(
      agenda: json['agenda'] == null
          ? null
          : AgendaModel.fromJson(json['agenda'] as Map<String, dynamic>),
      sala: json['sala'] == null
          ? null
          : SalaModel.fromJson(json['sala'] as Map<String, dynamic>),
      responsaveis: (json['responsaveis'] as List<dynamic>?)
          ?.map((e) => ResponsavelModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      sucesso: json['sucesso'] ?? false,
      mensagem: json['mensagem'] ?? "",
      token: json['token'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'sucesso': sucesso,
      'token': token,
      'agenda': agenda,
      'sala': sala,
      'responsaveis': responsaveis,
    };
  }
}

class AgendaModel {
  final String? titulo;
  final String? token;
  final int? duracao;
  final String? dataInicial;
  final String? dataFinal;
  final String? timezone;
  final String? dataCompleta;
  final int? situacaoAgenda;
  final int? modalidade;
  final String? modalidadeTexto;
  final String? tipoAgendamento;
  final String? orientacao;
  final int? situacaoParticipante;
  final String? situacaoParticipanteTexto;
  final String? justificativa;

  AgendaModel({
    this.titulo,
    this.token,
    this.duracao,
    this.dataInicial,
    this.dataFinal,
    this.timezone,
    this.dataCompleta,
    this.situacaoAgenda,
    this.modalidade,
    this.modalidadeTexto,
    this.tipoAgendamento,
    this.orientacao,
    this.situacaoParticipante,
    this.situacaoParticipanteTexto,
    this.justificativa,
  });

  factory AgendaModel.fromJson(Map<String, dynamic> json) {
    return AgendaModel(
      titulo: json['titulo'] as String?,
      token: json['token'] as String?,
      duracao: json['duracao'] as int?,
      dataInicial: json['dataInicial'] as String?,
      dataFinal: json['dataFinal'] as String?,
      timezone: json['timezone'] as String?,
      dataCompleta: json['dataCompleta'] as String?,
      situacaoAgenda: json['situacaoAgenda'] as int?,
      modalidade: json['modalidade'] as int?,
      modalidadeTexto: json['modalidadeTexto'] as String?,
      tipoAgendamento: json['tipoAgendamento'] as String?,
      orientacao: json['orientacao'] as String?,
      situacaoParticipante: json['situacaoParticipante'] as int?,
      situacaoParticipanteTexto: json['situacaoParticipanteTexto'] as String?,
      justificativa: json['justificativa'] as String?,
    );
  }
}

class SalaModel {
  final String? titulo;
  final String? endereco;
  final String? salaVirtual;

  SalaModel({this.titulo, this.endereco, this.salaVirtual});

  factory SalaModel.fromJson(Map<String, dynamic> json) {
    return SalaModel(
      titulo: json['titulo'] as String?,
      endereco: json['endereco'] as String?,
      salaVirtual: json['salaVirtual'] as String?,
    );
  }
}

class ResponsavelModel {
  final String? nome;

  ResponsavelModel({this.nome});

  factory ResponsavelModel.fromJson(Map<String, dynamic> json) {
    return ResponsavelModel(
      nome: json['nome'] as String?,
    );
  }
}
