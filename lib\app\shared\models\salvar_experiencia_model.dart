class SalvarExperienciaModel {
  final int? id;
  String? cargo;
  String? empresa;
  String? cidade;
  int? porte;
  String? mesI;
  String? mesF;
  String? anoI;
  String? anoF;
  bool? atual;
  final String? periodoPorExtenso;
  bool? voluntario;
  String? descricao;
  String? resultado;
  String? salario;

  SalvarExperienciaModel(
      {this.id,
      this.cargo,
      this.empresa,
      this.cidade,
      this.porte,
      this.mesI,
      this.mesF,
      this.anoI,
      this.anoF,
      this.atual = false,
      this.periodoPorExtenso,
      this.voluntario = false,
      this.descricao,
      this.resultado,
      this.salario});

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'cargo': cargo,
      'empresa': empresa,
      'cidade': cidade,
      'porte': porte,
      'mesI': mesI,
      'mesF': mesF,
      'anoI': anoI,
      'anoF': anoF,
      'atual': atual,
      'periodoPorExtenso': periodoPorExtenso,
      'voluntario': voluntario,
      'descricao': descricao,
      'resultado': resultado,
      'salario': salario,
    };
  }

  SalvarExperienciaModel copyWith({
    id,
    cargo,
    empresa,
    cidade,
    porte,
    mesI,
    mesF,
    anoI,
    anoF,
    atual,
    periodoPorExtenso,
    voluntario,
    descricao,
    resultado,
    salario,
  }) {
    return SalvarExperienciaModel(
      id: id ?? this.id,
      cargo: cargo ?? this.cargo,
      empresa: empresa ?? this.empresa,
      cidade: cidade ?? this.cidade,
      porte: porte ?? this.porte,
      mesI: mesI ?? this.mesI,
      mesF: mesF ?? this.mesF,
      anoI: anoI ?? this.anoI,
      anoF: anoF ?? this.anoF,
      atual: atual ?? this.atual,
      periodoPorExtenso: periodoPorExtenso ?? this.periodoPorExtenso,
      voluntario: voluntario ?? this.voluntario,
      descricao: descricao ?? this.descricao,
      resultado: resultado ?? this.resultado,
      salario: salario ?? this.salario,
    );
  }
}
