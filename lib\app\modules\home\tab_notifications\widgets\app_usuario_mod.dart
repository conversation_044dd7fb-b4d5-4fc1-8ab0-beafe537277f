import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../../shared/core/app_config.dart';
import '../../../../shared/widgets/app_usuario_card/app_usuario_card_controller.dart';
import '../../../curriculum/curriculum_module.dart';
import '../../home_controller.dart';
import '../../home_module.dart';
import '../../tab_profile/utils/behavioral_profile.dart';

class AppUsuarioCardMod extends StatefulWidget {
  final Function? onEdit;

  const AppUsuarioCardMod({super.key, this.onEdit});

  @override
  _AppUsuarioCardModState createState() => _AppUsuarioCardModState();
}

class _AppUsuarioCardModState extends State<AppUsuarioCardMod> {
  final controller = Modular.get<AppUsuarioCardController>();
  final HomeController controllerHome = Modular.get();

  @override
  void initState() {
    super.initState();
    controller.load();
  }

  goToJobs() {
    controllerHome.setTabSelected(1);
    Modular.to.navigate(HomeModule.tabJobs);
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            children: <Widget>[
              Flexible(
                flex: 2,
                child: CardNotifications(
                  title: 'Meu currículo',
                  path: 'lib/assets/images/document-text.png',
                  onTap: () {
                    Modular.to.pushNamed(CurriculumModule.route);
                  },
                  width: MediaQuery.of(context).size.width,
                  rightPosition: -2,
                ),
              ),
              const SizedBox(
                width: 8,
              ),
              Flexible(
                flex: 2,
                child: CardNotifications(
                  title: 'Perfil comportamental',
                  path: 'lib/assets/images/award.png',
                  onTap: () {
                    Modular.to.pushNamed(BehavioralProfile.route);
                  },
                  width: MediaQuery.of(context).size.width,
                  rightPosition: -2,
                ),
              ),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            children: <Widget>[
              Flexible(
                flex: 3,
                child: CardNotifications(
                  title: 'Candidaturas',
                  path: 'lib/assets/images/archive-book.png',
                  onTap: () {
                    Modular.to.pushNamed(HomeModule.tabSubscriptions);
                    _onPressedTabItem('/home/<USER>', 3);
                  },
                  width: MediaQuery.of(context).size.width,
                  rightPosition: -3,
                ),
              ),
              const SizedBox(
                width: 8,
              ),
              Flexible(
                flex: 3,
                child: CardNotifications(
                  title: 'Busca de vagas',
                  path: 'lib/assets/images/document-like.png',
                  onTap: () {
                    Modular.to.pushNamed(HomeModule.tabJobs);
                    _onPressedTabItem('/home/<USER>', 1);
                  },
                  width: MediaQuery.of(context).size.width,
                  rightPosition: -3,
                ),
              ),
              const SizedBox(
                width: 8,
              ),
              Flexible(
                flex: 3,
                child: CardNotifications(
                  title: 'Mensagens',
                  path: 'lib/assets/images/gift.png',
                  onTap: () {
                    Modular.to.pushNamed(HomeModule.tabMessages);
                    _onPressedTabItem('/home/<USER>', 2);
                  },
                  width: MediaQuery.of(context).size.width,
                  rightPosition: -3,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _onPressedTabItem(String? routeName, int? index) {
    Modular.to.navigate(routeName!, arguments: goToJobs);
    controllerHome.setTabSelected(index);
    setState(() => {});
  }
}

class CardNotifications extends StatelessWidget {
  const CardNotifications({
    required this.onTap,
    this.width,
    this.title,
    this.rightPosition,
    this.path,
    super.key,
  });

  final Function() onTap;
  final double? width;
  final String? title;
  final double? rightPosition;
  final String? path;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: AppConfig.colorPrimary,
      borderRadius: BorderRadius.circular(6),
      child: InkWell(
        onTap: onTap,
        child: Stack(
          children: [
            Container(
              width: width,
              height: 95,
              alignment: Alignment.bottomLeft,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  title ?? '',
                  style: const TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w500,
                    fontSize: 12,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
            Positioned(
              top: 0,
              right: rightPosition,
              child: Image.asset(
                path ?? '',
                width: 50,
                height: 50,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
