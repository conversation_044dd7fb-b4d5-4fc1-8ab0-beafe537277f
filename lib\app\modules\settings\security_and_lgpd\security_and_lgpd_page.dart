import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../shared/core/app_config.dart';
import 'pages/logs_lgpd_page.dart';
import 'pages/logs_login_page.dart';

class SecurityAndLGPDPage extends StatelessWidget {
  const SecurityAndLGPDPage({super.key});

  static const route = '/security-and-lgpd';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppConfig.white,
        elevation: 0.5,
        title: const Text(
          'Segurança e LGPD',
          style: TextStyle(color: Colors.black),
        ),
      ),
      body: ListView(
        physics: const BouncingScrollPhysics(),
        children: [
          ListTile(
            onTap: () => Modular.to.pushNamed(
              route + LogsLgpdPage.route,
            ),
            minLeadingWidth: 8,
            leading: Image.asset(
              'lib/assets/icons/logs_lgpd.png',
              scale: 0.7,
            ),
            title: const Text("Logs LGPD"),
          ),
          const Divider(),
          ListTile(
            onTap: () => Modular.to.pushNamed(route + LogsLoginPage.route),
            minLeadingWidth: 8,
            leading: Image.asset(
              'lib/assets/icons/logs_login.png',
              scale: 0.7,
            ),
            title: const Text("Logs de Login"),
          ),
          const Divider(),
        ],
      ),
    );
  }
}
