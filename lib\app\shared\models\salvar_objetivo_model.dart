class SalvarObjetivoModel {
  final List<int?>? objetivoAreas;
  final int? pretensaoSalarial;

  final String? moeda;
  final int? pretensaoSalarialMin;
  final int? pretensaoSalarialMax;

  SalvarObjetivoModel({
    required this.objetivoAreas,
    required this.pretensaoSalarial,
    required this.moeda,
    required this.pretensaoSalarialMin,
    required this.pretensaoSalarialMax,
  });

  Map<String, dynamic> toJson() {
    final result = <String, dynamic>{};

    result.addAll({'objetivoAreas': objetivoAreas});
    result.addAll({'pretensaoSalarial': pretensaoSalarial});

    result.addAll({'moeda': moeda});
    result.addAll({'pretensaoSalarialMin': pretensaoSalarialMin});
    result.addAll({'pretensaoSalarialMax': pretensaoSalarialMax});

    return result;
  }
}
