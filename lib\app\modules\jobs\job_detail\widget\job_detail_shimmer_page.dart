import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

import '../../../../shared/core/app_config.dart';

class JobDetailShimmerPage extends StatelessWidget {
  const JobDetailShimmerPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          Container(
            height: 92,
            width: 92,
            color: AppConfig.white,
          ),
          const Sized<PERSON>ox(
            height: 15,
          ),
          Container(
            color: AppConfig.white,
            height: 25,
            width: 200,
          ),
          Container(
            color: AppConfig.white,
            height: 20,
            width: 100,
          ),
          const SizedBox(
            height: 15,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              Container(
                color: AppConfig.white,
                height: 40,
                width: 100,
              ),
              Container(
                color: AppConfig.white,
                height: 40,
                width: 100,
              ),
            ],
          ),
          const SizedBox(
            height: 15,
          ),
          Container(
            color: AppConfig.white,
            height: 20,
            width: 100,
          ),
          const SizedBox(
            height: 15,
          ),
          Align(
            alignment: Alignment.centerLeft,
            child: Container(
              color: AppConfig.white,
              height: 40,
              width: 150,
            ),
          ),
          const SizedBox(
            height: 15,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              Container(
                color: AppConfig.white,
                height: 40,
                width: 100,
              ),
              Container(
                color: AppConfig.white,
                height: 40,
                width: 100,
              ),
            ],
          ),
          const SizedBox(
            height: 20,
          ),
          Container(
            color: AppConfig.white,
            height: 20,
          ),
          const SizedBox(
            height: 10,
          ),
          Container(
            color: AppConfig.white,
            height: 20,
          ),
          const SizedBox(
            height: 10,
          ),
          Container(
            color: AppConfig.white,
            height: 20,
          ),
          const SizedBox(
            height: 10,
          ),
          Container(
            color: AppConfig.white,
            height: 20,
          ),
        ],
      ),
    );
  }
}
