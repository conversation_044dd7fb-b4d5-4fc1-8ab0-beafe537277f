import 'package:mobx/mobx.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../shared/helpers/timezone_helper.dart';

part 'language_and_timezone_controller.g.dart';

// ignore: library_private_types_in_public_api
class LanguageAndTimezoneController = _LanguageAndTimezoneControllerBase
    with _$LanguageAndTimezoneController;

abstract class _LanguageAndTimezoneControllerBase with Store {
  @observable
  String? selectedLanguage;

  @action
  void changeSelectedLanguage(String? value) {
    selectedLanguage = value;
  }

  @observable
  String? selectedTimezone;

  @action
  Future<void> fetch() async {
    var shared = await SharedPreferences.getInstance();

    selectedLanguage = shared.getString("language") ?? "Português";
    selectedTimezone = shared.getString("timezone") ?? "UTC";
  }

  @action
  void changeSelectedTimezone(String? value) {
    selectedTimezone = value;
  }

  @action
  Future<void> save() async {
    var shared = await SharedPreferences.getInstance();

    await shared.setString("language", selectedLanguage ?? "Português");
    await shared.setString("timezone", selectedTimezone ?? "UTC");

    TimezoneHelper.instance.timezone = selectedTimezone ?? "UTC";
  }
}
