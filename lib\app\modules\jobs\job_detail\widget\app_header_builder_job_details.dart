// ignore_for_file: public_member_api_docs, sort_constructors_first, must_be_immutable
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

import '../../../../shared/utils/time_activity_utils.dart';

class AppHeaderBuildeJobDetails extends StatelessWidget {
  AppHeaderBuildeJobDetails({
    super.key,
    required this.image,
    required this.title,
    required this.company,
    required this.date,
    required this.offset,
  });

  final String image;
  final String title;
  final String company;
  final String date;

  final double offset;
  late double? changeOff;

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: Colors.white,
      child: Column(
        children: [
          const SizedBox(height: 4),
          Container(
            width: 84,
            height: 3,
            decoration: BoxDecoration(
              color: const Color(0xFFE3E3E3),
              borderRadius: BorderRadius.circular(200),
            ),
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 14,
                    horizontal: 24,
                  ),
                  child: Row(
                    children: [
                      AnimatedOpacity(
                        curve: Curves.bounceInOut,
                        opacity: offset <= 0.85 ? 1.0 : 0.0,
                        duration: const Duration(milliseconds: 500),
                        child: CachedNetworkImage(
                          width: offset <= 0.85 ? 65 : 0,
                          height: offset <= 0.85 ? 65 : 0,
                          imageUrl: image,
                          imageBuilder: (context, imageProvider) => Container(
                            decoration: BoxDecoration(
                              image: DecorationImage(
                                image: imageProvider,
                                fit: BoxFit.contain,
                                alignment: Alignment.center,
                              ),
                            ),
                          ),
                          placeholder: (context, url) => SpinKitCircle(
                            color: Colors.blue[300],
                          ),
                          errorWidget: (context, url, error) => const Icon(
                            Icons.broken_image,
                            size: 60,
                          ),
                        ),
                      ),
                      const SizedBox(width: 15),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            TweenAnimationBuilder(
                              tween: Tween<double>(
                                begin: 16,
                                end: offset <= 0.85 ? 15 : 17,
                              ),
                              duration: const Duration(milliseconds: 500),
                              builder: (context, double fontSize, child) {
                                return Text(
                                  title,
                                  maxLines: 2,
                                  style: TextStyle(
                                    color: Colors.black,
                                    fontSize: fontSize,
                                    fontFamily: 'Inter',
                                    fontWeight: FontWeight.w500,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                );
                              },
                            ),
                            const SizedBox(
                              height: 4,
                            ),
                            Text(
                              '$company • há ${TimeActivityUtils.calcularTempoAtiva(date)}',
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              style: const TextStyle(
                                color: Colors.black,
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                                fontFamily: 'Inter',
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 16, right: 16),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () async => Navigator.of(context).pop(),
                    customBorder: const CircleBorder(),
                    splashColor: Colors.grey.withValues(alpha: 0.6),
                    child: Container(
                      padding: const EdgeInsets.all(8.0),
                      decoration: const BoxDecoration(
                        shape: BoxShape.circle,
                      ),
                      child: Image.asset(
                        'lib/assets/images/close.png',
                        scale: 2,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
