name: empregare_app
description: "A new Flutter project. Created by <PERSON><PERSON><PERSON>"
version: 2.2.2+47
publish_to: none

vars:
  clean: fvm flutter clean
  get: fvm flutter pub get
  runner: fvm flutter pub run build_runner

scripts:
  mobx_build: $runner build
  mobx_watch: $runner watch --delete-conflicting-outputs
  mobx_build_clean: $clean & $get & $runner build --delete-conflicting-outputs
  generate_icons: flutter pub run flutter_launcher_icons:main

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  #Firebase
  firebase_core: ^3.6.0
  firebase_messaging: ^15.1.3

  #File
  audioplayers: ^6.1.0
  # image_crop_plus: ^1.0.0
  # Mudar para um package mais confiável ou fazer um fork
  image_crop_plus:
    git:
      url: https://github.com/pokepay/image_crop_extended.git
      ref: flutter-3.29-fixes
    # git:
    #   url: https://github.com/litang0908/image_crop.git
  youtube_player_flutter: ^9.1.1
  video_compress: ^3.0.0
  video_player: ^2.1.6
  chewie: ^1.2.2
  camera: ^0.11.0+2
  image_picker: ^1.0.4
  path_provider: ^2.0.2
  cached_network_image: ^3.0.0
  file_picker: 8.1.0

  #Device
  shared_preferences: any
  get_ip_address: ^0.0.5
  device_info_plus: ^11.1.0
  uuid: ^4.5.1

  #Ads
  google_mobile_ads: ^5.2.0

  #Internationalization
  i18n_extension: ^12.0.1
  intl: ^0.20.2
  mask_text_input_formatter: ^2.7.0
  intl_phone_field: ^3.2.0
  international_phone_input:
    git:
      url: https://github.com/carlosbatistadev/flutter-international-phone-input.git
      ref: new_parameter #Dart 3 incompatible

  #Manage State
  flutter_mobx: ^2.0.6+5
  mobx: ^2.1.4
  flutter_modular: ^6.3.2
  collection: ^1.15.0-nullsafety.4

  flutter:
    sdk: flutter

  flutter_localizations:
    sdk: flutter

  #Html/Web
  dio: ^5.1.1
  webview_flutter: ^4.2.0

  url_launcher: ^6.0.9
  flutter_html: ^3.0.0-alpha.6
  share_plus: ^10.1.1
  w3c_event_source:
    git:
      url: https://github.com/carlosbatistadev/dart-event-source.git
      ref: null-safety #Dart 3 incompatible
  html_editor_enhanced: ^2.6.0

  #Notifications
  flutter_local_notifications: ^17.2.3

  #Calendar
  flutter_holo_date_picker: ^2.0.0
  timeago: ^3.0.2
  add_2_calendar: ^3.0.1

  #Text
  flutter_typeahead: ^5.0.0
  font_awesome_flutter: ^10.4.0
  auto_size_text: ^3.0.0

  #Refresh
  pull_to_refresh: ^2.0.0

  #Dropdown
  multi_dropdown: ^2.0.0

  #Effect
  shimmer: ^3.0.0
  percent_indicator: ^4.2.1
  flutter_spinkit: ^5.0.0
  bottom_sheet: ^4.0.4
  device_preview: ^1.1.0
  timezone: ^0.9.4
  package_info_plus: ^8.1.0
  launch_app_store: ^1.1.2
  logging: ^1.3.0
  update_available: ^3.2.0
  sentry_flutter: ^8.14.1
  app_links: ^6.4.0
  get: ^4.7.2

dependency_overrides:
  intl: ^0.19.0

dev_dependencies:
  flutter_launcher_icons: ^0.14.1
  mobx_codegen: ^2.2.0
  build_runner: ^2.4.15
  flutter_test:
    sdk: flutter
  lints: ^4.0.0
  flutter_lints: ^4.0.0

# dependency_overrides:
#   firebase_messaging_platform_interface: 3.1.6
#   firebase_crashlytics_platform_interface: 3.1.6

flutter_icons:
  image_path_android: "lib/assets/icons/icon_android.png"
  image_path_ios: "lib/assets/icons/icon_ios.jpg"
  android: true # can specify file name here e.g. "ic_launcher"
  ios: true # can specify file name here e.g. "My-Launcher-Icon"

flutter:
  uses-material-design: true

  assets:
    - lib/assets/images/
    - lib/assets/icons/
    - lib/assets/icons/profile/
    - lib/assets/icons/privacity/

  fonts:
    - family: Roboto
      fonts:
        - asset: lib/assets/fonts/Roboto-Regular.ttf
        - asset: lib/assets/fonts/Roboto-Medium.ttf
    - family: Inter
      fonts:
        - asset: lib/assets/fonts/Inter-Light.ttf
          weight: 300
        - asset: lib/assets/fonts/Inter-Regular.ttf
          weight: 400
        - asset: lib/assets/fonts/Inter-Medium.ttf
          weight: 500
        - asset: lib/assets/fonts/Inter-Bold.ttf
          weight: 700
        - asset: lib/assets/fonts/Inter-Black.ttf
        - asset: lib/assets/fonts/Inter-ExtraBold.ttf
        - asset: lib/assets/fonts/Inter-ExtraLight.ttf
        - asset: lib/assets/fonts/Inter-SemiBold.ttf
        - asset: lib/assets/fonts/Inter-Thin.ttf
