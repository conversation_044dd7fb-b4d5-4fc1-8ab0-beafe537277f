import 'package:shared_preferences/shared_preferences.dart';
import 'package:timezone/standalone.dart' as tz;

class TimezoneHelper {
  TimezoneHelper._();

  static TimezoneHelper instance = TimezoneHelper._();

  String timezone = "UTC";
  Future<void> getTimezone() async {
    var shared = await SharedPreferences.getInstance();

    timezone = shared.getString("timezone") ?? "UTC";
  }

  DateTime now() {
    return tz.TZDateTime.now(tz.getLocation(timezone));
  }
}
