import 'package:flutter/material.dart';

import '../../../shared/app_container.dart';
import '../../../shared/core/app_translation.i18n.dart';
import '../questionnaire_controller.dart';

class PerguntaTextoCurta extends StatefulWidget {
  const PerguntaTextoCurta({
    super.key,
    required this.controller,
  });

  final QuestionnaireController controller;

  @override
  State<PerguntaTextoCurta> createState() => _PerguntaTextoCurtaState();
}

class _PerguntaTextoCurtaState extends State<PerguntaTextoCurta> {
  @override
  Widget build(BuildContext context) {
    return AppContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'RESPOSTA:'.i18n,
            textAlign: TextAlign.left,
          ),
          TextFormField(
            style: const TextStyle(letterSpacing: 1),
            controller: widget.controller.pergunta?.controller,
            keyboardType: TextInputType.multiline,
            minLines: 1,
            maxLines: 8,
            textAlign: TextAlign.start,
            decoration: InputDecoration(
              hintText: 'Digite sua resposta aqui...'.i18n,
              isDense: true,
            ),
            onChanged: (String? value) {
              widget.controller.setConteudo(value ?? '');
              setState(() {});
            },
          )
        ],
      ),
    );
  }
}
