import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:url_launcher/url_launcher_string.dart';
import 'package:video_player/video_player.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

import '../../modules/weblink/weblink_module.dart';
import 'app_audio_player.dart';
import 'app_image_network_widget.dart';

class AppHtml extends StatefulWidget {
  final String? data;
  final double fontSize;

  const AppHtml({super.key, this.data, this.fontSize = 13});

  @override
  _AppHtmlState createState() => _AppHtmlState();
}

class _AppHtmlState extends State<AppHtml> {
  VideoPlayerController? _audioPlayer;
  YoutubePlayer? _youtubePlayer;

  @override
  void dispose() {
    super.dispose();
    _audioPlayer?.dispose();
  }

  YoutubePlayer getYoutubePlayer(String src) {
    if (_youtubePlayer == null) {
      return _youtubePlayer = YoutubePlayer(
        controller: YoutubePlayerController(
          initialVideoId: YoutubePlayer.convertUrlToId(
            src.replaceAll('?feature=oembed', ''),
          )!,
        ),
      );
    }
    return _youtubePlayer!;
  }

  @override
  Widget build(BuildContext context) {
    return Html(
      data: widget.data!.replaceAll(
        "<iframe width=\"200\" height=\"113\"",
        "<iframe height=\"200\"",
      ),
      style: {
        "html": Style.fromTextStyle(
          TextStyle(
            fontFamily: 'Roboto',
            fontSize: widget.fontSize,
          ),
        ).merge(
          Style(
            textAlign: TextAlign.start,
          ),
        ),
      },
      shrinkWrap: true,
      onLinkTap: (url, attributes, element) {
        if (element != null) {
          launchUrlString(element.attributes['href']!);
        }
      },
      extensions: [
        TagExtension(
          tagsToExtend: {'audio'},
          builder: (extensionContext) {
            return AppAudioPlayer(
              url: extensionContext.attributes['src'],
            );
          },
        ),
        TagExtension(
          tagsToExtend: {'img'},
          builder: (extensionContext) {
            return ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: AppImageNetworkWidget(
                extensionContext.attributes['src']!,
                fit: BoxFit.contain,
                height: 135,
                alignment: Alignment.center,
              ),
            );
          },
        ),
        TagExtension(
          tagsToExtend: {'a'},
          builder: (extensionContext) {
            return InkWell(
              child: Text(
                extensionContext.innerHtml,
                style: const TextStyle(
                  color: Colors.blue,
                  decoration: TextDecoration.underline,
                ),
              ),
              onTap: () {
                Modular.to.pushNamed(WeblinkModule.route, arguments: {
                  'link': extensionContext.attributes['href'],
                  'title': 'Empregare'
                });
              },
            );
          },
        ),
        TagExtension(
          tagsToExtend: {'iframe'},
          builder: (extensionContext) {
            return YoutubePlayerBuilder(
              player: getYoutubePlayer(extensionContext.attributes['src']!),
              builder: (context, widget) {
                return widget;
              },
            );
          },
        )
      ],
    );
  }
}
