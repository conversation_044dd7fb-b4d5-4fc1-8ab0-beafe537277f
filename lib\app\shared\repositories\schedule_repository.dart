import 'package:dio/dio.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../core/app_rest.dart';
import '../models/agenda_salvar_model.dart';
import '../models/responses/agenda_response_model.dart';
import '../models/responses/inscricao_response_model.dart';

class ScheduleRepository {
  final AppRest _rest = Modular.get();

  Future<AgendaResponseModel> getCompromisso({required String? token}) async {
    try {
      final response = await _rest.get('/candidato/agenda/detalhes/$token');
      return AgendaResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return AgendaResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return AgendaResponseModel.fromJson(err.response!.data);
    }
  }

  Future<InscricaoResponseModel> postCompromisso(
      AgendaSalvarModel model) async {
    try {
      final response =
          await _rest.post('/candidato/agenda/salvar', data: model.toJson());
      return InscricaoResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return InscricaoResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return InscricaoResponseModel.fromJson(err.response!.data);
    }
  }
}
