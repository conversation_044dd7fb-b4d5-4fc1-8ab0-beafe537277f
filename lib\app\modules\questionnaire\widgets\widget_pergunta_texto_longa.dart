import 'package:flutter/material.dart';
import 'package:html_editor_enhanced/html_editor.dart';

import '../../../shared/app_container.dart';
import '../questionnaire_controller.dart';

class PerguntaTextoLonga extends StatefulWidget {
  const PerguntaTextoLonga({
    super.key,
    required this.context,
    required this.controller,
  });

  final BuildContext context;
  final QuestionnaireController controller;

  @override
  State<PerguntaTextoLonga> createState() => _PerguntaTextoLongaState();
}

class _PerguntaTextoLongaState extends State<PerguntaTextoLonga> {
  @override
  Widget build(BuildContext context) {
    final HtmlEditorController htmlController = HtmlEditorController();

    return AppContainer(
      height: MediaQuery.of(context).size.height * 0.55,
      child: HtmlEditor(
        controller: htmlController,
        htmlEditorOptions: const HtmlEditorOptions(
          hint: "Digite sua resposta aqui...",
        ),
        callbacks: Callbacks(
          onChangeContent: (String? value) {
            widget.controller.setConteudo(value ?? '');
          },
        ),
        htmlToolbarOptions: const HtmlToolbarOptions(
          defaultToolbarButtons: [
            FontButtons(
              underline: false,
              clearAll: false,
              subscript: false,
              superscript: false,
            ),
            ColorButtons(
              foregroundColor: false,
              highlightColor: false,
            ),
            ParagraphButtons(
              caseConverter: false,
              decreaseIndent: false,
              increaseIndent: false,
              lineHeight: false,
              textDirection: false,
            ),
            ListButtons(listStyles: false)
          ],
        ),
        otherOptions: OtherOptions(
          height: MediaQuery.of(context).size.height * 0.4,
        ),
      ),
    );
  }
}
