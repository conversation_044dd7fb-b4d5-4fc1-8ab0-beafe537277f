class ComplementarResponseModel {
  final bool sucesso;
  final String mensagem;
  final List<ComplementarModel>? dados;

  ComplementarResponseModel({
    this.sucesso = false,
    this.mensagem = "",
    this.dados,
  });

  factory ComplementarResponseModel.fromJson(Map<String, dynamic> json) =>
      ComplementarResponseModel(
        sucesso: json['sucesso'] ?? false,
        mensagem: json['mensagem'] ?? "",
        dados: (json['dados'] as List<dynamic>?)
            ?.map((e) => ComplementarModel.fromJson(e as Map<String, dynamic>))
            .toList(),
      );
}

class ComplementarModel {
  static const int mudanca = 1;
  static const int viajar = 2;
  static const int habA = 3;
  static const int habB = 4;
  static const int habC = 5;
  static const int habD = 6;
  static const int habE = 7;
  static const int veiculoCarro = 8;
  static const int veiculoMoto = 9;
  static const int veiculoBicicleta = 10;
  static const int veiculoCaminhao = 11;
  final int? id;
  final String? texto;

  ComplementarModel({this.id, this.texto});

  factory ComplementarModel.fromJson(Map<String, dynamic> json) =>
      ComplementarModel(
        id: json['id'] as int?,
        texto: json['texto'] as String?,
      );

  String get textoSeguro => texto ?? '';

  @override
  String toString() => textoSeguro;

  @override
  operator ==(other) => other is ComplementarModel && other.id == id;

  @override
  int get hashCode => id.hashCode ^ textoSeguro.hashCode;
}
