import 'dart:async';

import 'package:flutter/material.dart';

import '../core/app_config.dart';

class AppDefaultButton extends StatefulWidget {
  final Color? color;
  final Color? disabledColor;
  final Widget title;
  final double height;
  final double width;
  final bool withBorder;
  final FutureOr<void> Function()? onPressed;
  final double radius;

  const AppDefaultButton({
    super.key,
    this.color,
    required this.title,
    required this.onPressed,
    this.height = 45.0,
    this.width = double.infinity,
    this.disabledColor,
    this.withBorder = false,
    this.radius = 6.0,
  });

  @override
  State<AppDefaultButton> createState() => _AppDefaultButtonState();
}

class _AppDefaultButtonState extends State<AppDefaultButton> {
  bool isLoading = false;
  void changeIsLoading(bool value) {
    isLoading = value;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: widget.height,
      width: widget.width,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          foregroundColor:
              widget.withBorder ? AppConfig.colorPrimary : AppConfig.white,
          backgroundColor: widget.withBorder
              ? Colors.transparent
              : widget.color ?? AppConfig.colorPrimary,
          disabledBackgroundColor: widget.disabledColor ?? AppConfig.grey,
          shadowColor: Colors.transparent,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(widget.radius),
            side: widget.withBorder
                ? BorderSide(
                    color: widget.onPressed == null
                        ? Colors.grey
                        : widget.color ?? AppConfig.colorPrimary,
                    width: 1.6,
                  )
                : BorderSide.none,
          ),
        ),
        onPressed: widget.onPressed == null || isLoading
            ? null
            : () async {
                changeIsLoading(true);
                await widget.onPressed!();
                changeIsLoading(false);
              },
        child: isLoading
            ? const SizedBox.square(
                dimension: 20,
                child: CircularProgressIndicator(
                  color: AppConfig.colorPrimary,
                  backgroundColor: AppConfig.grey,
                ),
              )
            : widget.title,
      ),
    );
  }
}
