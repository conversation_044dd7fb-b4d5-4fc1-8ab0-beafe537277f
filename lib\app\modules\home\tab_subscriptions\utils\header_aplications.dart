import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../home_controller.dart';
import '../../home_module.dart';
import 'card_default.dart';

class HeaderApplications extends StatelessWidget {
  const HeaderApplications({
    super.key,
    required this.showMinhasVagas,
  });

  final bool showMinhasVagas;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: MediaQuery.of(context).size.width,
      child: DecoratedBox(
        decoration: const BoxDecoration(color: Color(0xFFF5F8FA)),
        child: Padding(
          padding: const EdgeInsets.only(left: 24.0, top: 16, bottom: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AnimatedCrossFade(
                duration: const Duration(milliseconds: 250),
                sizeCurve: Curves.fastEaseInToSlowEaseOut,
                firstChild: const Text(
                  'Vagas recentes',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 32,
                    fontFamily: 'Inter',
                  ),
                ),
                secondChild: const Text(
                  'Suas candidaturas',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: 32,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                crossFadeState: showMinhasVagas
                    ? CrossFadeState.showSecond
                    : CrossFadeState.showFirst,
              ),
              const SizedBox(height: 5),
              AnimatedCrossFade(
                duration: const Duration(milliseconds: 250),
                sizeCurve: Curves.fastEaseInToSlowEaseOut,
                firstChild: const Text(
                  'Descubra as oportunidades mais recentes',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                secondChild: const Text(
                  'Confira como está seu processo seletivo.',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                crossFadeState: showMinhasVagas
                    ? CrossFadeState.showSecond
                    : CrossFadeState.showFirst,
              ),
              const SizedBox(height: 5),
              Row(
                children: [
                  GestureDetector(
                    onTap: () {
                      Modular.to.navigate(
                        HomeModule.tabJobs,
                      );
                      Modular.get<HomeController>().setTabSelected(1);
                    },
                    child: CardDefault(
                      title: 'Vagas',
                      color: !showMinhasVagas
                          ? const Color(0xFF1B1B63)
                          : Colors.white,
                      colorText: !showMinhasVagas
                          ? Colors.white
                          : const Color(0xFF666666),
                      borderColor: !showMinhasVagas
                          ? const Color(0xFF1B1B63)
                          : const Color(0xFF666666),
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      Modular.to.navigate(
                        HomeModule.tabSubscriptions,
                      );
                      Modular.get<HomeController>().setTabSelected(3);
                    },
                    child: CardDefault(
                      title: 'Minhas vagas',
                      color: showMinhasVagas == true
                          ? const Color(0xFF1B1B63)
                          : Colors.white,
                      colorText: showMinhasVagas == true
                          ? Colors.white
                          : const Color(0xFF666666),
                      borderColor: showMinhasVagas
                          ? const Color(0xFF1B1B63)
                          : const Color(0xFF666666),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
