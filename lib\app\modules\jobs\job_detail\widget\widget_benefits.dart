import 'package:flutter/material.dart';

import '../../../../shared/core/app_config.dart';
import '../../../../shared/core/app_utils.dart';
import '../../../../shared/widgets/app_badge.dart';
import '../job_detail_controller.dart';

class Benefits extends StatelessWidget {
  const Benefits({
    super.key,
    required this.controller,
  });

  final JobDetailController controller;
  @override
  Widget build(BuildContext context) {
    List<String> beneficios =
        controller.jobDetails?.beneficio?.split(',') ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        const Text(
          'Benefícios',
          style: TextStyle(
            fontSize: 14,
            color: AppConfig.colorPrimary,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 20),
        Padding(
          padding: const EdgeInsets.only(bottom: 24),
          child: Wrap(
            children: beneficios.map((b) {
              return AppBadge(title: removeAllHtmlTags(b));
            }).toList(),
          ),
        )
      ],
    );
  }
}
