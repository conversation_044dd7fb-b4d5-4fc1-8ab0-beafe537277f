import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

import '../../../shared/core/app_config.dart';
import '../../../shared/core/app_translation.i18n.dart';
import '../../../shared/core/app_utils.dart';
import '../../../shared/widgets/app_default_button.dart';
import '../curriculum_module.dart';
import 'curriculum_help_controller.dart';

class CurriculumHelpPage extends StatelessWidget {
  const CurriculumHelpPage({super.key});

  CurriculumHelpController get controller => Modular.get();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: AppUtils.instance!.scaffoldKey,
      backgroundColor: AppConfig.grey,
      body: Container(
        padding: const EdgeInsets.all(15),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              'lib/assets/images/upload-document.png',
              width: 60,
            ),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 40, vertical: 10),
              child: Text(
                'Vamos te ajudar a preencher o seu currículo',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 20,
                  color: AppConfig.colorPrimary,
                ),
              ),
            ),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 35),
              child: Text(
                "Envie o seu currículo em Word, PDF ou tire uma foto para a nossa inteligência artificial ajudar a preencher o seu cadastro",
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
            ),
            const SizedBox(height: 50),
            const Padding(
              padding: EdgeInsets.symmetric(
                horizontal: 35,
                vertical: 15,
              ),
              child: Text(
                'Arquivos permitidos: docx, pdf ou jpg',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
              ),
            ),
            AppDefaultButton(
              onPressed: () {
                controller.selectCurriculum(
                  (mensagem) => AppUtils.instance!.showSnackBar(
                    context,
                    message: mensagem,
                    color: Colors.red,
                  ),
                );
              },
              title: ListenableBuilder(
                listenable: controller,
                builder: (context, child) {
                  if (controller.loading) {
                    return const SpinKitCircle(color: AppConfig.white);
                  }

                  return Text(
                    'SELECIONAR CURRÍCULO'.i18n,
                    style: const TextStyle(
                      color: AppConfig.white,
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 20),
            InkWell(
              onTap: () {
                Modular.to.pushReplacementNamed(
                  CurriculumModule.route,
                  arguments: true,
                );
              },
              child: const Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: 10,
                  vertical: 10,
                ),
                child: Text(
                  'PREENCHER MANUALMENTE',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 12,
                    color: AppConfig.colorPrimary,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
