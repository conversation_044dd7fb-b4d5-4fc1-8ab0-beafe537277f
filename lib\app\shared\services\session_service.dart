import 'dart:async';

import '../models/session_model.dart';
import 'storage_service.dart';

const sessionKey = '__SESSION__';

class SessionService {
  final StorageService storageService;

  SessionService(this.storageService);

  Future<bool> isLoggedIn() async {
    try {
      var session = await storageService.get<SessionModel>(sessionKey,
          construct: (json) => SessionModel.fromJson(json));
      return session != null;
    } catch (e) {
      return false;
    }
  }

  Future<void> logoff() async {
    await storageService.removeAll();
  }

  Future<bool> set(SessionModel session) async {
    await storageService.put(sessionKey, session.toJson());
    return true;
  }

  Future<SessionModel?> get() async {
    return await (storageService.get(sessionKey,
        construct: (value) => SessionModel.fromJson(value)));
  }
}
