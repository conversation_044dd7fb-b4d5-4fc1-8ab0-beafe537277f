import 'package:flutter_modular/flutter_modular.dart';
import 'package:mobx/mobx.dart';

import '../../shared/models/responses/dash_notificacoes_response_model.dart';
import '../../shared/repositories/dashboard_repository.dart';

part 'notification_controller.g.dart';

class NotificationController = _NotificationControllerBase
    with _$NotificationController;

abstract class _NotificationControllerBase with Store {
  final DashboardRepository _dashboardRepository = Modular.get();

  @observable
  bool loading = false;

  @observable
  List<QuestionarioModel> _questionarios = <QuestionarioModel>[].asObservable();
  List<QuestionarioModel> get questionarios => _questionarios;

  @observable
  List<AgendaModel> _agendas = <AgendaModel>[].asObservable();
  List<AgendaModel> get agendas => _agendas;

  _NotificationControllerBase() {
    init();
  }

  @action
  Future init() async {
    try {
      loading = true;
      DashNotificacoesResponseModel responseModel =
          await _dashboardRepository.getNotificacoes();
      _questionarios = responseModel.questionarios!.asObservable();
      _agendas = responseModel.agenda!.asObservable();
    } finally {
      loading = false;
    }
  }
}
