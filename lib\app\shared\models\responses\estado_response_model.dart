class EstadoResponseModel {
  final bool sucesso;
  final String mensagem;
  final List<EstadoModel>? dados;

  EstadoResponseModel({
    this.sucesso = false,
    this.mensagem = "",
    this.dados,
  });

  factory EstadoResponseModel.fromJson(Map<String, dynamic> json) {
    return EstadoResponseModel(
      sucesso: json['sucesso'] ?? false,
      mensagem: json['mensagem'] ?? "",
      dados: (json['dados'] as List<dynamic>?)
          ?.map((e) => EstadoModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class EstadoModel {
  final int? id;
  final String? nome;

  EstadoModel({this.id, this.nome});

  factory EstadoModel.fromJson(Map<String, dynamic> json) {
    return EstadoModel(
      id: json['id'] as int?,
      nome: json['nome'] as String?,
    );
  }

  @override
  String toString() {
    return nome!;
  }
}
