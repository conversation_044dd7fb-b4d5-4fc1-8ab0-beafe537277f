import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class AppImageNetworkWidget extends StatelessWidget {
  final String? urlImage;
  final String? errorImage;
  final double? height;
  final double? width;
  final BoxFit? fit;
  final double? scale;
  final Animation<double>? opacity;
  final FilterQuality filterQuality;
  final Alignment alignment;

  const AppImageNetworkWidget(
    this.urlImage, {
    super.key,
    this.height,
    this.width,
    this.errorImage,
    this.fit,
    this.opacity,
    this.scale,
    this.alignment = Alignment.center,
    this.filterQuality = FilterQuality.low,
  });

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: const BoxDecoration(
          // color: Colors.black38
          ),
      child: CachedNetworkImage(
        imageUrl: urlImage ?? '',
        height: height,
        width: width,
        fit: fit,
        alignment: alignment,
        imageBuilder: (context, imageProvider) => Image(
          image: imageProvider,
          height: height,
          width: width,
          fit: fit,
          opacity: opacity,
          filterQuality: filterQuality,
        ),
        placeholder: (context, url) => const Center(
          child: SizedBox.square(
            dimension: 20,
            child: CircularProgressIndicator(),
          ),
        ),
        errorWidget: (context, url, error) {
          if (errorImage != null && errorImage!.isNotEmpty) {
            return Image.asset(
              errorImage ?? '',
              height: height,
              width: width,
              fit: fit,
              scale: scale ?? 4,
            );
          } else {
            return Icon(
              Icons.error,
              size: height ?? 50,
            );
          }
        },
      ),
    );
  }
}
