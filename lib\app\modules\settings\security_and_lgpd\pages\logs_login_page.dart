import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../security_and_lgpd_controller.dart';

class LogsLoginPage extends StatelessWidget {
  const LogsLoginPage({super.key});

  static const route = '/logs-login';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Logs de Login')),
      body: Observer(
        builder: (context) {
          var control = Modular.get<SecurityAndLGPDController>();

          if (control.logsLogin.isEmpty) {
            return const Center(
              child: Text("Nada para mostrar"),
            );
          }

          return ListView.builder(
            itemCount: control.logsLogin.length,
            itemBuilder: (context, index) {
              return const LogTile(
                title: "Termos",
                date: "12/13/123 13:12",
                browser: "site",
                ip: 'asd',
                message: 'asdasd',
              );
            },
          );
        },
      ),
    );
  }
}

class LogTile extends StatelessWidget {
  const LogTile({
    super.key,
    required this.title,
    required this.date,
    required this.browser,
    required this.ip,
    required this.message,
  });

  final String title;
  final String date;
  final String browser;
  final String ip;
  final String message;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ListTile(
          isThreeLine: true,
          title: Text(title),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 8),
              Text(date),
              const SizedBox(height: 5),
              Text(browser),
              const SizedBox(height: 5),
              Text(ip),
              const SizedBox(height: 5),
              Text(message),
            ],
          ),
        ),
        const Divider(),
      ],
    );
  }
}
