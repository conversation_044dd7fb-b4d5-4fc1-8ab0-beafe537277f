import 'package:dio/dio.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../core/app_rest.dart';
import '../models/responses/listar_areas_response_model.dart';

class PositionsRepository {
  final AppRest _rest = Modular.get();

  Future<AreasResponseModel> getAreasInteresse() async {
    try {
      final response = await _rest.get('/variaveis/areas/listar');
      return AreasResponseModel.fromJson({"data": response.data});
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return AreasResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return AreasResponseModel.fromJson(err.response!.data);
    }
  }
}
