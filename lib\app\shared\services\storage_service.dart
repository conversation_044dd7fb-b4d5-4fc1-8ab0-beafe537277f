import 'dart:async';
import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';

class StorageService {
  Completer<SharedPreferences> instance = Completer<SharedPreferences>();

  StorageService() {
    _initStorage();
  }

  _initStorage() async {
    SharedPreferences share = await SharedPreferences.getInstance();
    if (!instance.isCompleted) instance.complete(share);
  }

  Future<bool> put(String key, dynamic json) async {
    try {
      SharedPreferences share = await instance.future;
      share.setString(key, jsonEncode(json));
      return true;
    } catch (e) {
      return false;
    }
  }

  Future get<S>(String key, {S Function(dynamic)? construct}) async {
    try {
      SharedPreferences share = await instance.future;
      String? value = share.getString(key);

      if (value == null || value.isEmpty) {
        return null;
      }

      dynamic json = jsonDecode(value);
      if (construct == null) {
        return json;
      } else {
        return construct(json);
      }
    } catch (e) {
      return null;
    }
  }

  Future remove(String key) async {
    SharedPreferences share = await instance.future;
    return share.remove(key);
  }

  Future removeAll() async {
    SharedPreferences share = await instance.future;
    return share.clear();
  }
}
