import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../../../shared/core/app_config.dart';
import '../../../../shared/models/responses/pessoa_response_model.dart';
import '../login_controller.dart';

class ShowPeopleDialog extends StatelessWidget {
  const ShowPeopleDialog({super.key, required this.people, required this.onLogin});

  final List<PessoaResponseModel> people;
  final Future<void> Function(String? personId) onLogin;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: <Widget>[
        SimpleDialog(
          contentPadding: EdgeInsets.zero,
          titlePadding: EdgeInsets.zero,
          title: Container(
            width: MediaQuery.of(context).size.width,
            padding: const EdgeInsets.all(10),
            child: Text(
              'Selecione o currículo que você quer acessar'.i18n,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          children: List.generate(people.length, (index) {
            final p = people[index];

            return Column(
              children: <Widget>[
                Container(height: 1, color: AppConfig.grey),
                ListTile(
                  dense: true,
                  title: Text(p.nome ?? '?', style: const TextStyle(fontSize: 16)),
                  onTap: () => onLogin(p.id),
                ),
              ],
            );
          }),
        ),
        GetBuilder<LoginController>(
          builder: (controller) {
            return Visibility(
              visible: controller.loading,
              child: Container(
                color: Colors.white70,
                child: const Center(
                  child: SpinKitCircle(color: AppConfig.colorPrimary),
                ),
              ),
            );
          },
        ),
      ],
    );
  }
}
