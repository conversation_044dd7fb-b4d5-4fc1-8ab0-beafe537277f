import 'package:add_2_calendar/add_2_calendar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:shimmer/shimmer.dart';
import 'package:url_launcher/url_launcher_string.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../shared/app_container.dart';
import '../../shared/core/app_config.dart';
import '../../shared/core/app_translation.i18n.dart';
import '../../shared/core/app_utils.dart';
import '../../shared/widgets/app_bar_default.dart';
import '../../shared/widgets/app_default_button.dart';
import 'schedule_controller.dart';

class SchedulePage extends StatefulWidget {
  final String? token;

  const SchedulePage({super.key, required this.token});

  @override
  _SchedulePageState createState() => _SchedulePageState();
}

class _SchedulePageState extends State<SchedulePage> {
  final controller = Modular.get<ScheduleController>();

  String? webviewUrl;

  @override
  void initState() {
    super.initState();
    controller.load(widget.token);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConfig.grey,
      appBar: AppBarDefault(
        titleText: 'Dados do Compromisso'.i18n,
      ),
      body: Observer(
        builder: (_) {
          if (controller.loading) {
            return Container(
              color: Colors.white,
              child: _shimmer(),
            );
          }

          return Visibility(
            visible: webviewUrl != null,
            replacement: SingleChildScrollView(
              padding: const EdgeInsets.only(top: 15),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AppContainer(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Text(
                          controller.compromisso?.agenda?.titulo ?? '',
                          textAlign: TextAlign.center,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Row(
                          children: <Widget>[
                            const Icon(
                              Icons.calendar_today,
                              color: Colors.black54,
                            ),
                            const SizedBox(
                              width: 5,
                            ),
                            Expanded(
                              child: Text(
                                removeAllHtmlTags(
                                  controller.compromisso?.agenda?.dataCompleta,
                                ),
                                textAlign: TextAlign.center,
                                style: const TextStyle(
                                  fontSize: 16,
                                  color: Colors.black87,
                                ),
                              ),
                            )
                          ],
                        ),
                        const SizedBox(height: 15),
                        AppDefaultButton(
                          color: AppConfig.green,
                          onPressed: () {
                            controller.setMensagem('');
                            _showConfirmarDialog(context);
                          },
                          title: Text(
                            'CONFIRMAR PRESENÇA'.i18n,
                            style: const TextStyle(
                              fontSize: 16,
                              color: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(
                          height: 12,
                        ),
                        AppDefaultButton(
                          color: AppConfig.red,
                          onPressed: () {
                            controller.setMensagem('');
                            _showRecusarDialog(context);
                          },
                          title: Text(
                            'RECUSAR PRESENÇA'.i18n,
                            style: const TextStyle(
                              fontSize: 16,
                              color: Colors.white,
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  AppContainer(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Visibility(
                            visible: (controller.compromisso?.sala?.salaVirtual
                                    ?.isNotEmpty ??
                                false),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                                Text(
                                  'SALA VIRTUAL'.i18n,
                                  style: const TextStyle(color: Colors.black54),
                                ),
                                const SizedBox(height: 15),
                                AppDefaultButton(
                                  onPressed: () {
                                    launchUrlString(
                                      Uri.encodeFull(
                                        controller.compromisso?.sala
                                                ?.salaVirtual ??
                                            '',
                                      ),
                                    );
                                  },
                                  title: Text(
                                    'ACESSAR SALA VIRTUAL'.i18n,
                                    style: const TextStyle(
                                      fontSize: 16,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ],
                            )),
                        Visibility(
                          visible: (controller
                                  .compromisso?.sala?.salaVirtual?.isEmpty ??
                              true),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              Text(
                                'ENDEREÇO'.i18n,
                                style: const TextStyle(
                                  color: Colors.black54,
                                ),
                              ),
                              const SizedBox(height: 10),
                              Text(
                                controller.compromisso?.sala?.endereco ?? '',
                              ),
                              const SizedBox(
                                height: 10,
                              ),
                              Text(
                                'SALA'.i18n,
                                style: const TextStyle(
                                  color: Colors.black54,
                                ),
                              ),
                              const SizedBox(height: 10),
                              Text(
                                controller.compromisso?.sala?.titulo ?? '',
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 30),
                        InkWell(
                          child: Row(
                            children: [
                              const Icon(Icons.add,
                                  color: AppConfig.colorPrimary),
                              const SizedBox(
                                width: 10,
                              ),
                              Text(
                                'ADICIONAR COMPROMISSO NA AGENDA'.i18n,
                                style: const TextStyle(
                                  color: AppConfig.colorPrimary,
                                ),
                              ),
                            ],
                          ),
                          onTap: () {
                            final Event event = Event(
                              title: controller.compromisso!.agenda!.titulo!,
                              description:
                                  controller.compromisso!.agenda!.dataCompleta!,
                              location: controller.compromisso!.sala!.endereco!,
                              startDate: DateTime.parse(
                                  controller.compromisso!.agenda!.dataInicial!),
                              endDate: DateTime.parse(
                                  controller.compromisso!.agenda!.dataFinal!),
                            );

                            Add2Calendar.addEvent2Cal(event);
                          },
                        ),
                        const SizedBox(
                          height: 12,
                        ),
                        const Row(
                          children: <Widget>[
                            Expanded(
                              child: Text(
                                'MODALIDADE',
                                style: TextStyle(color: Colors.black54),
                              ),
                            ),
                            Expanded(
                              child: Text(
                                'TIPO DE AGENDAMENTO',
                                style: TextStyle(
                                  color: Colors.black54,
                                ),
                              ),
                            )
                          ],
                        ),
                        Row(
                          children: <Widget>[
                            Expanded(
                              child: Text(controller
                                  .compromisso!.agenda!.modalidadeTexto!),
                            ),
                            Expanded(
                              child: Text(controller
                                  .compromisso!.agenda!.tipoAgendamento!),
                            )
                          ],
                        ),
                        const SizedBox(
                          height: 12,
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            const Text(
                              'RESPONSÁVEIS',
                              style: TextStyle(color: Colors.black54),
                            ),
                            Text(controller.compromisso!.responsaveis!
                                .map((e) => e.nome)
                                .join(', '))
                          ],
                        ),
                        const SizedBox(
                          height: 12,
                        ),
                        Visibility(
                          visible: controller.compromisso?.agenda?.orientacao !=
                              null,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              const Text(
                                'ORIENTAÇÕES',
                                style: TextStyle(
                                  color: Colors.black54,
                                ),
                              ),
                              Text(
                                removeAllHtmlTags(
                                  controller.compromisso?.agenda?.orientacao,
                                ),
                              )
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                ],
              ),
            ),
            child: WebViewWidget(
              controller: WebViewController()
                ..loadRequest(Uri.parse(webviewUrl!)),
            ),
          );
        },
      ),
    );
  }

  void _showConfirmarDialog(BuildContext context) {
    showDialog(
        context: context,
        builder: (_) {
          return SimpleDialog(
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
            titlePadding: EdgeInsets.zero,
            title: SizedBox(
              width: MediaQuery.of(context).size.width,
              child: Column(
                children: [
                  Align(
                    alignment: Alignment.topRight,
                    child: IconButton(
                      icon: const Icon(
                        Icons.close,
                        color: Colors.grey,
                      ),
                      onPressed: () {
                        Modular.to.pop();
                      },
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 15),
                    child: Text(
                      'Você deseja realmente aceitar esse convite?'.i18n,
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        color: AppConfig.green,
                        fontSize: 22,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  )
                ],
              ),
            ),
            children: [
              TextFormField(
                controller: TextEditingController(text: controller.mensagem),
                decoration: InputDecoration(
                    labelText: 'MENSAGEM PARA O RESPONSÁVEL (OPCIONAL)'.i18n,
                    labelStyle: const TextStyle(fontSize: 12)),
                minLines: 3,
                maxLines: 4,
                onChanged: controller.setMensagem,
                textInputAction: TextInputAction.done,
              ),
              const SizedBox(
                height: 40,
              ),
              AppDefaultButton(
                title: Observer(
                  builder: (_) => controller.confirmando
                      ? const SpinKitCircle(color: AppConfig.white)
                      : Text(
                          'SIM, ACEITAR PRESENÇA'.i18n,
                          style: const TextStyle(
                            fontSize: 16,
                            color: Colors.white,
                          ),
                        ),
                ),
                onPressed: () {
                  controller.confirmar();
                },
                color: AppConfig.green,
              ),
            ],
          );
        });
  }

  void _showRecusarDialog(BuildContext context) {
    showDialog(
        context: context,
        builder: (_) {
          return SimpleDialog(
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
            titlePadding: EdgeInsets.zero,
            title: SizedBox(
                width: MediaQuery.of(context).size.width,
                child: Column(
                  children: [
                    Align(
                      alignment: Alignment.topRight,
                      child: IconButton(
                        icon: const Icon(
                          Icons.close,
                          color: Colors.grey,
                        ),
                        onPressed: () {
                          Modular.to.pop();
                        },
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 15),
                      child: Text(
                        'Você deseja realmente recusar esse convite?'.i18n,
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          color: AppConfig.red,
                          fontSize: 22,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    )
                  ],
                )),
            children: [
              TextFormField(
                controller: TextEditingController(text: controller.mensagem),
                textInputAction: TextInputAction.done,
                decoration: InputDecoration(labelText: 'JUSTIFICATIVA'.i18n),
                minLines: 3,
                maxLines: 4,
                validator: (value) =>
                    value!.isEmpty ? 'Informe uma justificativa'.i18n : null,
                onChanged: controller.setMensagem,
              ),
              const Text(
                'Caso esteja recusando esse comprimisso devido à indisponibilidade de horário, informe nesse campo acima outras opções de datas',
                style: TextStyle(
                  color: Colors.black45,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 40),
              Observer(
                builder: (_) => AppDefaultButton(
                  title: Observer(
                    builder: (_) => controller.recusando
                        ? const SpinKitCircle(color: AppConfig.white)
                        : Text(
                            'SIM, RECUSAR PRESENÇA'.i18n,
                            style: const TextStyle(
                              fontSize: 16,
                              color: Colors.white,
                            ),
                          ),
                  ),
                  onPressed: controller.mensagem!.isEmpty
                      ? null
                      : () {
                          controller.recusar();
                        },
                  color: AppConfig.red,
                  disabledColor: Colors.red.withValues(alpha: 0.5),
                ),
              ),
            ],
          );
        });
  }

  Widget _shimmer() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          const SizedBox(
            height: 15,
          ),
          Container(
            height: 30,
            color: AppConfig.white,
          ),
          const SizedBox(
            height: 15,
          ),
          Container(
            color: AppConfig.white,
            height: 20,
          ),
          const SizedBox(
            height: 15,
          ),
          Container(
            color: AppConfig.white,
            height: 45,
          ),
          const SizedBox(
            height: 15,
          ),
          Container(
            color: AppConfig.white,
            height: 45,
          ),
          const SizedBox(
            height: 40,
          ),
          Container(
            color: AppConfig.white,
            height: 20,
          ),
          const SizedBox(
            height: 15,
          ),
          Container(
            color: AppConfig.white,
            height: 20,
          ),
          const SizedBox(
            height: 15,
          ),
          Container(
            color: AppConfig.white,
            height: 20,
          ),
          const SizedBox(
            height: 15,
          ),
          Container(
            color: AppConfig.white,
            height: 20,
          ),
          const SizedBox(
            height: 15,
          ),
          Container(
            color: AppConfig.white,
            height: 45,
          ),
          Container(
            color: AppConfig.white,
            height: 20,
          ),
          const SizedBox(
            height: 15,
          ),
          Container(
            color: AppConfig.white,
            height: 20,
          ),
          const SizedBox(
            height: 15,
          ),
          Container(
            color: AppConfig.white,
            height: 20,
          ),
          const SizedBox(
            height: 15,
          ),
          Container(
            color: AppConfig.white,
            height: 20,
          ),
          const SizedBox(
            height: 15,
          ),
          Container(
            color: AppConfig.white,
            height: 20,
          ),
          const SizedBox(
            height: 15,
          ),
        ],
      ),
    );
  }
}
