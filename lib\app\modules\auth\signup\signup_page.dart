import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../../shared/core/app_config.dart';
import '../../../shared/core/app_translation.i18n.dart';
import '../../../shared/models/responses/cadastrar_response_model.dart';
import '../../../shared/widgets/app_default_button.dart';
import '../../../shared/widgets/app_dropdown_search.dart';
import '../../../shared/widgets/helpers/cpf_input_formatter.dart';
import '../../curriculum/curriculum_help/curriculum_help_module.dart';
import '../login/login_module.dart';
import '../../../shared/widgets/app_text_form_field.dart';
import 'signup_controller.dart';
import 'utils/privacy_politics.dart';
import 'utils/select_date.dart';

class SignupPage extends StatefulWidget {
  const SignupPage({super.key});

  @override
  State<SignupPage> createState() => _SignupPageState();
}

class _SignupPageState extends State<SignupPage> {
  final controller = Modular.get<SignupController>();

  final _formKey = GlobalKey<FormState>();

  final _dataNascFocusNode = FocusNode();
  final _cpfFocusNode = FocusNode();
  final _emailFocusNode = FocusNode();
  final _emailConfirmacaoFocusNode = FocusNode();
  final _senhaFocusNode = FocusNode();
  final _termosDeUsoFocusNode = FocusNode();
  final _politicaDePrivacidadeFocusNode = FocusNode();

  final _cpfInputFormatter = CpfInputFormatter();

  final maskFormatterBrazil = MaskTextInputFormatter(mask: "(##) #####-####");

  @override
  void dispose() {
    super.dispose();
    _formKey.currentState?.dispose();
    _dataNascFocusNode.dispose();
    _cpfFocusNode.dispose();
    _emailFocusNode.dispose();
    _emailConfirmacaoFocusNode.dispose();
    _senhaFocusNode.dispose();
    _termosDeUsoFocusNode.dispose();
    _politicaDePrivacidadeFocusNode.dispose();
  }

  String dataNascimento = '';
  String emailConfirmacao = '';
  bool termsOfUse = false;
  bool privacyPolicy = false;
  String? webviewUrl;

  final celController = TextEditingController();
  final telController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (bool value, q) {
        if (webviewUrl != null) {
          setState(() => webviewUrl = null);
          Future.value(false);
        }
        Future.value(true);
      },
      child: Scaffold(
        body: webviewUrl != null
            ? WebViewWidget(
                controller: WebViewController()..loadRequest(Uri.parse(webviewUrl ?? '')),
              )
            : Padding(
                padding: const EdgeInsets.all(24),
                child: Form(
                  key: _formKey,
                  child: Observer(
                    builder: (BuildContext context) {
                      return ListView(
                        physics: const BouncingScrollPhysics(),
                        shrinkWrap: true,
                        children: [
                          const Padding(
                            padding: EdgeInsets.only(top: 25.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Cadastrar Currículo',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Inter',
                                    fontSize: 24,
                                    color: Color(0xff161519),
                                  ),
                                ),
                                SizedBox(height: 4),
                                Text(
                                  'Prencha o formulário abaixo para continuar',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontFamily: 'Inter',
                                    fontWeight: FontWeight.w400,
                                    color: Color(0xff161519),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 24),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text('Nome'),
                              const SizedBox(height: 8),
                              AppTextFormField(
                                hintText: 'Digite seu nome completo',
                                controller: TextEditingController(
                                  text: controller.nome,
                                ),
                                onChanged: controller.setNome,
                                enabled: true,
                                isPassword: false,
                                radius: 6,
                                validator: (value) {
                                  if (value!.isEmpty) {
                                    return 'Campo obrigatório'.i18n;
                                  }

                                  return null;
                                },
                              )
                            ],
                          ),
                          const SizedBox(height: 16),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text('Data de Nascimento'),
                              const SizedBox(height: 8),
                              DateSelect(
                                controller: controller,
                                dataNascFocusNode: _dataNascFocusNode,
                              )
                            ],
                          ),
                          const SizedBox(height: 16),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'País',
                                style: TextStyle(fontSize: 14),
                              ),
                              const SizedBox(height: 8),
                              DropdownSearch(
                                context,
                                items: controller.paises
                                    .map((e) => DropdownSearchItem(
                                          value: e,
                                          searchKey: e.nome!,
                                          text: e.nome,
                                          child: Text(e.nome!),
                                        ))
                                    .toList(),
                                onSelected: (dynamic v) {
                                  controller.setPais(v);
                                  controller.getEstados();
                                },
                                value: controller.pais,
                                hintText: 'Escolha o país',
                              ),
                            ],
                          ),
                          Visibility(
                            visible: controller.pais?.nome == "Brasil",
                            replacement: const SizedBox.shrink(),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const SizedBox(height: 16),
                                const Text('CPF'),
                                const SizedBox(height: 8),
                                AppTextFormField(
                                  hintText: '___.___.___-__',
                                  controller: TextEditingController(
                                    text: controller.cpf,
                                  ),
                                  keyboardType: TextInputType.number,
                                  hintStyleColor: const Color(0xff60606b),
                                  onChanged: controller.setCpf,
                                  enabled: true,
                                  isPassword: false,
                                  radius: 6,
                                  focusNode: _cpfFocusNode,
                                  onFieldSubmitted: (_) => _cpfFocusNode.nextFocus(),
                                  validator: (value) {
                                    if (value!.isEmpty) {
                                      return 'Campo obrigatório'.i18n;
                                    }

                                    return null;
                                  },
                                  inputFormatters: [
                                    FilteringTextInputFormatter.digitsOnly,
                                    LengthLimitingTextInputFormatter(11),
                                    _cpfInputFormatter,
                                  ],
                                )
                              ],
                            ),
                          ),
                          const SizedBox(height: 16),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text('E-mail'),
                              const SizedBox(height: 8),
                              AppTextFormField(
                                hintText: 'Qual seu endereço de e-mail',
                                controller: TextEditingController(
                                  text: controller.email,
                                ),
                                textInputAction: TextInputAction.next,
                                keyboardType: TextInputType.emailAddress,
                                onChanged: controller.setEmail,
                                enabled: true,
                                isPassword: false,
                                radius: 6,
                                validator: (value) {
                                  if (value!.isEmpty) {
                                    return 'Campo obrigatório'.i18n;
                                  }

                                  return null;
                                },
                                focusNode: _emailFocusNode,
                                onFieldSubmitted: (_) => _emailFocusNode.nextFocus(),
                              )
                            ],
                          ),
                          const SizedBox(height: 16),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text('Confirmação de E-mail'),
                              const SizedBox(height: 8),
                              AppTextFormField(
                                hintText: 'Qual seu endereço de e-mail',
                                controller: TextEditingController(
                                  text: emailConfirmacao,
                                ),
                                textInputAction: TextInputAction.next,
                                keyboardType: TextInputType.emailAddress,
                                onChanged: (value) => emailConfirmacao = value,
                                enabled: true,
                                isPassword: false,
                                radius: 6,
                                validator: (value) {
                                  if (value!.isEmpty) {
                                    return 'Campo obrigatório'.i18n;
                                  } else if (value != controller.email) {
                                    return 'Confirmação de e-mail não coincide'.i18n;
                                  }

                                  return null;
                                },
                                focusNode: _emailConfirmacaoFocusNode,
                                onFieldSubmitted: (_) => _emailConfirmacaoFocusNode.nextFocus(),
                              )
                            ],
                          ),
                          const SizedBox(height: 16),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              const Text(
                                'Senha',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontFamily: 'Inter',
                                  fontWeight: FontWeight.w400,
                                  color: Color(0xff161519),
                                ),
                              ),
                              const SizedBox(
                                height: 10,
                              ),
                              Observer(
                                builder: (control) {
                                  return AppTextFormField(
                                    controller: TextEditingController(
                                      text: controller.senha,
                                    ),
                                    hintText: '**********************',
                                    onChanged: controller.setSenha,
                                    textInputAction: TextInputAction.next,
                                    enabled: control.mounted == true,
                                    isPassword: true,
                                    radius: 6,
                                    validator: (value) {
                                      if (value!.isEmpty) {
                                        return 'Campo obrigatório'.i18n;
                                      }
                                      return null;
                                    },
                                    focusNode: _senhaFocusNode,
                                    onFieldSubmitted: (_) => _senhaFocusNode.nextFocus(),
                                  );
                                },
                              ),
                            ],
                          ),
                          const SizedBox(height: 23),
                          PrivacyPolitics(
                            acceptTermsOfUse: termsOfUse,
                            onChanged: (value) {
                              setState(() => termsOfUse = value);
                              setState(() {
                                privacyPolicy = value;
                              });
                            },
                            termsOfUse: "https://www.empregare.com/pt-br/pagina/termos-de-uso",
                            privacyPolicy: "https://www.empregare.com/pt-br/pagina/privacidade",
                            cookiesPolicy: "https://www.empregare.com/pt-br/pagina/cookies",
                          ),
                          const SizedBox(height: 23),
                          Observer(
                            builder: (BuildContext context) {
                              if (controller.state == SignupState.saved) {
                                return const SizedBox.shrink();
                              }

                              return AppDefaultButton(
                                color: AppConfig.colorPrimary,
                                disabledColor: AppConfig.grey,
                                title: controller.state == SignupState.saving
                                    ? const SpinKitCircle(color: AppConfig.white)
                                    : Text(
                                        'Cadastre-se, grátis'.i18n,
                                        style: const TextStyle(
                                          color: AppConfig.white,
                                        ),
                                      ),
                                onPressed: !termsOfUse || !privacyPolicy
                                    ? null
                                    : () async {
                                        if (controller.pais == null) {
                                          controller.showSnackBarRequiredField(
                                            'País'.i18n,
                                            context,
                                          );
                                          return;
                                        }

                                        if (!_formKey.currentState!.validate()) {
                                          return;
                                        }

                                        CadastrarResponseModel response =
                                            await (controller.postCadastrar());
                                        ScaffoldMessenger.of(context).showSnackBar(
                                          SnackBar(
                                            backgroundColor:
                                                response.sucesso ? AppConfig.green : AppConfig.red,
                                            content: Text(
                                              response.mensagem == 'CadastroAutorizado'
                                                  ? 'Cadastro realizado com sucesso'.i18n
                                                  : response.mensagem,
                                            ),
                                          ),
                                        );
                                        if (response.sucesso) {
                                          await controller
                                              .saveSessionAndUpdateFirebase(response.token);
                                          Modular.to.pushNamedAndRemoveUntil(
                                              CurriculumHelpModule.route, (_) => false);
                                        }
                                      },
                              );
                            },
                          ),
                          const SizedBox(
                            height: 35,
                          ),
                          const Divider(
                            height: 2,
                          ),
                          const SizedBox(
                            height: 16,
                          ),
                          InkWell(
                            onTap: () => Modular.to.pushNamed(
                              LoginModule.routeName,
                            ),
                            child: const Text(
                              'Entrar com e-mail e senha',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: 14,
                                fontFamily: 'Inter',
                                fontWeight: FontWeight.w400,
                                color: Color(0xff161519),
                              ),
                            ),
                          ),
                          const SizedBox(
                            height: 38,
                          ),
                        ],
                      );
                    },
                  ),
                ),
              ),
      ),
    );
  }
}
