// ignore_for_file: depend_on_referenced_packages

class DeficienciaResponseModel {
  final bool sucesso;
  final String mensagem;
  final List<DeficienciaModel>? dados;
  final List<DeficienciaLaudoModel>? laudo;

  DeficienciaResponseModel({
    this.sucesso = false,
    this.dados,
    this.laudo,
    this.mensagem = "",
  });

  factory DeficienciaResponseModel.fromJson(Map<String, dynamic> json) =>
      DeficienciaResponseModel(
        sucesso: json['sucesso'] ?? false,
        mensagem: json['mensagem'] ?? "",
        dados: (json['dados'] as List<dynamic>?)
            ?.map((e) => DeficienciaModel.fromJson(e as Map<String, dynamic>))
            .toList(),
        laudo: (json['laudo'] as List<dynamic>?)
            ?.map((e) =>
                DeficienciaLaudoModel.fromJson(e as Map<String, dynamic>))
            .toList(),
      );
}

class DeficienciaModel {
  final int? deficienciaID;
  final int? tipo;
  final String? nome;
  final String? categoria;

  DeficienciaModel({this.deficienciaID, this.tipo, this.nome, this.categoria});

  factory DeficienciaModel.fromJson(Map<String, dynamic> json) =>
      DeficienciaModel(
        deficienciaID: json['deficienciaID'] as int?,
        tipo: json['tipo'] as int?,
        nome: json['nome'] as String?,
        categoria: json['categoria'] as String?,
      );
}

class DeficienciaLaudoModel {
  final int? id;
  final String? arquivo;
  final String? dataCadastro;

  DeficienciaLaudoModel({this.id, this.arquivo, this.dataCadastro});

  factory DeficienciaLaudoModel.fromJson(Map<String, dynamic> json) =>
      DeficienciaLaudoModel(
        id: json['id'] as int?,
        arquivo: json['arquivo'] as String?,
        dataCadastro: json['dataCadastro'] as String?,
      );
}
