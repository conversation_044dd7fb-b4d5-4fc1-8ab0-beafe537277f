import 'package:flutter_modular/flutter_modular.dart';

import '../../shared/models/responses/simple_response_model.dart';
import '../../shared/repositories/settings_repository.dart';
import '../../shared/services/session_service.dart';

class SettingsController {
  final SessionService _sessionService = Modular.get();
  final SettingsRepository _repository = Modular.get();

  Future<void> sair() async {
    await _sessionService.logoff();
    Modular.to.navigate(Modular.initialRoute);
  }

  Future<void> desativarConta() async {
    var response = await _repository.postDesativarConta();
    if (response.sucesso) await sair();
  }

  Future<SimpleResponseModel?> delete() async {
    var response = await _repository.deleteAccount();
    if (response.sucesso) {
      await sair();
      return null;
    }
    return response;
  }
}
