class QuestionarioResponseModel {
  final bool sucesso;
  final String mensagem;
  final QuestionarioModel? questionario;

  QuestionarioResponseModel(
      {this.sucesso = false, this.mensagem = "", this.questionario});

  factory QuestionarioResponseModel.fromJson(Map<String, dynamic> json) {
    return QuestionarioResponseModel(
      sucesso: json['sucesso'] ?? false,
      mensagem: json['mensagem'] ?? "",
      questionario: json['questionario'] == null
          ? null
          : QuestionarioModel.fromJson(
              json['questionario'] as Map<String, dynamic>,
            ),
    );
  }
}

class QuestionarioModel {
  final String? titulo;
  final String? token;
  final bool? bloqueado;
  final int? totalPerguntas;
  final int? totalRespondido;
  final String? ultimaRespostaID;
  final List<OrientacaoModel>? orientacoes;

  QuestionarioModel({
    this.titulo,
    this.token,
    this.bloqueado,
    this.totalPerguntas,
    this.totalRespondido,
    this.ultimaRespostaID,
    this.orientacoes,
  });

  factory QuestionarioModel.fromJson(Map<String, dynamic> json) {
    return QuestionarioModel(
      titulo: json['titulo'] as String?,
      token: json['token'] as String?,
      bloqueado: json['bloqueado'] as bool?,
      totalPerguntas: json['totalPerguntas'] as int?,
      totalRespondido: json['totalRespondido'] as int?,
      ultimaRespostaID: json['ultimaRespostaID'] as String?,
      orientacoes: (json['orientacoes'] as List<dynamic>?)
          ?.map((e) => OrientacaoModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class OrientacaoModel {
  final String? id;
  final String? titulo;
  final String? descricao;

  OrientacaoModel({this.id, this.titulo, this.descricao});

  factory OrientacaoModel.fromJson(Map<String, dynamic> json) {
    return OrientacaoModel(
      id: json['id'] as String?,
      titulo: json['titulo'] as String?,
      descricao: json['descricao'] as String?,
    );
  }
}
