import 'package:dio/dio.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../core/app_rest.dart';
import '../models/responses/cancelar_inscricao_response_model.dart';
import '../models/responses/inscricao_response_model.dart';
import '../models/responses/inscricoes_response_model.dart';

class InscriptionRepository {
  final AppRest _rest = Modular.get();

  Future<InscricoesResponseModel> getInscricoes({required int pagina}) async {
    try {
      final response = await _rest.get('/candidato/inscricoes/$pagina');
      return InscricoesResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return InscricoesResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return InscricoesResponseModel.fromJson(err.response!.data);
    }
  }

  Future<InscricaoResponseModel> getInscricao(
      {required int? candidaturaID}) async {
    try {
      final response = await _rest.get(
        '/candidato/inscricoes/detalhes/$candidaturaID',
      );
      return InscricaoResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return InscricaoResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return InscricaoResponseModel.fromJson(err.response!.data);
    }
  }

  Future<CancelarInscricaoResponseModel> getCancelarInscricao(
    int? candidaturaId,
  ) async {
    try {
      final response = await _rest.get(
        '/vagas/cancelar-inscricao/$candidaturaId',
      );
      return CancelarInscricaoResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return CancelarInscricaoResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return CancelarInscricaoResponseModel.fromJson(err.response!.data);
    }
  }
}
