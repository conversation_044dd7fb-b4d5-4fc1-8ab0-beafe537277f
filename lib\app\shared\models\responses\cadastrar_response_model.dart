class CadastrarResponseModel {
  final bool sucesso;
  final String mensagem;
  final String? token;

  CadastrarResponseModel({
    this.sucesso = false,
    this.mensagem = "",
    this.token,
  });

  factory CadastrarResponseModel.fromJson(Map<String, dynamic> json) {
    return CadastrarResponseModel(
      sucesso: json['sucesso'] ?? false,
      mensagem: json['mensagem'] ?? "",
      token: json['token'] as String?,
    );
  }
}
