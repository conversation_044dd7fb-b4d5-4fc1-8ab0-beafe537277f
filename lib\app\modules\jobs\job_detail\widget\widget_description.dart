import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../../../shared/core/app_config.dart';
import '../../../../shared/widgets/app_html.dart';
import '../../../../shared/widgets/app_info.dart';
import '../job_detail_controller.dart';

class Description extends StatelessWidget {
  const Description({
    super.key,
    required this.controller,
  });

  final JobDetailController controller;

  @override
  Widget build(BuildContext context) {
    double heightCidades() {
      if (controller.jobDetails!.cidades!.length > 3) {
        if (controller.mostrarMaisCidades) {
          return (controller.jobDetails!.cidades!.length + 1) * 14.0;
        } else {
          return 56.0;
        }
      }

      return controller.jobDetails!.cidades!.length * 14.0;
    }

    RegExp exp = RegExp(r'<[^>]*>$');
    String texto = controller.jobDetails?.descricao!.replaceAll(exp, '') ?? '';

    while (exp.hasMatch(texto)) {
      texto = texto.replaceFirst(exp, '');
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.max,
      children: <Widget>[
        Text(
          'Descrição'.i18n,
          style: const TextStyle(
            fontSize: 14,
            color: AppConfig.colorPrimary,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 20),
        AppInfo(
          padding: const EdgeInsets.all(0),
          icon: const Icon(
            Icons.location_on,
            color: Colors.grey,
          ),
          title: Text('LOCAL DE TRABALHO'.i18n),
          child: Observer(
            builder: (_) => SizedBox(
              height: heightCidades(),
              child: Visibility(
                visible: controller.jobDetails!.modalidade == 4,
                replacement: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Expanded(
                      child: ListView.builder(
                        itemCount: controller.jobDetails!.cidades!.length,
                        itemBuilder: (_, index) {
                          return Text(
                            controller.jobDetails!.cidades![index].nome ?? '',
                            style: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          );
                        },
                      ),
                    ),
                    Visibility(
                      visible: controller.jobDetails!.cidades!.length > 3,
                      child: InkWell(
                        child: Text(
                          'Ver ${controller.mostrarMaisCidades ? 'menos' : 'mais'}'
                              .i18n,
                          style: const TextStyle(
                            color: AppConfig.colorPrimary,
                            fontSize: 12,
                          ),
                        ),
                        onTap: () {
                          controller.setMostrarMaisCidades(
                            !controller.mostrarMaisCidades,
                          );
                        },
                      ),
                    )
                  ],
                ),
                child: const Text("Totalmente Remoto"),
              ),
            ),
          ),
        ),
        const SizedBox(
          height: 15,
        ),
        if (controller.jobDetails!.horario != null &&
            controller.jobDetails!.horario!.isNotEmpty)
          AppInfo(
            icon: const Icon(
              Icons.timer,
              color: Colors.grey,
            ),
            title: Text('HORÁRIO'.i18n),
            subtitle: Text(controller.jobDetails!.horario ?? ''),
          ),
        const SizedBox(
          height: 15,
        ),
        AppInfo(
          icon: const Icon(
            Icons.work,
            color: Colors.grey,
          ),
          title: Text('CONTRATAÇÃO'.i18n),
          subtitle: Text(controller.jobDetails!.contratacao ?? ''),
        ),
        const SizedBox(
          height: 15,
        ),
        AppHtml(data: texto)
      ],
    );
  }
}
