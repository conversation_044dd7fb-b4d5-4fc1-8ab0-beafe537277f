import 'package:mobx/mobx.dart';

part 'security_and_lgpd_controller.g.dart';

// ignore: library_private_types_in_public_api
class SecurityAndLGPDController = _SecurityAndLGPDControllerBase
    with _$SecurityAndLGPDController;

abstract class _SecurityAndLGPDControllerBase with Store {
  @observable
  bool loadingLogsLgpd = false;

  @observable
  List logsLgpd = [].asObservable();

  @action
  Future<void> getLogsLGPD() async {
    loadingLogsLgpd = true;

    loadingLogsLgpd = false;
  }

  @observable
  bool loadingLogsLogin = false;

  @observable
  List logsLogin = [].asObservable();

  @action
  Future<void> getLogsLogin() async {
    loadingLogsLogin = true;

    loadingLogsLogin = false;
  }
}
