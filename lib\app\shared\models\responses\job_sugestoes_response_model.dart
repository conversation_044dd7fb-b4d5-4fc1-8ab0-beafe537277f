import 'inscricoes_response_model.dart';

class JobSugestoesResponseModel {
  final FacetsModel? facets;
  final int? totalVagas;
  final int? totalRegistro;
  final int? pagina;
  final int? itensPagina;
  final double? totalPaginas;
  final List<JobModel>? dados;

  JobSugestoesResponseModel({
    this.facets,
    this.totalVagas,
    this.totalRegistro,
    this.pagina,
    this.itensPagina,
    this.totalPaginas,
    this.dados,
  });
  factory JobSugestoesResponseModel.fromJson(Map<String, dynamic> json) {
    return JobSugestoesResponseModel(
      facets: json['facets'] == null
          ? null
          : FacetsModel.fromJson(json['facets'] as Map<String, dynamic>),
      totalVagas: json['totalVagas'] as int?,
      totalRegistro: json['totalRegistro'] as int?,
      pagina: json['pagina'] as int?,
      itensPagina: json['itensPagina'] as int?,
      totalPaginas: (json['totalPaginas'] as num?)?.toDouble(),
      dados: (json['dados'] as List<dynamic>?)
          ?.map((e) => JobModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class FacetModel {
  final String? id;
  final String nome;
  final int? qntde;

  bool? _selected;

  bool? get isSelected => _selected;

  setSelected(bool? value) => _selected = value;

  FacetModel({this.id, this.nome = "", this.qntde}) {
    _selected = false;
  }

  factory FacetModel.fromJson(Map<String, dynamic> json) {
    return FacetModel(
      id: json['id'] as String?,
      nome: json['nome'] ?? "",
      qntde: json['qntde'] as int?,
    );
  }
}

class FacetsModel {
  final List<FacetModel> pais;
  final List<FacetModel> cidade;
  final List<FacetModel> uf;
  final List<FacetModel> nivel;
  final List<FacetModel> regime;
  final List<FacetModel> tipoRecrutamento;
  final List<FacetModel> selecaoCega;
  final List<FacetModel> pcd;

  factory FacetsModel.empty() {
    return FacetsModel(
      cidade: [],
      pais: [],
      uf: [],
      nivel: [],
      regime: [],
      tipoRecrutamento: [],
      selecaoCega: [],
      pcd: [],
    );
  }

  FacetsModel({
    required this.pais,
    required this.cidade,
    required this.uf,
    required this.nivel,
    required this.regime,
    required this.tipoRecrutamento,
    required this.selecaoCega,
    required this.pcd,
  });

  factory FacetsModel.fromJson(Map<String, dynamic> json) {
    return FacetsModel(
      pais: List.from(json['pais']?.map((e) => FacetModel.fromJson(e)) ?? []),
      cidade:
          List.from(json['cidade']?.map((e) => FacetModel.fromJson(e)) ?? []),
      uf: List.from(json['uf']?.map((e) => FacetModel.fromJson(e)) ?? []),
      nivel: List.from(json['nivel']?.map((e) => FacetModel.fromJson(e)) ?? []),
      regime:
          List.from(json['regime']?.map((e) => FacetModel.fromJson(e)) ?? []),
      tipoRecrutamento: List.from(
          json['tipoRecrutamento']?.map((e) => FacetModel.fromJson(e)) ?? []),
      selecaoCega: List.from(
          json['selecaoCega']?.map((e) => FacetModel.fromJson(e)) ?? []),
      pcd: List.from(json['pcd']?.map((e) => FacetModel.fromJson(e)) ?? []),
    );
  }

  FacetsModel copyWith({
    List<FacetModel>? pais,
    List<FacetModel>? cidade,
    List<FacetModel>? uf,
    List<FacetModel>? nivel,
    List<FacetModel>? regime,
    List<FacetModel>? tipoRecrutamento,
    List<FacetModel>? selecaoCega,
    List<FacetModel>? pcd,
  }) {
    return FacetsModel(
      pais: pais ?? this.pais,
      cidade: cidade ?? this.cidade,
      uf: uf ?? this.uf,
      nivel: nivel ?? this.nivel,
      regime: regime ?? this.regime,
      tipoRecrutamento: tipoRecrutamento ?? this.tipoRecrutamento,
      selecaoCega: selecaoCega ?? this.selecaoCega,
      pcd: pcd ?? this.pcd,
    );
  }
}
