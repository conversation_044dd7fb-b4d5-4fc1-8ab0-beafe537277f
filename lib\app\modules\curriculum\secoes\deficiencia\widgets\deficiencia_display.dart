import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:i18n_extension/default.i18n.dart';

import '../../../../../shared/core/app_config.dart';
import '../../../../../shared/models/responses/listar_deficiencias_response_model.dart';
import '../../../controllers/deficiencies_controller.dart';
import '../../../curriculum_controller.dart';

class DeficienciaDisplay extends StatelessWidget {
  final DeficienciesController controller;
  final Function(DeficienciaLaudoModel) onRemoveLaudo;

  const DeficienciaDisplay({
    super.key,
    required this.controller,
    required this.onRemoveLaudo,
  });

  @override
  Widget build(BuildContext context) {
    return Observer(
      builder: (_) => Visibility(
        visible: controller.deficiencias!.isEmpty,
        replacement: Column(
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ...controller.joinDeficiencias().map(
                  (c) => Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                        ),
                        child: Text(
                          c['categoria'] ?? '',
                          textAlign: TextAlign.center,
                          style: const TextStyle(
                            fontFamily: 'Inter',
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: Color(0xFF292929),
                          ),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 5,
                        ),
                        child: Text(
                          c['deficiencias'] ?? '',
                          textAlign: TextAlign.center,
                          style: const TextStyle(
                            fontFamily: 'Inter',
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                            color: Color(0xFF666666),
                          ),
                        ),
                      ),
                      Divider(
                        color: Colors.grey.shade300,
                      ),
                    ],
                  ),
                ),
            if (controller.laudos!.isNotEmpty) ...[
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                ),
                child: Text(
                  'Laudos'.i18n,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontFamily: 'Inter',
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF292929),
                  ),
                ),
              ),
              ...controller.laudos!.map(
                (l) => Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                  ),
                  child: Card(
                    elevation: 0,
                    color: const Color(0xFFF7F7F7),
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(
                                Icons.attach_file,
                                size: 18,
                                color: Color(0xFF666666),
                              ),
                              const Text(
                                'Laudo PcD',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFF666666),
                                ),
                              ),
                              const Spacer(),
                              Material(
                                child: InkWell(
                                  onTap: () {
                                    if (l.arquivo != null) {
                                      controller.downloadLaudo(l.arquivo!);
                                    }
                                  },
                                  child: const Icon(
                                    Icons.sim_card_download_rounded,
                                    color: AppConfig.colorPrimary,
                                    size: 24,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 25),
                              Material(
                                child: InkWell(
                                  onTap: () => onRemoveLaudo(l),
                                  child: const Icon(
                                    Icons.delete,
                                    color: Colors.red,
                                    size: 24,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          Text(
                            l.dataCadastro!,
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                              fontFamily: 'Inter',
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              color: Color(0xFF666666),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              )
            ],
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 24,
            vertical: 10,
          ),
          child: Text(
             Modular.get<CurriculumController>().pessoa?.semDeficiencia == true
                ? 'Não tenho deficiência'
                : 'Você é uma Pessoa com Deficiência ou Reabilitado pelo INSS?',
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontFamily: 'Inter',
              fontSize: 16,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
      ),
    );
  }
}
