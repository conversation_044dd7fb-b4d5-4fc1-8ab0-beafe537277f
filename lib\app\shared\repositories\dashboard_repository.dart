import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../modules/splash/splash_module.dart';
import '../core/app_rest.dart';
import '../models/responses/clube_beneficios_atualizar_termos.dart';
import '../models/responses/clube_beneficios_categorias_parceiro.dart';
import '../models/responses/clube_beneficios_detalhes_model.dart';
import '../models/responses/clube_beneficios_link_clicado.dart';
import '../models/responses/clube_beneficios_promocoes.dart';
import '../models/responses/clube_beneficios_response_model.dart';
import '../models/responses/cluble_beneficios_categorias_model.dart';
import '../models/responses/dash_articles_response_model.dart';
import '../models/responses/dash_notificacoes_response_model.dart';
import '../services/session_service.dart';

class DashboardRepository {
  final AppRest _rest = Modular.get();

  Future<DashNotificacoesResponseModel> getNotificacoes() async {
    try {
      final response = await _rest.get('/candidato/dashboard/notificacao');

      if (response.statusCode == 401) {
        await Modular.get<SessionService>().logoff();
        Modular.to.pushNamedAndRemoveUntil(SplashModule.route, (_) => false);
      }

      return DashNotificacoesResponseModel.fromJson(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return DashNotificacoesResponseModel.fromJson({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return DashNotificacoesResponseModel.fromJson(err.response!.data);
    }
  }

  Future<DashArticlesResponseModel> getArticles() async {
    try {
      final response = await _rest.get('/blog/ultimos-posts');

      if (response.statusCode == 401) {
        await Modular.get<SessionService>().logoff();
        Modular.to.pushNamedAndRemoveUntil(
          SplashModule.route,
          (_) => false,
        );
      }

      return DashArticlesResponseModel.fromMap(response.data);
    } on DioException catch (err) {
      if (err.response?.data is! Map) {
        return DashArticlesResponseModel.fromMap({
          'mensagem': 'Erro desconhecido. Tente novamente mais tarde',
        });
      }

      return DashArticlesResponseModel.fromMap(err.response!.data);
    }
  }

  Future<bool> updateTokenNotificacao(String? token) async {
    try {
      final response = await _rest.post(
          '/candidato/dashboard/atualizar-token-notificacao',
          data: {"tokenNotificacao": token});
      return response.statusCode == 200;
    } on DioException catch (err) {
      throw err.message ?? '';
    }
  }

  String _getErrorMessage(DioException dioError) {
    try {
      final Map<String, dynamic> errorResponse =
          json.decode(dioError.response!.data.toString());
      return errorResponse['error'] ?? 'Erro desconhecido';
    } catch (e) {
      return 'Erro desconhecido';
    }
  }

  Future<ClubeBeneficiosResponseModel> getClubeBeneficios() async {
    int maxRetries = 3;
    int currentAttempt = 0;

    while (currentAttempt < maxRetries) {
      try {
        Response<dynamic> response = await _rest.get(
          '/candidato/clube-beneficios/listar',
          options: Options(
            receiveTimeout: const Duration(seconds: 120),
            sendTimeout: const Duration(seconds: 30),
          ),
        );

        if (response.statusCode == 200) {
          return ClubeBeneficiosResponseModel.fromJson(response.data);
        } else {
          throw DioException(
            requestOptions: response.requestOptions,
            response: response,
          );
        }
      } on DioException catch (err) {
        currentAttempt++;

        bool isTimeoutError = err.type == DioExceptionType.connectionTimeout ||
            err.type == DioExceptionType.receiveTimeout ||
            err.type == DioExceptionType.sendTimeout;

        if (isTimeoutError && currentAttempt < maxRetries) {
          await Future.delayed(Duration(seconds: currentAttempt * 2));
          continue;
        }

        if (err.response != null) {
          final errorMessage = _getErrorMessage(err);
          throw DioException(
            requestOptions: err.requestOptions,
            message: errorMessage,
          );
        } else {
          if (isTimeoutError) {
            throw 'Timeout na requisição. Verifique sua conexão com a internet e tente novamente.';
          }
          throw 'Erro na requisição ${err.message}';
        }
      }
    }

    throw 'Não foi possível carregar os benefícios após $maxRetries tentativas. Verifique sua conexão e tente novamente.';
  }

  Future<ClubeBeneficiosDetalhesModel> getClubeBeneficiosDetalhes({
    required int detalhesId,
  }) async {
    try {
      Response<dynamic> response = await _rest.get(
        '/candidato/clube-beneficios/detalhes/$detalhesId',
      );

      if (response.statusCode == 200) {
        return ClubeBeneficiosDetalhesModel.fromJson(response.data);
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
        );
      }
    } on DioException catch (err) {
      if (err.response != null) {
        final errorMessage = _getErrorMessage(err);
        throw DioException(
          requestOptions: err.requestOptions,
          message: errorMessage,
        );
      } else {
        throw 'Erro na requisição ${err.message}';
      }
    }
  }

  Future<ClubeBeneficiosPromocoesModel> getClubeBeneficiosPromocoes({
    required int detalhesId,
  }) async {
    try {
      Response<dynamic> response = await _rest.get(
        '/candidato/clube-beneficios/promocoes/$detalhesId',
      );

      if (response.statusCode == 200) {
        return ClubeBeneficiosPromocoesModel.fromJson(response.data);
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
        );
      }
    } on DioException catch (err) {
      if (err.response != null) {
        final errorMessage = _getErrorMessage(err);
        throw DioException(
          requestOptions: err.requestOptions,
          message: errorMessage,
        );
      } else {
        throw 'Erro na requisição ${err.message}';
      }
    }
  }

  Future<ClubeBeneficiosCategoriasParceiroModel>
      getClubeBeneficiosCategoriasParceiro({required int id}) async {
    try {
      Response<dynamic> response = await _rest.get(
        '/candidato/clube-beneficios/categorias-parceiro/$id',
      );

      if (response.statusCode == 200) {
        return ClubeBeneficiosCategoriasParceiroModel.fromJson(response.data);
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
        );
      }
    } on DioException catch (err) {
      if (err.response != null) {
        final errorMessage = _getErrorMessage(err);
        throw DioException(
          requestOptions: err.requestOptions,
          message: errorMessage,
        );
      } else {
        throw 'Erro na requisição ${err.message}';
      }
    }
  }

  Future<ClubeBeneficiosCategoriasModel> getClubeBeneficiosCategorias() async {
    try {
      Response<dynamic> response = await _rest.get(
        '/candidato/clube-beneficios/categorias',
      );

      if (response.statusCode == 200) {
        return ClubeBeneficiosCategoriasModel.fromJson(response.data);
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
        );
      }
    } on DioException catch (err) {
      if (err.response != null) {
        final errorMessage = _getErrorMessage(err);
        throw DioException(
          requestOptions: err.requestOptions,
          message: errorMessage,
        );
      } else {
        throw 'Erro na requisição ${err.message}';
      }
    }
  }

  Future<ClubeBeneficiosAtualizarTermosModel>
      getClubeBeneficiosAtualizarTermos({
    required bool termos,
  }) async {
    try {
      Response<dynamic> response = await _rest.post(
        '/candidato/clube-beneficios/atualizar-termos?aceitouTermos=$termos',
      );

      if (response.statusCode == 200) {
        return ClubeBeneficiosAtualizarTermosModel.fromJson(response.data);
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
        );
      }
    } on DioException catch (err) {
      if (err.response != null) {
        final errorMessage = _getErrorMessage(err);
        throw DioException(
          requestOptions: err.requestOptions,
          message: errorMessage,
        );
      } else {
        throw 'Erro na requisição ${err.message}';
      }
    }
  }

  Future<ClubeBeneficiosLinkClicadoModel> getClubeBeneficiosLinkClicado({
    required int parceiroID,
    required int promocaoID,
  }) async {
    try {
      Response<dynamic> response = await _rest.post(
        '/candidato/clube-beneficios/link-clicado?parcroID=$parceiroID&promocaoID=$promocaoID',
      );

      if (response.statusCode == 200) {
        return ClubeBeneficiosLinkClicadoModel.fromJson(response.data);
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
        );
      }
    } on DioException catch (err) {
      if (err.response != null) {
        final errorMessage = _getErrorMessage(err);
        throw DioException(
          requestOptions: err.requestOptions,
          message: errorMessage,
        );
      } else {
        throw 'Erro na requisição ${err.message}';
      }
    }
  }
}
