import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../../../shared/models/responses/clube_beneficios_detalhes_model.dart';
import '../../../../../shared/models/responses/clube_beneficios_promocoes.dart';
import '../../tab_notifications_controller.dart';
import 'benefits_dialog.dart';
import 'benefits_more_card.dart';

class BenefitsMore extends StatefulWidget {
  final ClubeBeneficiosDetalhesModel? model;
  final ClubeBeneficiosPromocoesModel? modelPromo;

  const BenefitsMore({
    super.key,
    this.model,
    this.modelPromo,
  });

  @override
  State<BenefitsMore> createState() => _BenefitsMoreState();
}

class _BenefitsMoreState extends State<BenefitsMore> {
  final controller = Modular.get<TabNotificationsController>();

  @override
  void initState() {
    super.initState();
    controller.load();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        leadingWidth: 0,
        leading: const SizedBox.shrink(),
        elevation: 1,
        shadowColor: Colors.black45,
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  Navigator.of(context).pop();
                },
                customBorder: const CircleBorder(),
                splashColor: Colors.grey.withValues(alpha: 0.6),
                child: Container(
                  padding: const EdgeInsets.all(8.0),
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.close,
                    color: Colors.black,
                    size: 36,
                  ),
                ),
              ),
            ),
          ),
        ],
        title: const Text(
          'Benefícios Empregare',
          style: TextStyle(
            color: Colors.black,
            fontFamily: 'Inter',
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      body: ListView(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Padding(
                  padding: EdgeInsets.symmetric(
                    vertical: 10.0,
                  ),
                  child: Text(
                    'Tenha acesso gratuito aos benefícios que preparamos especialmente para você.',
                    style: TextStyle(
                      color: Colors.black,
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w300,
                    ),
                  ),
                ),
                Flexible(
                  fit: FlexFit.loose,
                  child: ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: controller.beneficios?.dados?.length ?? 0,
                    itemBuilder: (context, index) {
                      final beneficios = controller.beneficios?.dados?[index];
                      return BenefitsMoreCard(
                        imagePath: beneficios?.logo,
                        isNew: false,
                        discountText: beneficios?.beneficio,
                        titlePartner: beneficios?.nome,
                        onTap: () async {
                          if (controller.loadingPromo == true) return;

                          await controller.getClubeBeneficiosDetalhes(
                            beneficios?.id ?? 0,
                          );

                          await controller.getClubeBeneficiosPromocoes(
                            beneficios?.id ?? 0,
                          );

                          showDialog(
                            context: context,
                            builder: (_) => BenefitsDialog(
                              model: controller.beneficiosDetalhes,
                              modelPromo: controller.beneficiosPromocoes,
                            ),
                          );
                        },
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
