import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../app_controller.dart';
import '../core/app_config.dart';

class SnackbarHelper {
  static void showSnackbarSucesso(BuildContext context, String mensagem) {
    Modular.get<AppController>().updateProfile();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        backgroundColor: Colors.green,
        content: Text(mensagem),
        duration: const Duration(milliseconds: 1500),
        key: const Key('showSnackbarSucesso'),
      ),
    );
  }

  static info(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        backgroundColor: AppConfig.colorPrimary,
        content: Text(message),
      ),
    );
  }

  static error(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        backgroundColor: AppConfig.red,
        content: Text(message),
      ),
    );
  }
}
