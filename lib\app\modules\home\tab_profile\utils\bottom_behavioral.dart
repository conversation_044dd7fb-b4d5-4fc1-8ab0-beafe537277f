import 'package:flutter/material.dart';

import '../../../welcome/utils/build_indicator_widget.dart';

class BottomBehavioral extends StatefulWidget {
  const BottomBehavioral({
    super.key,
  });

  @override
  State<BottomBehavioral> createState() => _BottomBehavioralState();
}

class _BottomBehavioralState extends State<BottomBehavioral> {
  final pageController = PageController();

  late int currentPage = 0;

  final List<String> texts = [
    '<PERSON>ia as instruções cuidadosamente. Caso precise ler novamente durante o questionário, clique no ícone \n\nVocê verá perguntas que exigem uma resposta, explorando suas atitudes, seus sentimentos e suas necessidades típicas em várias situações.',
    'As perguntas podem ser desafiadoras e abordar temas pessoais, mas é importante que você seja honesto e sincero em suas respostas.',
    'Se precisar revisar as instruções ou as perguntas durante o questionário, clique no ícone indicado.',
  ];

  @override
  void initState() {
    super.initState();
    pageController.addListener(() {
      setState(() {
        currentPage = pageController.page!.round();
      });
    });
  }

  @override
  void dispose() {
    pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: DecoratedBox(
        decoration: const BoxDecoration(
          color: Color(0xFF3E3EDF),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(22),
            topRight: Radius.circular(22),
          ),
        ),
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Container(
                height: 4,
                width: 132,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(500),
                ),
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Padding(
                  padding: const EdgeInsets.only(right: 16.0),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () {
                        Navigator.of(context).pop();
                      },
                      customBorder: const CircleBorder(),
                      splashColor: Colors.grey.withValues(alpha: 0.8),
                      hoverDuration: const Duration(seconds: 2),
                      child: Container(
                        decoration: const BoxDecoration(
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.close,
                          color: Colors.white,
                          size: 36,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const Align(
              alignment: Alignment.centerLeft,
              child: Padding(
                padding: EdgeInsets.only(left: 24.0),
                child: Text(
                  'Aguardar definição',
                  style: TextStyle(
                    fontSize: 24,
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
            Flexible(
              child: PageView.builder(
                controller: pageController,
                itemCount: texts.length,
                itemBuilder: (context, index) {
                  return Padding(
                    padding: const EdgeInsets.only(
                      left: 24,
                      right: 24,
                      top: 12,
                    ),
                    child: Text(
                      texts[index],
                      textAlign: TextAlign.left,
                      style: const TextStyle(
                        fontSize: 16,
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w400,
                        color: Colors.white,
                      ),
                    ),
                  );
                },
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 75),
              child: BuildIndicator(
                count: texts.length,
                currentIndex: currentPage,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
