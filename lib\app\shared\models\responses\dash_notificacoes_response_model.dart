class DashNotificacoesResponseModel {
  final bool sucesso;
  final String mensagem;
  final String? versaoApp;
  final List<QuestionarioModel>? questionarios;
  final List<AgendaModel>? agenda;
  final bool? talentoday;

  bool get temNoticacao =>
      (questionarios != null && questionarios!.isNotEmpty) ||
      (agenda != null && agenda!.isNotEmpty) ||
      (talentoday ?? false);

  DashNotificacoesResponseModel({
    this.sucesso = false,
    this.mensagem = "",
    this.versaoApp,
    this.questionarios,
    this.agenda,
    this.talentoday,
  });

  factory DashNotificacoesResponseModel.fromJson(Map<String, dynamic> json) {
    return DashNotificacoesResponseModel(
      sucesso: json["sucesso"] ?? false,
      versaoApp: json["versaoApp"] is num
          ? json["versaoApp"].toString()
          : json["versaoApp"],
      questionarios: (json["questionarios"] as List<dynamic>?)
          ?.map((i) => QuestionarioModel.fromJson(i))
          .toList(),
      agenda: (json["agenda"] as List<dynamic>?)
          ?.map((i) => AgendaModel.fromJson(i))
          .toList(),
      talentoday: json["talentoday"] ?? false,
      mensagem: json["mensagem"] ?? "",
    );
  }
}

class QuestionarioModel {
  final int? id;
  final String? titulo;
  final String? vaga;
  final String? situacao;
  final String? solicitado;
  final String? expira;
  final String? questoes;

  QuestionarioModel({
    this.id,
    this.titulo,
    this.vaga,
    this.situacao,
    this.solicitado,
    this.expira,
    this.questoes,
  });

  factory QuestionarioModel.fromJson(Map<String, dynamic> json) {
    return QuestionarioModel(
      id: json["id"],
      titulo: json["titulo"],
      vaga: json["vaga"],
      situacao: json["situacao"],
      solicitado: json["solicitado"],
      expira: json["expira"],
      questoes: json["questoes"],
    );
  }
}

class AgendaModel {
  final String? token;
  final String? data;
  final String? situacao;

  AgendaModel({
    this.token,
    this.data,
    this.situacao,
  });

  factory AgendaModel.fromJson(Map<String, dynamic> json) {
    return AgendaModel(
      token: json["token"],
      data: json["data"],
      situacao: json["situacao"],
    );
  }
}
