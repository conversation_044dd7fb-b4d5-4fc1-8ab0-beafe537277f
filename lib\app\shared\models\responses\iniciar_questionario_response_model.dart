class IniciarQuestionarioResponseModel {
  final bool sucesso;
  final bool? acertou;
  final String? link;
  final String mensagem;
  final PerguntaModel? pergunta;

  IniciarQuestionarioResponseModel({
    this.sucesso = false,
    this.acertou,
    this.link,
    this.mensagem = "",
    this.pergunta,
  });

  factory IniciarQuestionarioResponseModel.fromJson(Map<String, dynamic> json) {
    return IniciarQuestionarioResponseModel(
      sucesso: json['sucesso'] ?? false,
      mensagem: json['mensagem'] ?? "",
      acertou: json['acertou'] as bool?,
      link: json['link'] as String?,
      pergunta: json['pergunta'] == null
          ? null
          : PerguntaModel.fromJson(json['pergunta'] as Map<String, dynamic>),
    );
  }
}

class PerguntaModel {
  final String? titulo;
  final String? tipo;
  final List<String>? alternativas;
  final String? alternativaSelecionada;

  PerguntaModel({
    this.titulo,
    this.tipo,
    this.alternativas,
    this.alternativaSelecionada,
  });

  factory PerguntaModel.fromJson(Map<String, dynamic> json) {
    return PerguntaModel(
      titulo: json['titulo'] as String?,
      tipo: json['tipo'] as String?,
      alternativas: (json['alternativas'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );
  }
}
