import 'package:mobx/mobx.dart';

import '../../../shared/models/job_parameter_model.dart';
import '../../../shared/models/responses/job_sugestoes_response_model.dart';

part 'jobs_filter_controller.g.dart';

class JobsFilterController = _JobsFilterBase with _$JobsFilterController;

abstract class _JobsFilterBase with Store {
  // @observable
  // bool _expandirPais = false;
  // bool get expandirPais => _expandirPais;

  // @observable
  // bool _expandirCidade = false;
  // bool get expandirCidade => _expandirCidade;

  // @observable
  // bool _expandirEstado = false;
  // bool get expandirEstado => _expandirEstado;

  // @observable
  // bool _expandirNivel = false;
  // bool get expandirNivel => _expandirNivel;

  // @observable
  // bool _expandirRegime = false;
  // bool get expandirRegime => _expandirRegime;

  // @observable
  // bool _expandirTipo = false;
  // bool get expandirTipo => _expandirTipo;

  // @action
  // setExpandirPais(bool value) => _expandirPais = value;

  // @action
  // setExpandirEstado(bool value) => _expandirEstado = value;

  // @action
  // setExpandirCidade(bool value) => _expandirCidade = value;

  // @action
  // setExpandirNivel(bool value) => _expandirNivel = value;

  // @action
  // setExpandirRegime(bool value) => _expandirRegime = value;

  // @action
  // setExpandirTipo(bool value) => _expandirTipo = value;

  @observable
  JobParameterModel parameters = JobParameterModel();

  @action
  void setParameters(FacetsModel facets) {
    List<String> nome(Iterable<FacetModel> data) {
      return data.map((e) => e.nome).toList();
    }

    List<String> selected(List<FacetModel> data) {
      return nome(data.where((item) => item.isSelected == true));
    }

    parameters = parameters.copyWith(
      pais: selected(facets.pais),
      uf: selected(facets.uf),
      cidade: selected(facets.cidade),
      nivel: selected(facets.nivel),
      regime: selected(facets.regime),
      pcd: isPcD,
      selecaoCega: isAsCegas,
      carregarFiltro: true,
      carregarLista: true,
    );
  }

  @observable
  bool? _pcd;
  bool? get isPcD => _pcd;

  @observable
  bool? _asCegas;
  bool? get isAsCegas => _asCegas;

  @action
  setPcD(bool? value) => _pcd = value;

  @action
  setAsCegas(bool? value) => _asCegas = value;
}
