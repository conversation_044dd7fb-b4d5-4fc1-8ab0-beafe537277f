import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:get/get.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

import '../../../shared/core/app_config.dart';
import '../../../shared/core/app_translation.i18n.dart';
import '../../../shared/core/app_utils.dart';
import '../../../shared/models/job_parameter_model.dart';
import '../../../shared/widgets/app_badge.dart';
import '../../../shared/widgets/app_image_network_widget.dart';
import '../../../shared/widgets/app_usuario_card/app_usuario_card_controller.dart';
import '../../../shared/widgets/banner_ad_widget.dart';
import '../../jobs/job_detail/widget/app_job_card.dart';
import '../tab_subscriptions/utils/header_aplications.dart';
import 'tab_jobs_controller.dart';

class TabJobsPage extends StatefulWidget {
  const TabJobsPage({super.key});

  @override
  State<TabJobsPage> createState() => _TabJobsPageState();
}

class _TabJobsPageState extends State<TabJobsPage> {
  final controller = Modular.get<TabJobsController>();
  final appUsuarioController = Modular.get<AppUsuarioCardController>();

  @override
  void initState() {
    super.initState();
    controller.reload();
  }

  JobParameterModel get filtros => controller.filtros;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Padding(
          padding: const EdgeInsets.only(left: 16.0),
          child: Image.asset(
            'lib/assets/images/logo-empregare-splash.png',
            scale: 1.8,
          ),
        ),
        elevation: 0.5,
        backgroundColor: AppConfig.white,
        actions: <Widget>[
          GetBuilder<AppUsuarioCardController>(
            init: appUsuarioController,
            builder: (_) => Padding(
              padding: const EdgeInsets.only(
                bottom: 6.0,
                top: 6.0,
                right: 26,
              ),
              child: ClipOval(
                child: AppImageNetworkWidget(
                  height: 45,
                  width: 45,
                  fit: BoxFit.cover,
                  appUsuarioController.foto ?? '',
                  errorImage: 'lib/assets/images/person-filled.png',
                  scale: 2,
                ),
              ),
            ),
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            const HeaderApplications(showMinhasVagas: false),
            Expanded(
              child: GetBuilder<TabJobsController>(
                init: controller,
                builder: (_) {
                  if (controller.loading && controller.jobs.isEmpty) {
                    return const ShimmerJobPage();
                  }

                  return Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 20),
                        alignment: Alignment.centerLeft,
                        child: SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Visibility(
                                visible: controller.filtros.q?.isNotEmpty ??
                                    controller.filtros.query?.isNotEmpty ??
                                    false,
                                child: AppBadge(
                                  title: controller.filtros.q ??
                                      controller.filtros.query ??
                                      '',
                                ),
                              ),
                              Visibility(
                                visible: controller.paises.isNotEmpty,
                                child: Row(
                                  children: controller.paises.map((e) {
                                    return AppBadge(
                                      title: e,
                                    );
                                  }).toList(),
                                ),
                              ),
                              Visibility(
                                visible: controller.cidades.isNotEmpty,
                                child: Row(
                                  children: controller.cidades.map((e) {
                                    return AppBadge(
                                      title: e,
                                    );
                                  }).toList(),
                                ),
                              ),
                              Visibility(
                                visible: controller.estados.isNotEmpty,
                                child: Row(
                                  children: controller.estados.map((e) {
                                    return AppBadge(
                                      title: e,
                                    );
                                  }).toList(),
                                ),
                              ),
                              Visibility(
                                visible: controller.niveis.isNotEmpty,
                                child: Row(
                                  children: controller.niveis.map((e) {
                                    return AppBadge(
                                      title: e,
                                    );
                                  }).toList(),
                                ),
                              ),
                              Visibility(
                                visible: controller.regimes.isNotEmpty,
                                child: Row(
                                  children: controller.regimes.map((e) {
                                    return AppBadge(
                                      title: e,
                                    );
                                  }).toList(),
                                ),
                              ),
                              Visibility(
                                visible: controller.isAsCegas ?? false,
                                child: const AppBadge(
                                  title: 'Seleção às Cegas',
                                ),
                              ),
                              Visibility(
                                visible: controller.isPcd ?? false,
                                child: const AppBadge(title: 'Somente PcD'),
                              ),
                              InkWell(
                                onTap: controller.filter,
                                borderRadius: BorderRadius.circular(500),
                                child: Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Text(
                                    'MAIS FILTROS'.i18n,
                                    style: const TextStyle(
                                      color: AppConfig.colorPrimary,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              )
                            ],
                          ),
                        ),
                      ),
                      Expanded(
                        child: ListView.separated(
                          physics: const BouncingScrollPhysics(),
                          itemBuilder: (_, i) {
                            if (controller.jobs.length > 2 &&
                                (i == 2 || i % 50 == 2)) {
                              return Column(
                                children: <Widget>[
                                  const SizedBox(height: 15),
                                  BannerAdWidget(
                                    adUnitId: AppConfig
                                        .getAdmobBannerListagemVagasId(),
                                    size: AdSize.largeBanner,
                                  ),
                                  const SizedBox(height: 15),
                                  AppJobCard(job: controller.jobs[i]),
                                ],
                              );
                            }

                            if (i < controller.jobs.length) {
                              return AppJobCard(job: controller.jobs[i]);
                            } else if (i > 1 &&
                                controller.filtros.pag <
                                    controller.totalPaginas!) {
                              if (!controller.loading) {
                                controller.filtros.nextPage();
                                WidgetsBinding.instance
                                    .addPostFrameCallback((_) {
                                  controller.load();
                                });
                              }
                              return const ShimmerJobPageItem();
                            }

                            if (controller.jobs.isEmpty) {
                              return const Center(
                                child: Text("Nenhuma vaga encontrada"),
                              );
                            }

                            return const SizedBox.shrink();
                          },
                          separatorBuilder: (_, i) => const Divider(height: 2),
                          itemCount: controller.jobs.length + 1,
                        ),
                      ),
                      Visibility(
                        visible: controller.jobs.length <= 2,
                        child: BannerAdWidget(
                          adUnitId: AppConfig.getAdmobBannerListagemVagasId(),
                          size: AdSize.fullBanner,
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
