import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:get_ip_address/get_ip_address.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

class GetVersionLib {
  String? platformVersion;
  String? projectVersion;
  String? projectCode;
  String? model;
  String? hardware;
  String? ip;

  Future<void> initialize() async {
    final deviceInfo = DeviceInfoPlugin();
    final packageInfo = await PackageInfo.fromPlatform();

    projectVersion = packageInfo.version;
    projectCode = packageInfo.buildNumber;

    try {
      ip = await IpAddress().getIpAddress();
    } catch (e, stack) {
      await Sentry.captureException(e, stackTrace: stack);
    }

    if (Platform.isAndroid) {
      var info = await deviceInfo.androidInfo;
      platformVersion = "Android ${info.version}";
      model = info.model;
      hardware = info.hardware;
    } else if (Platform.isIOS) {
      var info = await deviceInfo.iosInfo;
      platformVersion = "iOS ${info.systemVersion}";
      model = info.model;
      hardware = info.utsname.machine;
    }
  }

  @override
  String toString() => "$platformVersion|$projectVersion|$model|$hardware";
}
